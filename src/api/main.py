"""
FastAPI backend for BTC/USDT price prediction system
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, FileResponse
from pydantic import BaseModel
from typing import Dict, List, Optional, Any
import pandas as pd
import numpy as np
import logging
from pathlib import Path
from datetime import datetime, timedelta, timezone
import json

from src.data_processing.data_loader import DataLoader
from src.data_processing.preprocessor import DataPreprocessor, PreprocessingConfig
from src.models.model_trainer import ModelTrainer
from config import API_CONFIG, MODEL_CONFIG

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# UTC+7 timezone
UTC_PLUS_7 = timezone(timedelta(hours=7))

def get_current_time_utc7():
    """Get current time in UTC+7 timezone"""
    return datetime.now(UTC_PLUS_7).isoformat()

# Initialize FastAPI app
app = FastAPI(
    title="BTC/USDT Price Prediction API",
    description="Cryptocurrency price prediction system using machine learning",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Global variables for models and data
model_trainer = None
preprocessor = None
latest_data = None
original_data = None
last_update = None
cached_features = None  # Cache latest features for faster predictions
cached_current_price = None  # Cache current price

# Pydantic models for API
class PredictionRequest(BaseModel):
    model_name: Optional[str] = "NeuralNetwork"
    include_confidence: bool = True

class PredictionResponse(BaseModel):
    timestamp: str
    current_price: float
    predictions: Dict[str, float]
    confidence_intervals: Optional[Dict[str, Dict[str, float]]] = None
    model_info: Dict[str, Any]

class ModelInfo(BaseModel):
    name: str
    is_trained: bool
    training_date: Optional[str]
    performance_metrics: Dict[str, Dict[str, float]]

class HealthResponse(BaseModel):
    status: str
    timestamp: str
    models_loaded: int
    last_data_update: Optional[str]

# Initialize models and data on startup
@app.on_event("startup")
async def startup_event():
    """Initialize models and load data on startup"""
    global model_trainer, preprocessor, latest_data, last_update
    
    logger.info("Starting BTC/USDT Price Prediction API...")
    
    try:
        # Initialize model trainer
        model_trainer = ModelTrainer(models_dir=Path("models"))
        
        # Load trained models
        available_models = ['NeuralNetwork', 'RandomForest', 'GradientBoosting']
        for model_name in available_models:
            model_dir = Path("models") / model_name
            if model_dir.exists():
                try:
                    model = model_trainer.get_available_models()[model_name]
                    model.load_model(model_dir)
                    model_trainer.trained_models[model_name] = model
                    logger.info(f"Loaded {model_name} model")
                except Exception as e:
                    logger.error(f"Failed to load {model_name}: {e}")
        
        # Initialize preprocessor
        config = PreprocessingConfig(
            sequence_length=60,
            prediction_horizons=[4, 16, 24],
            features=['open', 'high', 'low', 'close', 'volume'],
            target='close',
            scaler_type='standard'
        )
        preprocessor = DataPreprocessor(config)
        
        # Load and preprocess latest data
        await update_data()
        
        logger.info("API startup completed successfully!")
        
    except Exception as e:
        logger.error(f"Failed to initialize API: {e}")
        raise

async def update_data():
    """Update latest data for predictions"""
    global latest_data, original_data, last_update, preprocessor, cached_features, cached_current_price

    try:
        logger.info("Updating latest data...")

        # Load recent data
        loader = DataLoader()
        df_1h = loader.load_timeframe_data('1h')

        # Use last 5000 records for preprocessing context
        recent_data = df_1h.tail(5000).copy()

        # Store original data (before preprocessing)
        original_data = recent_data.copy()

        # Preprocess data
        if preprocessor.is_fitted:
            processed_data = preprocessor.transform(recent_data)
        else:
            processed_data = preprocessor.fit_transform(recent_data)

        latest_data = processed_data
        last_update = get_current_time_utc7()

        # Cache latest features and current price for faster predictions
        cached_features = latest_data.tail(1)
        cached_current_price = float(original_data['close'].iloc[-1])

        logger.info(f"Data updated successfully. Latest data shape: {latest_data.shape}")

    except Exception as e:
        logger.error(f"Failed to update data: {e}")
        raise

@app.get("/", response_class=HTMLResponse)
async def root():
    """Serve the main dashboard"""
    return FileResponse("templates/index.html")

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    return HealthResponse(
        status="healthy",
        timestamp=get_current_time_utc7(),
        models_loaded=len(model_trainer.trained_models) if model_trainer else 0,
        last_data_update=last_update
    )

@app.get("/models", response_model=List[ModelInfo])
async def get_models():
    """Get information about available models"""
    if not model_trainer:
        raise HTTPException(status_code=503, detail="Models not initialized")

    models_info = []

    for model_name, model in model_trainer.trained_models.items():
        # Get performance metrics if available
        metrics = {}
        if model_name in model_trainer.evaluation_results:
            metrics = model_trainer.evaluation_results[model_name]

        model_info = ModelInfo(
            name=model_name,
            is_trained=model.is_trained,
            training_date=model.model_metadata.get('training_date'),
            performance_metrics=metrics
        )
        models_info.append(model_info)

    return models_info

@app.get("/training-info")
async def get_training_info():
    """Get detailed training data information"""
    try:
        from src.data_processing.data_loader import DataLoader

        loader = DataLoader()
        df = loader.load_timeframe_data('1h')

        # Get training data subset (last 20,000 records as used in training)
        training_data = df.tail(20000)

        training_info = {
            "dataset_info": {
                "total_records": len(df),
                "date_range": {
                    "start": df.index.min().strftime('%Y-%m-%d %H:%M:%S UTC'),
                    "end": df.index.max().strftime('%Y-%m-%d %H:%M:%S UTC')
                },
                "price_range": {
                    "start_price": float(df['close'].iloc[0]),
                    "latest_price": float(df['close'].iloc[-1])
                }
            },
            "training_data": {
                "records_used": len(training_data),
                "date_range": {
                    "start": training_data.index.min().strftime('%Y-%m-%d %H:%M:%S UTC'),
                    "end": training_data.index.max().strftime('%Y-%m-%d %H:%M:%S UTC')
                },
                "coverage_years": round((training_data.index.max() - training_data.index.min()).days / 365.25, 1)
            },
            "models": []
        }

        # Add model training dates
        if model_trainer:
            for model_name, model in model_trainer.trained_models.items():
                training_date = model.model_metadata.get('training_date')
                if training_date:
                    # Convert to UTC+7 for display
                    from datetime import datetime
                    dt = datetime.fromisoformat(training_date.replace('Z', '+00:00'))
                    utc7_dt = dt.replace(tzinfo=UTC_PLUS_7)

                    training_info["models"].append({
                        "name": model_name,
                        "training_date": utc7_dt.strftime('%Y-%m-%d %H:%M:%S UTC+7'),
                        "feature_count": len(model.feature_columns) if hasattr(model, 'feature_columns') else 0
                    })

        return training_info

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get training info: {str(e)}")

@app.post("/predict", response_model=PredictionResponse)
async def predict_price(request: PredictionRequest):
    """Get price predictions"""
    if not model_trainer or latest_data is None:
        raise HTTPException(status_code=503, detail="Service not ready")

    model_name = request.model_name
    if model_name not in model_trainer.trained_models:
        raise HTTPException(status_code=404, detail=f"Model {model_name} not found")

    try:
        # Get the model
        model = model_trainer.trained_models[model_name]

        # Use cached features for faster predictions
        features_to_use = cached_features if cached_features is not None else latest_data.tail(1)
        current_price = cached_current_price if cached_current_price is not None else float(original_data['close'].iloc[-1])

        # Make predictions
        predictions = model.predict(features_to_use)

        # Convert predictions to regular floats and inverse transform to original scale
        pred_dict = {}
        for horizon, pred_array in predictions.items():
            # Inverse transform the prediction back to original price scale
            try:
                unscaled_pred = preprocessor.inverse_transform_target(pred_array, 'close')
                pred_dict[horizon] = float(unscaled_pred[0])
            except:
                # Fallback: use the prediction as is
                pred_dict[horizon] = float(pred_array[0])
        
        # Calculate confidence intervals (simplified approach)
        confidence_intervals = None
        if request.include_confidence:
            confidence_intervals = {}
            for horizon in pred_dict.keys():
                # Use model's validation metrics to estimate confidence
                if model_name in model_trainer.evaluation_results:
                    metrics = model_trainer.evaluation_results[model_name].get(horizon, {})
                    rmse = metrics.get('rmse', 1000)
                    
                    # Simple confidence interval: prediction ± 1.96 * RMSE (95% CI)
                    pred_value = pred_dict[horizon]
                    margin = 1.96 * rmse
                    
                    confidence_intervals[horizon] = {
                        'lower': pred_value - margin,
                        'upper': pred_value + margin,
                        'margin': margin
                    }
        
        # Get model info
        model_info = {
            'name': model_name,
            'training_date': model.model_metadata.get('training_date'),
            'feature_count': len(model.feature_columns),
            'prediction_horizons': model.prediction_horizons
        }
        
        return PredictionResponse(
            timestamp=get_current_time_utc7(),
            current_price=current_price,
            predictions=pred_dict,
            confidence_intervals=confidence_intervals,
            model_info=model_info
        )
        
    except Exception as e:
        logger.error(f"Prediction failed: {e}")
        raise HTTPException(status_code=500, detail=f"Prediction failed: {str(e)}")

@app.get("/current-price")
async def get_current_price():
    """Get current BTC/USDT price"""
    if original_data is None:
        raise HTTPException(status_code=503, detail="Data not available")

    try:
        current_price = float(original_data['close'].iloc[-1])
        timestamp = original_data.index[-1].isoformat()

        return {
            "price": current_price,
            "timestamp": timestamp,
            "last_update": last_update
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get current price: {str(e)}")

@app.get("/historical-data")
async def get_historical_data(hours: int = 168):  # Default 1 week
    """Get historical price data"""
    if original_data is None:
        raise HTTPException(status_code=503, detail="Data not available")

    try:
        # Get last N hours of data from original (unscaled) data
        historical = original_data.tail(hours)

        data = []
        for idx, row in historical.iterrows():
            data.append({
                "timestamp": idx.isoformat(),
                "open": float(row['open']),
                "high": float(row['high']),
                "low": float(row['low']),
                "close": float(row['close']),
                "volume": float(row['volume'])
            })

        return {
            "data": data,
            "count": len(data),
            "timeframe": "1h"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get historical data: {str(e)}")

@app.post("/update-data")
async def trigger_data_update(background_tasks: BackgroundTasks):
    """Trigger data update"""
    background_tasks.add_task(update_data)
    return {"message": "Data update triggered"}

@app.get("/model-performance/{model_name}")
async def get_model_performance(model_name: str):
    """Get detailed model performance metrics"""
    if not model_trainer or model_name not in model_trainer.trained_models:
        raise HTTPException(status_code=404, detail=f"Model {model_name} not found")
    
    try:
        model = model_trainer.trained_models[model_name]
        
        # Get model info
        model_info = model.get_model_info()
        
        # Get evaluation results if available
        evaluation_results = {}
        if model_name in model_trainer.evaluation_results:
            evaluation_results = model_trainer.evaluation_results[model_name]
        
        return {
            "model_info": model_info,
            "evaluation_results": evaluation_results,
            "feature_importance": model.get_feature_importance() if hasattr(model, 'get_feature_importance') else {}
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get model performance: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host=API_CONFIG["host"],
        port=API_CONFIG["port"],
        reload=API_CONFIG["debug"]
    )
