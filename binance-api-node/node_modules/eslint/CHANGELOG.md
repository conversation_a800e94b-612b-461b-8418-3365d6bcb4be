v6.8.0 - December 20, 2019

* [`c5c7086`](https://github.com/eslint/eslint/commit/c5c708666b450fb69522a55aa375626f9297dc6f) Fix: ignore aligning single line in key-spacing (fixes #11414) (#12652) (Yeon<PERSON>uan)
* [`9986d9e`](https://github.com/eslint/eslint/commit/9986d9e0baed0d3586bbee472fe2fae2ed625f5d) Chore: add object option test cases in yield-star-spacing (#12679) (YeonJuan)
* [`1713d07`](https://github.com/eslint/eslint/commit/1713d0758b083f3840d724505f997a7cb20ff384) New: Add no-error-on-unmatched-pattern flag (fixes #10587) (#12377) (ncraley)
* [`5c25a26`](https://github.com/eslint/eslint/commit/5c25a26608fbd9a1d0127c9a3653609aa4b63e86) Update: autofix bug in lines-between-class-members (fixes #12391) (#12632) (Yeon<PERSON><PERSON>)
* [`4b3cc5c`](https://github.com/eslint/eslint/commit/4b3cc5cd2459f04eae149faea0651785d7f9db0b) Chore: enable prefer-regex-literals in eslint codebase (#12268) (薛定谔的猫)
* [`05faebb`](https://github.com/eslint/eslint/commit/05faebb943456ad2b20117f3c8b3eccbe2e2fb03) Update: improve suggestion testing experience (#12602) (Brad Zacher)
* [`05f7dd5`](https://github.com/eslint/eslint/commit/05f7dd53ed91a6e3be9eb40825fb6d2207f82209) Update: Add suggestions for no-unsafe-negation (fixes #12591) (#12609) (Milos Djermanovic)
* [`d3e43f1`](https://github.com/eslint/eslint/commit/d3e43f1c10c5e19f40e7b3d3944b87f1b0c9c075) Docs: Update no-multi-assign explanation (#12615) (Yuping Zuo)
* [`272e4db`](https://github.com/eslint/eslint/commit/272e4db6074283bc01cc6ec72c9e396bb3c110e6) Fix: no-multiple-empty-lines: Adjust reported `loc` (#12594) (Tobias Bieniek)
* [`a258039`](https://github.com/eslint/eslint/commit/a258039e556075d7d1f955a79d094ea103ec165a) Fix: no-restricted-imports schema allows multiple paths/patterns objects (#12639) (Milos Djermanovic)
* [`51f9620`](https://github.com/eslint/eslint/commit/51f9620cc55cc091fe38dbe68e4633de06297b8c) Fix: improve report location for array-bracket-spacing (#12653) (Milos Djermanovic)
* [`45364af`](https://github.com/eslint/eslint/commit/45364afc9c7f0251348cd1a7a13656c3816435d7) Fix: prefer-numeric-literals doesn't check types of literal arguments (#12655) (Milos Djermanovic)
* [`e3c570e`](https://github.com/eslint/eslint/commit/e3c570eaf3d1d44fb57bf42f1870887856e4c5a0) Docs: Add example for expression option (#12694) (Arnaud Barré)
* [`6b774ef`](https://github.com/eslint/eslint/commit/6b774ef0d849ccf5c1127b25e1fe7c3e438d586b) Docs: Add spacing in comments for no-console rule (#12696) (Nikki Nikkhoui)
* [`7171fca`](https://github.com/eslint/eslint/commit/7171fca6ef4e0e8f267658fc7d8f603f00eddd84) Chore: refactor regex in config comment parser (#12662) (Milos Djermanovic)
* [`1600648`](https://github.com/eslint/eslint/commit/1600648d2880ffb1e9e414b31ff0f66ead7167f9) Update: Allow $schema in config (#12612) (Yordis Prieto)
* [`acc0e47`](https://github.com/eslint/eslint/commit/acc0e47572a9390292b4e313b4a4bf360d236358) Update: support .eslintrc.cjs (refs eslint/rfcs#43) (#12321) (Evan Plaice)
* [`49c1658`](https://github.com/eslint/eslint/commit/49c1658544ace24b9aaaa301af0fc07a2ef3bf30) Chore: remove bundling of ESLint during release (#12676) (Kai Cataldo)
* [`257f3d6`](https://github.com/eslint/eslint/commit/257f3d67905a52bf8602a5a5707c893cc90d7ca7) Chore: complete to move to GitHub Actions (#12625) (Toru Nagashima)
* [`ab912f0`](https://github.com/eslint/eslint/commit/ab912f0ef709a916ab9a27ea09d9d7adf046fb2d) Docs: 1tbs with allowSingleLine edge cases (refs #12284) (#12314) (Ari Kardasis)
* [`dd1c30e`](https://github.com/eslint/eslint/commit/dd1c30e35f05ed332e2abbd3d4d53635efde74b8) Sponsors: Sync README with website (ESLint Jenkins)
* [`a230f84`](https://github.com/eslint/eslint/commit/a230f8404e4f2423dd79378b065d24c12776775b) Update: include node version in cache (#12582) (Eric Wang)
* [`8b65f17`](https://github.com/eslint/eslint/commit/8b65f175dfb4fac11ed7184537be400ed14996fb) Chore: remove references to parser demo (#12644) (Kai Cataldo)
* [`e9cef99`](https://github.com/eslint/eslint/commit/e9cef99e6ebec1faefdb576ca597e81ae4f04afd) Docs: wrap {{}} in raw liquid tags to prevent interpolation (#12643) (Kai Cataldo)
* [`e707453`](https://github.com/eslint/eslint/commit/e70745325ff9e085acc6843dd8bfae5550645d4f) Docs: Fix configuration example in no-restricted-imports (fixes #11717) (#12638) (Milos Djermanovic)
* [`19194ce`](https://github.com/eslint/eslint/commit/19194cec724e016df02376bbeae31171be6f0bdf) Chore: Add tests to cover default object options in comma-dangle (#12627) (YeonJuan)
* [`6e36d12`](https://github.com/eslint/eslint/commit/6e36d12d95e76022172fd0ec8a5e85c22fde6a8a) Update: do not recommend require-atomic-updates (refs #11899) (#12599) (Kai Cataldo)

v6.7.2 - November 30, 2019

* [`bc435a9`](https://github.com/eslint/eslint/commit/bc435a93afd6ba4def1b53993ef7cf8220f3f070) Fix: isSpaceBetweenTokens() recognizes spaces in JSXText (fixes #12614) (#12616) (Toru Nagashima)
* [`4928d51`](https://github.com/eslint/eslint/commit/4928d513b4fe716c7ed958c294a10ef8517be25e) Fix: don't ignore the entry directory (fixes #12604) (#12607) (Toru Nagashima)
* [`b41677a`](https://github.com/eslint/eslint/commit/b41677ae2a143790b19b0e70391a46ec6c8f5de1) Docs: Clarify suggestion's data in Working with Rules (refs #12606) (#12617) (Milos Djermanovic)
* [`ea16de4`](https://github.com/eslint/eslint/commit/ea16de4e7c6f661398b0b7843f95e5f307c89551) Fix: Support tagged template literal generics in no-unexpected-multiline (#11698) (Brad Zacher)
* [`fa6415d`](https://github.com/eslint/eslint/commit/fa6415d5b877370374a6a530a5190ab5a411b4dc) Sponsors: Sync README with website (ESLint Jenkins)
* [`e1e158b`](https://github.com/eslint/eslint/commit/e1e158b4d7bd61e812723b378d2c391295da43a5) Sponsors: Sync README with website (ESLint Jenkins)

v6.7.1 - November 24, 2019

* [`dd1e9f4`](https://github.com/eslint/eslint/commit/dd1e9f4df2103c43509a54b0ad5f9106557997f9) Fix: revert changes to key-spacing due to regression (#12598) (Kai Cataldo)
* [`c644b54`](https://github.com/eslint/eslint/commit/c644b5429e5bc8a050afd70c99ec82035eb611fa) Docs: Update README team and sponsors (ESLint Jenkins)

v6.7.0 - November 22, 2019

* [`312a88f`](https://github.com/eslint/eslint/commit/312a88f2230082d898b7d8d82f8af63cb352e55a) New: Add grouped-accessor-pairs rule (fixes #12277) (#12331) (Milos Djermanovic)
* [`5c68f5f`](https://github.com/eslint/eslint/commit/5c68f5feeb4a6c0cb53ff76b2fd255b5bfa69c93) Update: Add 'lexicalBindings' to no-implicit-globals and change messages (#11996) (Milos Djermanovic)
* [`6eaad96`](https://github.com/eslint/eslint/commit/6eaad964ff159d0a38de96c1104782ffe6858c78) New: Add suggestions API (#12384) (Will Douglas)
* [`b336fbe`](https://github.com/eslint/eslint/commit/b336fbedecd85731611fdc2dfd8edb635a8b1c39) Fix: indent rule with JSX spread props (#12581) (Nathan Woltman)
* [`97c745d`](https://github.com/eslint/eslint/commit/97c745dc277febbea82552a4d9186e3df847f860) Update: Report assignment expression location in no-cond-assign (#12465) (Milos Djermanovic)
* [`0f01f3d`](https://github.com/eslint/eslint/commit/0f01f3d0807c580631c2fdcff29192a64a870637) Update: Check member expressions with `this` in operator-assignment (#12495) (Milos Djermanovic)
* [`62c7038`](https://github.com/eslint/eslint/commit/62c7038a493d89e4a7b14ac673a063d09d04057b) Fix: invalid token checking in computed-property-spacing (fixes #12198) (#12533) (YeonJuan)
* [`4f8a1ee`](https://github.com/eslint/eslint/commit/4f8a1ee1c26ccb5882e5e83ea7eab2f406c7476b) Update: Add enforceForClassMembers option to no-useless-computed-key (#12110) (ark120202)
* [`1a2eb99`](https://github.com/eslint/eslint/commit/1a2eb99f11c65813bba11d6576a06cff2b823cc9) New: new rule no-constructor-return (fixes #12481) (#12529) (Pig Fang)
* [`ca3b2a6`](https://github.com/eslint/eslint/commit/ca3b2a62c9e829dc4534bca3643d6bc729b46df0) New: ignorePatterns in config files (refs eslint/rfcs#22) (#12274) (Toru Nagashima)
* [`60204a3`](https://github.com/eslint/eslint/commit/60204a3620e33a078c1c35fa2e5d839a16c627ff) Docs: Added another Textmate 2 bundle. (#12580) (Ryan Fitzer)
* [`62623f9`](https://github.com/eslint/eslint/commit/62623f9f611a3adb79696304760a2fd14be8afbc) Fix: preserve whitespace in multiline-comment-style (fixes #12312) (#12316) (Kai Cataldo)
* [`17a8849`](https://github.com/eslint/eslint/commit/17a8849491a983f6cb8e98da8c0c9d52ff5f2aa6) New: Add no-dupe-else-if rule (fixes #12469) (#12504) (Milos Djermanovic)
* [`41a78fd`](https://github.com/eslint/eslint/commit/41a78fd7ce245cad8ff6a96c42f5840688849427) Update: improve location for semi and comma-dangle (#12380) (Chiawen Chen)
* [`0a480f8`](https://github.com/eslint/eslint/commit/0a480f8307a0e438032f484254941e6426748143) Docs: Change "Code Conventions" link in pull-requests.md (#12401) (Denis Sikuler)
* [`fed20bb`](https://github.com/eslint/eslint/commit/fed20bb039cf9f53adfcf93e467f418c5e958f45) Fix: require-await crash on global await (#12571) (Brad Zacher)
* [`b8030fc`](https://github.com/eslint/eslint/commit/b8030fc23e88f57a04d955b3befd1ab0fc2c5d10) Update: deprecate personal config (fixes #11914, refs eslint/rfcs#32) (#12426) (Toru Nagashima)
* [`40c8c32`](https://github.com/eslint/eslint/commit/40c8c3264c7c383d98c9faf9c4cb4f8b75aee40f) Fix: improve report location for object-curly-spacing (#12563) (Milos Djermanovic)
* [`1110045`](https://github.com/eslint/eslint/commit/1110045e0d28a461e75d2f57d5f01533d59ef239) Fix: ignore marker-only comments in spaced-comment (fixes #12036) (#12558) (Milos Djermanovic)
* [`6503cb8`](https://github.com/eslint/eslint/commit/6503cb8d99e549fece53b80b110e890a7978b9fd) Update: Fix uglified object align in key-spacing (fixes #11414) (#12472) (YeonJuan)
* [`40791af`](https://github.com/eslint/eslint/commit/40791af69efde1701690637603ad37d41e15a727) Docs: clarify ignoreDestructuring option in the camelcase rule (#12553) (Milos Djermanovic)
* [`07d398d`](https://github.com/eslint/eslint/commit/07d398d91d5b6d0247e58b1f8ea64bb5acd570a8) Chore: Add GitHub organization to Sponsor button (#12562) (Brandon Mills)
* [`a477707`](https://github.com/eslint/eslint/commit/a47770706ac59633dcd73e886d1a7282b324ee06) Chore: Format style guide links so they can be clicked (#12189) (Ivan V)
* [`0f7edef`](https://github.com/eslint/eslint/commit/0f7edefdc1576d5e3e7ef89083002b0a4a31f039) Update: add react plugin config for eslint init (#12446) (Ibrahim Rouis)
* [`448ff1e`](https://github.com/eslint/eslint/commit/448ff1e53734c503fb9e7e6802c1c7e441d4c019) Update: Report '\08' and '\09' in no-octal-escape (fixes #12080) (#12526) (Milos Djermanovic)
* [`45aa6a3`](https://github.com/eslint/eslint/commit/45aa6a3ba3486f1b116c5daab6432d144e5ea574) New: Add no-setter-return rule (fixes #12285) (#12346) (Milos Djermanovic)
* [`0afb518`](https://github.com/eslint/eslint/commit/0afb518d1f139376245613dddd8eaef32b52d619) Fix: invalid autofix in function-call-argument-newline (fixes #12454) (#12539) (YeonJuan)
* [`90305e0`](https://github.com/eslint/eslint/commit/90305e017c2c5fba0b4b62f41b180910b4baeedb) Update: Depcrecate isSpaceBetweenTokens() (#12519) (Kai Cataldo)
* [`41b1e43`](https://github.com/eslint/eslint/commit/41b1e4308c1cb01c8b00cc8adc36440e77854117) New: add option for camelcase (fixes #12527) (#12528) (Pig Fang)
* [`f49f1e0`](https://github.com/eslint/eslint/commit/f49f1e0a69afa49f6548af7b2c0e6347e1ea022d) Upgrade: upgrade optionator to avoid license issue (fixes #11536) (#12537) (Pig Fang)
* [`0286b57`](https://github.com/eslint/eslint/commit/0286b5730501b391c74e069db46849f0de0885d2) Docs: Clean up Getting Started Guide (#12544) (Nicholas C. Zakas)
* [`575a98d`](https://github.com/eslint/eslint/commit/575a98d724b2688f1e9c83744c5dc9ffe9a7bfb4) Chore: Add funding field to package.json (#12543) (Nicholas C. Zakas)
* [`9e29e18`](https://github.com/eslint/eslint/commit/9e29e189752f06362fd1956659e07834efb746a5) Fix: sourceCode#isSpaceBetweenTokens() checks non-adjacent tokens (#12491) (Kai Cataldo)
* [`5868550`](https://github.com/eslint/eslint/commit/586855060afb3201f4752be8820dc85703b523a6) Docs: add notice about `function` keyword in keyword-spacing (#12524) (Pig Fang)
* [`bb556d5`](https://github.com/eslint/eslint/commit/bb556d5fd735ad2dcea322082edcc07a58105ce9) Fix: curly `multi` reports single lexical declarations (fixes #11908) (#12513) (Milos Djermanovic)
* [`ac60621`](https://github.com/eslint/eslint/commit/ac606217d4beebc35b865d14a7f9723fd21faa48) Fix: unexpected autofix in prefer-const (fixes #12514) (#12521) (YeonJuan)
* [`990065e`](https://github.com/eslint/eslint/commit/990065e5f58b6cc6922ab6cee5b97bfc56a6237a) Update: curly multi-or-nest flagging semis on next line (fixes #12370) (#12378) (cherryblossom000)
* [`084a8a6`](https://github.com/eslint/eslint/commit/084a8a63a749232681fefe9bdac6802efdcdc8a8) Fix: no-cond-assign with `always` option reports switch case clauses (#12470) (Milos Djermanovic)
* [`7e41355`](https://github.com/eslint/eslint/commit/7e41355b19a8ef347620dd7c0dde491c3460937b) Update: improve report location for space-infix-ops (#12324) (Chiawen Chen)
* [`94ff921`](https://github.com/eslint/eslint/commit/94ff921689115f856578159564ee1968b4b914be) Update: Add capIsConstructor option to no-invalid-this (fixes #12271) (#12308) (Milos Djermanovic)
* [`de65de6`](https://github.com/eslint/eslint/commit/de65de6e488112a602949e6a5d27dd4c754b003c) New: Add prefer-exponentiation-operator rule (fixes #10482) (#12360) (Milos Djermanovic)
* [`c78f4a7`](https://github.com/eslint/eslint/commit/c78f4a73de68f81cd41132b46d4840b91599d599) Update: Allow JSX exception in no-inline-comments (fixes #11270) (#12388) (Milos Djermanovic)
* [`e17fb90`](https://github.com/eslint/eslint/commit/e17fb90f5817d16081e690eb06b7720afcb9fa2a) New: allowAfterThisConstructor for no-underscore-dangle (fixes #11488) (#11489) (sripberger)
* [`287ca56`](https://github.com/eslint/eslint/commit/287ca562811d037bde09a47af7f5b9c7b741e022) Build: update CI for Node.js 13 (#12496) (Toru Nagashima)
* [`98e1d50`](https://github.com/eslint/eslint/commit/98e1d50273f31c2a7b59772298280ed7305274c8) Upgrade: globals to v12.1.0 (#12296) (Tony Brix)
* [`8ac71a3`](https://github.com/eslint/eslint/commit/8ac71a3c89a9db13706a44b23d1b509b65185113) Sponsors: Sync README with website (ESLint Jenkins)
* [`4e142ea`](https://github.com/eslint/eslint/commit/4e142ea411dfb692b6e2a69cd5f1204ade4dd58a) Docs: Update README team and sponsors (ESLint Jenkins)

v6.6.0 - October 25, 2019

* [`39dfe08`](https://github.com/eslint/eslint/commit/39dfe0880fa934e287e8ea1f7b56d5cba8d43765) Update: false positives in function-call-argument-newline (fixes #12123) (#12280) (Scott O'Hara)
* [`4d84210`](https://github.com/eslint/eslint/commit/4d842105c9c82026be668d7425213138903d4d41) Update: improve report location for no-trailing-spaces (fixes #12315) (#12477) (Milos Djermanovic)
* [`c6a7745`](https://github.com/eslint/eslint/commit/c6a7745a1371a85932bfae5fec039d1b6fcfc128) Update: no-trailing-spaces false negatives after comments (fixes #12479) (#12480) (Milos Djermanovic)
* [`0bffe95`](https://github.com/eslint/eslint/commit/0bffe953d2752dd2d3045f2f8771c96b6cee8fc4) Fix: no-misleading-character-class crash on invalid regex (fixes #12169) (#12347) (Milos Djermanovic)
* [`c6a9a3b`](https://github.com/eslint/eslint/commit/c6a9a3bc58b69dbf9be9cd09b0283c081ca211e7) Update: Add enforceForIndexOf option to use-isnan (fixes #12207) (#12379) (Milos Djermanovic)
* [`364877b`](https://github.com/eslint/eslint/commit/364877b2504e8f7ece04770b93d517e2f27458d0) Update: measure plugin loading time and output in debug message (#12395) (Victor Homyakov)
* [`1744fab`](https://github.com/eslint/eslint/commit/1744faba3c93c869f7dbbf0a704d32e2692d6856) Fix: operator-assignment removes and duplicates comments (#12485) (Milos Djermanovic)
* [`52ca11a`](https://github.com/eslint/eslint/commit/52ca11a66ab6c2fb5a71d8b9869482f14f98cb9d) Fix: operator-assignment invalid autofix with adjacent tokens (#12483) (Milos Djermanovic)
* [`0f6d0dc`](https://github.com/eslint/eslint/commit/0f6d0dcdf5adc30079a7379bbf605a4ef3887a85) Fix: CLIEngine#addPlugin reset lastConfigArrays (fixes #12425) (#12468) (Toru Nagashima)
* [`923a8cb`](https://github.com/eslint/eslint/commit/923a8cb752b8dee1e622c5fd36f3f53288e30602) Chore: Fix lint failure in JSDoc comment (#12489) (Brandon Mills)
* [`aac3be4`](https://github.com/eslint/eslint/commit/aac3be435cccc241781150fcac728df04d086fa8) Update: Add ignored prop regex no-param-reassign (#11275) (Luke Bennett)
* [`e5382d6`](https://github.com/eslint/eslint/commit/e5382d6e4eb1344f537b6f107535269e9939fcb8) Chore: Remove unused parameter in dot-location (#12464) (Milos Djermanovic)
* [`49faefb`](https://github.com/eslint/eslint/commit/49faefbee3fc7daaf2482d9d7d23513d6ffda9e8) Fix: no-obj-calls false positive (fixes #12437) (#12467) (Toru Nagashima)
* [`b3dbd96`](https://github.com/eslint/eslint/commit/b3dbd9657bbeac6571111a4429b03fc085ba6655) Fix: problematic installation issue (fixes #11018) (#12309) (Toru Nagashima)
* [`cd7c29b`](https://github.com/eslint/eslint/commit/cd7c29b17085c14c9cf6345201c72a192c0d0e0c) Sponsors: Sync README with website (ESLint Jenkins)
* [`8233873`](https://github.com/eslint/eslint/commit/8233873b8e5facd80ab7b172bff1e896a9c5fd39) Docs: Add note about Node.js requiring SSL support (fixes #11413) (#12475) (Nicholas C. Zakas)
* [`89e8aaf`](https://github.com/eslint/eslint/commit/89e8aafcc622a4763bed6b9d62f148ef95798f38) Fix: improve report location for no-tabs (#12471) (Milos Djermanovic)
* [`7dffe48`](https://github.com/eslint/eslint/commit/7dffe482d646d4e5f94fa87a22f3b5b2e0a4b189) Update: Enable function string option in comma-dangle (fixes #12058) (#12462) (YeonJuan)
* [`e15e1f9`](https://github.com/eslint/eslint/commit/e15e1f933f287d274a726e7f0f0a1dd80f0964af) Docs: fix doc for no-unneeded-ternary rule (fixes #12098) (#12410) (Sam Rae)
* [`b1dc58f`](https://github.com/eslint/eslint/commit/b1dc58f0a717cb3d19300c845ca23a21ceb610d3) Sponsors: Sync README with website (ESLint Jenkins)
* [`61749c9`](https://github.com/eslint/eslint/commit/61749c94bd8a2ebcdfb89e0cd48c4a029a945079) Chore: Provide debug log for parser errors (#12474) (Brad Zacher)
* [`7c8bbe0`](https://github.com/eslint/eslint/commit/7c8bbe0391944e1f92e083a04715bf4b3fe6be5d) Update: enforceForOrderingRelations no-unsafe-negation (fixes #12163) (#12414) (Sam Rae)
* [`349ed67`](https://github.com/eslint/eslint/commit/349ed6700e1155384597e1e6035550a96cb8a42d) Update: improve report location for no-mixed-operators (#12328) (Chiawen Chen)
* [`a102eaa`](https://github.com/eslint/eslint/commit/a102eaa9ac19e1c6d92f79a4033e9048cfb64c0d) Fix: prefer-numeric-literals invalid autofix with adjacent tokens (#12387) (Milos Djermanovic)
* [`6e7c18d`](https://github.com/eslint/eslint/commit/6e7c18ddb30b32ee5b2e842cc8258aa7aebb7445) Update: enforceForNewInMemberExpressions no-extra-parens (fixes #12428) (#12436) (Milos Djermanovic)
* [`51fbbd7`](https://github.com/eslint/eslint/commit/51fbbd78f98f223d17071650f5117d07f60dadc2) Fix: array-bracket-newline consistent error with comments (fixes #12416) (#12441) (Milos Djermanovic)
* [`e657d4c`](https://github.com/eslint/eslint/commit/e657d4ccb9f3dd5cacceaaa40ffe24ac29a1349a) Fix: report full dot location in dot-location (#12452) (Milos Djermanovic)
* [`2d6e345`](https://github.com/eslint/eslint/commit/2d6e345e3c2626b0f2252f22cfaffdf53ea0871a) Update: make isSpaceBetweenTokens() ignore newline in comments (#12407) (YeonJuan)
* [`84f71de`](https://github.com/eslint/eslint/commit/84f71de0e686e0fe37b83d6728ce1825caaa44fb) Update: remove default overrides in keyword-spacing (fixes #12369) (#12411) (YeonJuan)
* [`18a0b0e`](https://github.com/eslint/eslint/commit/18a0b0e3df927428a22b5b5295f9faee4bd57246) Update: improve report location for no-space-in-parens (#12364) (Chiawen Chen)
* [`d61c8a5`](https://github.com/eslint/eslint/commit/d61c8a5a75447a36276f2d4f84afb3e1129618da) Update: improve report location for no-multi-spaces (#12329) (Chiawen Chen)
* [`561093f`](https://github.com/eslint/eslint/commit/561093fc4267a4ae317d63bc9f103020fad88802) Upgrade: bump inquirer to ^7.0.0 (#12440) (Joe Graham)
* [`fb633b2`](https://github.com/eslint/eslint/commit/fb633b2bbd0a390b247047524fdd1f612dbab803) Chore: Add a script for testing with more control (#12444) (Eric Wang)
* [`012ec51`](https://github.com/eslint/eslint/commit/012ec5151113a2be06fc0e4cd208d714e52dbc57) Sponsors: Sync README with website (ESLint Jenkins)
* [`874fe16`](https://github.com/eslint/eslint/commit/874fe1642a10a0fb937ccccdd4d22343b84f80dc) New: pass cwd from cli engine (#12389) (Eric Wang)
* [`b962775`](https://github.com/eslint/eslint/commit/b962775b8cb7c90985a5ab63e56744bb2ba79644) Update: no-self-assign should detect member expression with this (#12279) (Tibor Blenessy)
* [`02977f2`](https://github.com/eslint/eslint/commit/02977f25a922dd0b8617c16116bb4364d0f30e94) Docs: Clarify `eslint:recommended` semver policy (#12429) (Kevin Partington)
* [`97045ae`](https://github.com/eslint/eslint/commit/97045ae0805e6503887eef0b131dcb9e70b6d185) Docs: Fixes object type for `rules` in "Use a Plugin" (#12409) (Daisy Develops)
* [`24ca088`](https://github.com/eslint/eslint/commit/24ca088fdc901feef8f10b050414fbde64b55c7d) Docs: Fix typo in v6 migration guide (#12412) (Benjamim Sonntag)
* [`b094008`](https://github.com/eslint/eslint/commit/b094008fb196dc1de5b4c27b7dbf0bcbb4b7b352) Chore: update version parameter name (#12402) (Toru Nagashima)
* [`e5637ba`](https://github.com/eslint/eslint/commit/e5637badd42f087d115f81575b832097fe6fe554) Chore: enable jsdoc/require-description (#12365) (Kai Cataldo)
* [`d31f337`](https://github.com/eslint/eslint/commit/d31f3370396ec4868722bdc044aa697b135ac183) Sponsors: Sync README with website (ESLint Jenkins)
* [`7ffb22f`](https://github.com/eslint/eslint/commit/7ffb22f61cf1622511a7fe42b5ead7c3b216df5e) Chore: Clean up inline directive parsing (#12375) (Jordan Eldredge)
* [`84467c0`](https://github.com/eslint/eslint/commit/84467c07461cc47ee43807ba9014e13700473c5c) Docs: fix wrong max-depth example (fixes #11991) (#12358) (Gabriel R Sezefredo)
* [`3642342`](https://github.com/eslint/eslint/commit/364234262efabd91fa8bd53161d9d3e1e37e7944) Docs: Fix minor formatting/grammar errors (#12371) (cherryblossom000)
* [`c47fa0d`](https://github.com/eslint/eslint/commit/c47fa0dfc76211b3b0e5649c63acdd9606ce0eca) Docs: Fix missing word in sentence (#12361) (Dan Boulet)
* [`8108f49`](https://github.com/eslint/eslint/commit/8108f49f9fa0c2de80b3b66c847551beff585951) Chore: enable additional eslint-plugin-jsdoc rules (#12336) (Kai Cataldo)
* [`b718d2e`](https://github.com/eslint/eslint/commit/b718d2e6c9fe3fc56aa7cfc68b1a40b5cd8a7c01) Chore: update issue template with --eslint-fix flag (#12352) (James George)
* [`20ba14d`](https://github.com/eslint/eslint/commit/20ba14dc78fc2654b2920d14877dde21c6c10da4) Sponsors: Sync README with website (ESLint Jenkins)
* [`566a947`](https://github.com/eslint/eslint/commit/566a947f67c8038a50e204d68723519778a78a0f) Sponsors: Sync README with website (ESLint Jenkins)
* [`070cbd0`](https://github.com/eslint/eslint/commit/070cbd0a2ec07831962a25c4276d08e097302416) Sponsors: Sync README with website (ESLint Jenkins)

v6.5.1 - September 30, 2019

* [`0d3d7d9`](https://github.com/eslint/eslint/commit/0d3d7d9cdd83a7f0e035c95f716a91b9ecc4868b) Docs: fix typo in no-magic-numbers (#12345) (Josiah Rooney)
* [`447ac87`](https://github.com/eslint/eslint/commit/447ac877e8ca2858d61b1e983f72d39e3e2ca74d) Fix: no-useless-rename handles ExperimentalRestProperty (fixes #12335) (#12339) (Kai Cataldo)
* [`b6ff73c`](https://github.com/eslint/eslint/commit/b6ff73cad13282fbfc91186cf4bc2f20278a8936) Sponsors: Sync README with website (ESLint Jenkins)

v6.5.0 - September 29, 2019

* [`73596cb`](https://github.com/eslint/eslint/commit/73596cbdf0a12e2878b2994783f9b969b0c5fbeb) Update: Add enforceForSwitchCase option to use-isnan (#12106) (Milos Djermanovic)
* [`d592a24`](https://github.com/eslint/eslint/commit/d592a248d67920f7200925c003f10853d29f1f8d) Fix: exclude `\u000d` so new line won't convert to text (fixes #12027) (#12031) (zamboney)
* [`e85d27a`](https://github.com/eslint/eslint/commit/e85d27af427d6185ac553a0d801b5103153426d4) Fix: no-regex-spaces false positives and invalid autofix (fixes #12226) (#12231) (Milos Djermanovic)
* [`b349bf7`](https://github.com/eslint/eslint/commit/b349bf79ad56dded826bc99cb52c3551af34fa63) Fix: prefer-named-capture-group incorrect locations (fixes #12233) (#12247) (Milos Djermanovic)
* [`7dc1ea9`](https://github.com/eslint/eslint/commit/7dc1ea9a1b9a21daaffcf712ba9c0e91af81b906) Fix: no-useless-return autofix removes comments (#12292) (Milos Djermanovic)
* [`0e68677`](https://github.com/eslint/eslint/commit/0e68677ec0aaf060a071ecf71e4af954dddb6af0) Fix: no-extra-bind autofix removes comments (#12293) (Milos Djermanovic)
* [`6ad7e86`](https://github.com/eslint/eslint/commit/6ad7e864303e56a39c89569d50c6caf80752ee21) Fix: no-extra-label autofix removes comments (#12298) (Milos Djermanovic)
* [`acec201`](https://github.com/eslint/eslint/commit/acec201f06df780791179ad92cfc484f9b6d23d4) Fix: no-undef-init autofix removes comments (#12299) (Milos Djermanovic)
* [`d89390b`](https://github.com/eslint/eslint/commit/d89390b75e3e9993f347387a49b0ac5550f45c7f) Fix: use async reading of stdin in bin/eslint.js (fixes #12212) (#12230) (Barrie Treloar)
* [`334ca7c`](https://github.com/eslint/eslint/commit/334ca7c8b9c18ac097849c1cefaa43097a4e51dc) Update: no-useless-rename also reports default values (fixes #12301) (#12322) (Kai Cataldo)
* [`41bfe91`](https://github.com/eslint/eslint/commit/41bfe919c06932b7e58cd9ead20157e06656160a) Update: Fix handling of chained new expressions in new-parens (#12303) (Milos Djermanovic)
* [`160b7c4`](https://github.com/eslint/eslint/commit/160b7c46b556ccb6023eb411a8be8801a4bda6df) Chore: add autofix npm script (#12330) (Kai Cataldo)
* [`04b6adb`](https://github.com/eslint/eslint/commit/04b6adb7f1bcb2b6cb3fa377b1ca4cecd810630e) Chore: enable eslint-plugin-jsdoc (refs #11146) (#12332) (Kai Cataldo)
* [`9b86167`](https://github.com/eslint/eslint/commit/9b86167e6f053e4a72bf68ebc79db53903f7f8c3) Docs: Add new ES environments to Configuring ESLint (#12289) (Milos Djermanovic)
* [`c9aeab2`](https://github.com/eslint/eslint/commit/c9aeab21a71c6743f51163b7a8fdf4f0cbfcdbde) Docs: Add supported ECMAScript version to README (#12290) (Milos Djermanovic)
* [`8316e7b`](https://github.com/eslint/eslint/commit/8316e7be5a9429513d7ecf2ee2afc40ab4415b8f) Fix: no-useless-rename autofix removes comments (#12300) (Milos Djermanovic)
* [`29c12f1`](https://github.com/eslint/eslint/commit/29c12f18726a3afb21fc89ab1bdacc6972d49e68) Chore: cache results in runtime-info (#12320) (Kai Cataldo)
* [`f5537b2`](https://github.com/eslint/eslint/commit/f5537b2ed0b0b5e51a34c22cdd4ebfd024eaea3d) Fix: prefer-numeric-literals autofix removes comments (#12313) (Milos Djermanovic)
* [`11ae6fc`](https://github.com/eslint/eslint/commit/11ae6fcb5d5503e5dea41c02780369efe51f0bb9) Update: Fix call, new and member expressions in no-extra-parens (#12302) (Milos Djermanovic)
* [`a7894eb`](https://github.com/eslint/eslint/commit/a7894ebb43523152d36720efa770bb1fe8b58c07) New: add --env-info flag to CLI (#12270) (Kai Cataldo)
* [`61392ff`](https://github.com/eslint/eslint/commit/61392ff5ec660bfc01ac2ff0e9660d259cf88fd6) Sponsors: Sync README with website (ESLint Jenkins)
* [`2c6bf8e`](https://github.com/eslint/eslint/commit/2c6bf8ea9c8a8f94746f980bd5bea0a8c5c4d6b7) Docs: English fix (#12306) (Daniel Nixon)
* [`6f11877`](https://github.com/eslint/eslint/commit/6f118778366613fc53036cb6a7537e1b4c6e7af8) Sponsors: Sync README with website (ESLint Jenkins)
* [`2e202ca`](https://github.com/eslint/eslint/commit/2e202ca2228846e6226aa8dd99c614d572fb86a8) Docs: fix links in array-callback-return (#12288) (Milos Djermanovic)
* [`e39c631`](https://github.com/eslint/eslint/commit/e39c6318af0fd27edd5fd2aaf2b24a3e204005dd) Docs: add example for CLIEngine#executeOnText 3rd arg (#12286) (Kai Cataldo)
* [`d4f9a16`](https://github.com/eslint/eslint/commit/d4f9a16af7e00021e2ed63823d9c2f149bc985d6) Update: add support for JSXFragments in indent rule (fixes #12208) (#12210) (Kai Cataldo)
* [`c6af95f`](https://github.com/eslint/eslint/commit/c6af95f5bf1ef10f08545d54fd52b98e85fdf7f7) Sponsors: Sync README with website (ESLint Jenkins)
* [`8cadd52`](https://github.com/eslint/eslint/commit/8cadd5229b7372aed0d4785dcae15532a399bf55) Sponsors: Sync README with website (ESLint Jenkins)
* [`f9fc695`](https://github.com/eslint/eslint/commit/f9fc695d77c19cd5ecb3f0e97e1ea124c8543409) Chore: enable default-param-last (#12244) (薛定谔的猫)
* [`9984c3e`](https://github.com/eslint/eslint/commit/9984c3e27c92de76b8c05a58525dbcea12b10b83) Docs: Update README team and sponsors (ESLint Jenkins)

v6.4.0 - September 13, 2019

* [`e915fff`](https://github.com/eslint/eslint/commit/e915fffb6089a23ff1cae926cc607f9b87dc1819) Docs: Improve examples and clarify default option (#12067) (Yuping Zuo)
* [`540296f`](https://github.com/eslint/eslint/commit/540296fcecd232a09dc873a5a22f5839b59b7842) Update: enforceForClassMembers option to accessor-pairs (fixes #12063) (#12192) (Milos Djermanovic)
* [`d3c2334`](https://github.com/eslint/eslint/commit/d3c2334646eae9287d5be9e457d041e445efb512) Update: flag nested block with declaration as error (#12193) (David Waller)
* [`b2498d2`](https://github.com/eslint/eslint/commit/b2498d284b9c30ed1543429c2f45d9014e12fe22) Update: Fix handling of property names in no-self-assign (#12105) (Milos Djermanovic)
* [`1ee61b0`](https://github.com/eslint/eslint/commit/1ee61b06715fcc750be2c923034a1e59ba663287) Update: enforceForClassMembers computed-property-spacing (fixes #12049) (#12214) (Milos Djermanovic)
* [`520c922`](https://github.com/eslint/eslint/commit/520c92270eed6e90c1a796e8af275980f01705e0) Docs: Added naming convention details to plugin usage (#12202) (Henrique Barcelos)
* [`f826eab`](https://github.com/eslint/eslint/commit/f826eabbeecddb047f58f4e7308a14c18148d369) Fix: Allow line comment exception in object-curly-spacing (fixes #11902) (#12216) (Milos Djermanovic)
* [`db2a29b`](https://github.com/eslint/eslint/commit/db2a29beb0fa28183f65bf9e659c66c03a8918b5) Update: indentation of comment followed by semicolon (fixes #12232) (#12243) (Kai Cataldo)
* [`ae17d1c`](https://github.com/eslint/eslint/commit/ae17d1ca59dd466aa64da0680ec2453c2dc3b80d) Fix: no-sequences is reporting incorrect locations (#12241) (Milos Djermanovic)
* [`365331a`](https://github.com/eslint/eslint/commit/365331a42e22af5a77ac9cfa9673d6a8f653eb5a) Fix: object-shorthand providing invalid fixes for typescript (#12260) (Brad Zacher)
* [`1c921c6`](https://github.com/eslint/eslint/commit/1c921c6dfd7ddfb0308c8103e53d32c1241475f0) New: add no-import-assign (fixes #12237) (#12252) (Toru Nagashima)
* [`3be04fd`](https://github.com/eslint/eslint/commit/3be04fd6a4e7b3f5a5ecb845a29cf29b71fe2dfb) New: Add prefer-regex-literals rule (fixes #12238) (#12254) (Milos Djermanovic)
* [`37c0fde`](https://github.com/eslint/eslint/commit/37c0fdeb87b92a0b779b125adf45535b79b65757) Update: Report global Atomics calls in no-obj-calls (fixes #12234) (#12258) (Milos Djermanovic)
* [`985c9e5`](https://github.com/eslint/eslint/commit/985c9e5eba351965a8a1491a41dbdcc78154b8f4) Fix: space-before-function-paren autofix removes comments (fixes #12259) (#12264) (Milos Djermanovic)
* [`01da7d0`](https://github.com/eslint/eslint/commit/01da7d04c4e5a7376cf241ec02db7971726a1bf9) Fix: eqeqeq rule reports incorrect locations (#12265) (Milos Djermanovic)
* [`319e4d8`](https://github.com/eslint/eslint/commit/319e4d8386ea846928f0f906c251b46043a53491) Docs: adding finally example (#12256) (Jens Melgaard)
* [`d52328f`](https://github.com/eslint/eslint/commit/d52328f012f3704c7d1ce39427e63f80531c7979) Docs: fix no-sequences `with` examples (#12239) (Milos Djermanovic)
* [`a41fdc0`](https://github.com/eslint/eslint/commit/a41fdc07404a7675d14183fab245fb8f49dcb858) Fix: Remove autofixer for no-unsafe-negation (#12157) (Milos Djermanovic)
* [`e38f5fd`](https://github.com/eslint/eslint/commit/e38f5fdfc786363a3eae642f1a69a8725600aa61) Update: fix no-octal-escape false negatives after \0 (#12079) (Milos Djermanovic)
* [`9418fbe`](https://github.com/eslint/eslint/commit/9418fbe0eb31cace3debe27b620709628df2fad7) Sponsors: Sync README with website (ESLint Jenkins)
* [`acc5ec5`](https://github.com/eslint/eslint/commit/acc5ec5082aed466a29899f651e6767b39155aec) Sponsors: Sync README with website (ESLint Jenkins)
* [`460c5ad`](https://github.com/eslint/eslint/commit/460c5ad176eaf39ff579cd96b3bcbe0539093f8f) Sponsors: Sync README with website (ESLint Jenkins)
* [`0313441`](https://github.com/eslint/eslint/commit/0313441d016c8aa0674c135f9da67a676e766ec5) New: add rule default-param-last (fixes #11361) (#12188) (Chiawen Chen)
* [`7621f5d`](https://github.com/eslint/eslint/commit/7621f5d2aa7d87e798b75ca47d6889c280597e99) Update: add more specific linting messages to space-in-parens (#11121) (Che Fisher)
* [`21eb904`](https://github.com/eslint/eslint/commit/21eb9044135c01b6c12188517bba840614483fc6) Fix: basePath of OverrideTester (fixes #12032) (#12205) (Toru Nagashima)
* [`86e5e65`](https://github.com/eslint/eslint/commit/86e5e657ea3fbf12b10524abcbc197afd215a060) Sponsors: Sync README with website (ESLint Jenkins)
* [`2b1a13f`](https://github.com/eslint/eslint/commit/2b1a13fa0de8360586857f3ced8da514c971297d) Fix: no-extra-boolean-cast reports wrong negation node (fixes #11324) (#12197) (Milos Djermanovic)
* [`ba8c2aa`](https://github.com/eslint/eslint/commit/ba8c2aa0154561fbeca33db0343cb39a7fbd9b4f) Sponsors: Sync README with website (ESLint Jenkins)
* [`a0a9746`](https://github.com/eslint/eslint/commit/a0a9746724ccd22c721ddc1b25c566aa9acea154) Docs: Fix link in no-irregular-whitespace.md (#12196) (Timo Tijhof)
* [`e10eeba`](https://github.com/eslint/eslint/commit/e10eebab4abd193dee697c4de7fb2d95bbab2d8c) Fix: quotes autofix produces syntax error with octal escape sequences (#12118) (Milos Djermanovic)

v6.3.0 - August 30, 2019

* [`0acdefb`](https://github.com/eslint/eslint/commit/0acdefb97f35bb09db2910540c70dc377a01ad62) Chore: refactor code (#12113) (James George)
* [`52e2cf5`](https://github.com/eslint/eslint/commit/52e2cf50b35d57fb8466e0bcd0581eff1590fb4c) New: reportUnusedDisableDirectives in config (refs eslint/rfcs#22) (#12151) (Toru Nagashima)
* [`020f952`](https://github.com/eslint/eslint/commit/020f9526b618a191566acea3e17e20815d484c58) Update: enforceForSequenceExpressions to no-extra-parens (fixes #11916) (#12142) (Milos Djermanovic)
* [`aab1b84`](https://github.com/eslint/eslint/commit/aab1b840f9cffb2a76a5c9fe1852961be71dc184) Fix: reset to the default color (#12174) (Ricardo Gobbo de Souza)
* [`4009d39`](https://github.com/eslint/eslint/commit/4009d39aa59451510aa24911e758d664f216289a) Fix: yoda rule produces invalid autofix with preceding yield (#12166) (Milos Djermanovic)
* [`febb660`](https://github.com/eslint/eslint/commit/febb6605d350c936d64cb73e694482cfbb20b29c) Fix: no-extra-boolean-cast invalid autofix with yield before negation (#12164) (Milos Djermanovic)
* [`4c0b70b`](https://github.com/eslint/eslint/commit/4c0b70b869c16647f7af6de9d5c5479fc19f49db) New: support TypeScript at config initializer (fixes #11789) (#12172) (Pig Fang)
* [`94e39d9`](https://github.com/eslint/eslint/commit/94e39d9f782f45db86a079e07508d63040118ef1) Chore: use GitHub Actions (#12144) (Toru Nagashima)
* [`e88f305`](https://github.com/eslint/eslint/commit/e88f305df9d454868624c559fd93b981a680c215) Chore: support es2020 in fuzz (#12180) (薛定谔的猫)
* [`00d2c5b`](https://github.com/eslint/eslint/commit/00d2c5be9a89efd90135c4368a9589f33df3f7ba) Docs: corrected class extension example (#12176) (Marius M)
* [`31e5428`](https://github.com/eslint/eslint/commit/31e542819967b2aa1191e1abaa1c4a49fddbe3cf) Chore: Fix wrong error object keys in test files (#12162) (Milos Djermanovic)
* [`197f443`](https://github.com/eslint/eslint/commit/197f4432fca70a574028e5568c48afad12213224) Fix: func-name-matching crash on descriptor-like arguments (#12100) (Milos Djermanovic)
* [`644ce33`](https://github.com/eslint/eslint/commit/644ce3306748a33b74fc6a94be0267c2c9f19348) Fix: no-self-assign false positive with rest and spread in array (#12099) (Milos Djermanovic)
* [`a81d263`](https://github.com/eslint/eslint/commit/a81d2636ce41fb34d6826c2e9857814e11cb9c30) Fix: fix message of function-paren-newline (#12136) (Pig Fang)
* [`77f8ed1`](https://github.com/eslint/eslint/commit/77f8ed1ad9656c526217ce54a6717fa232d522c8) Chore: update blogpost template (#12154) (Toru Nagashima)
* [`6abc7b7`](https://github.com/eslint/eslint/commit/6abc7b72dfb824a372379708ca39340b2c7abc03) Docs: Document the exception in no-unsafe-negation (#12161) (Milos Djermanovic)

v6.2.2 - August 23, 2019

* [`0e0b784`](https://github.com/eslint/eslint/commit/0e0b784b922051c2a1d39dd8160382114b645800) Upgrade: espree@^6.1.1 (#12158) (Kevin Partington)
* [`04e859f`](https://github.com/eslint/eslint/commit/04e859f228d081efd3af6edb22563dbc775f8d1d) Sponsors: Sync README with website (ESLint Jenkins)
* [`34783d1`](https://github.com/eslint/eslint/commit/34783d10ff9b58a3c1e39a36e10864caeb9f66e0) Sponsors: Sync README with website (ESLint Jenkins)
* [`b809e72`](https://github.com/eslint/eslint/commit/b809e72221bc658e5a42bfd4b8723d3771571f9e) Docs: Update README team and sponsors (ESLint Jenkins)

v6.2.1 - August 20, 2019

* [`8c021b5`](https://github.com/eslint/eslint/commit/8c021b5917b3aa3c578ffe3972106d0a6bcf0838) Upgrade: eslint-utils 1.4.2 (#12131) (Toru Nagashima)
* [`e82388b`](https://github.com/eslint/eslint/commit/e82388bd87717430200ec554634cc08806e38d3c) Sponsors: Sync README with website (ESLint Jenkins)
* [`4aeeeed`](https://github.com/eslint/eslint/commit/4aeeeedb656ee3519ea82ebf0cb41ca801215046) Docs: update docs for ecmaVersion 2020 (#12120) (silverwind)
* [`6886148`](https://github.com/eslint/eslint/commit/6886148d1f528659ec3e125f61ef7a5f4c67556d) Docs: Add duplicate keys limitation to accessor-pairs (#12124) (Milos Djermanovic)

v6.2.0 - August 18, 2019

* [`fee6acb`](https://github.com/eslint/eslint/commit/fee6acbe13cecd4c028e681e185fc6a6d6ba9452) Update: support bigint and dynamic import (refs #11803) (#11983) (Toru Nagashima)
* [`afd8012`](https://github.com/eslint/eslint/commit/afd8012c2797f2f5bf3c360cb241ea2ba6e1a489) New: noInlineConfig setting (refs eslint/rfcs#22) (#12091) (Toru Nagashima)
* [`3d12378`](https://github.com/eslint/eslint/commit/3d12378221961439c27ddae0ecda9845ac575107) Update: Fix accessor-pairs to enforce pairs per property in literals (#12062) (Milos Djermanovic)
* [`8cd00b3`](https://github.com/eslint/eslint/commit/8cd00b308987e0db0bdb2e242bf13b2b07b350bd) New: function-call-argument-newline (#12024) (finico)
* [`30ebf92`](https://github.com/eslint/eslint/commit/30ebf929f60684520b1201c1adfd86214c19d614) Fix: prefer-template autofix produces syntax error with octal escapes (#12085) (Milos Djermanovic)
* [`13c3988`](https://github.com/eslint/eslint/commit/13c3988a4001ae368ea7b6c8d3dd0abfa7c6cf64) Fix: Check literal type explicitly in dot-notation (#12095) (Milos Djermanovic)
* [`3e5ceca`](https://github.com/eslint/eslint/commit/3e5ceca4d2284b55a2292a1d3de9aa4cdf6fa213) Fix: Handle empty string property names in getFunctionNameWithKind (#12104) (Milos Djermanovic)
* [`9a043ff`](https://github.com/eslint/eslint/commit/9a043ffbb864fc65baeb16fe5668435e3b7cfe34) Fix: no-duplicate-case false positives on Object.prototype keys (#12107) (Milos Djermanovic)
* [`fe631af`](https://github.com/eslint/eslint/commit/fe631afee59641876598d19b1935967099cc6fa0) Chore: minor typo fix (#12112) (James George)
* [`4cb7877`](https://github.com/eslint/eslint/commit/4cb78774f6cc687a3c8701462f8c7f7b587ecaf0) Fix: fix no-extra-parens ignores some nodes (#11909) (Pig Fang)
* [`2dc23b8`](https://github.com/eslint/eslint/commit/2dc23b81e54defbce7a70a7f26c2e4c7b692cf58) Update: fix no-dupe-keys false negatives on empty string names (#12069) (Milos Djermanovic)
* [`19ab666`](https://github.com/eslint/eslint/commit/19ab6666e8e4142a183bdee2be96e5bafbac0e21) Fix: yoda exceptRange false positives on empty string property names (#12071) (Milos Djermanovic)
* [`d642150`](https://github.com/eslint/eslint/commit/d642150fe016608e71a1df2a72960e915b3cfbad) Update: Check empty string property names in sort-keys (#12073) (Milos Djermanovic)
* [`acce6de`](https://github.com/eslint/eslint/commit/acce6de940e2b089ff5ba59e4518a54af1682d5e) Fix: class-methods-use-this reports 'undefined' names (#12103) (Milos Djermanovic)
* [`92ec2cb`](https://github.com/eslint/eslint/commit/92ec2cb1731b7b6e0ac66336d583fbb782504290) Fix: Allow bind call with a single spread element in no-extra-bind (#12088) (Milos Djermanovic)
* [`bfdb0c9`](https://github.com/eslint/eslint/commit/bfdb0c97003fc0e045aa6ed10b177c35305a6e46) Fix: no-extra-boolean-cast invalid autofix for Boolean() without args (#12076) (Milos Djermanovic)
* [`34ccc0c`](https://github.com/eslint/eslint/commit/34ccc0cd81f495190e585c6efa8ae233d45bd3ed) Chore: Remove TDZ scope type condition from no-unused-vars (#12055) (Milos Djermanovic)
* [`01d38ce`](https://github.com/eslint/eslint/commit/01d38ce2faf0abbc9dd5d25694baeee131036165) Docs: Remove TDZ scope from the scope manager interface documentation (#12054) (Milos Djermanovic)
* [`1aff8fc`](https://github.com/eslint/eslint/commit/1aff8fc4f9394cd9126654a55f7f3a43ab1cf8f0) Update: warn about mixing ternary and logical operators (fixes #11704) (#12001) (Karthik Priyadarshan)
* [`11be2f8`](https://github.com/eslint/eslint/commit/11be2f8513bd61499f6247392a33ac0a26901c90) Docs: do not recommend global-installed usage (#12016) (薛定谔的猫)
* [`cf31dab`](https://github.com/eslint/eslint/commit/cf31dab5d5982151e0cfcc32879e69a83180ec70) Fix: no-restricted-syntax - correct the schema (#12051) (Brad Zacher)
* [`fbec99e`](https://github.com/eslint/eslint/commit/fbec99ea3e39316791685652c66e522d698f52d8) Update: fix class-methods-use-this false negatives with exceptMethods (#12077) (Milos Djermanovic)
* [`fb08b7c`](https://github.com/eslint/eslint/commit/fb08b7c9d28bc68864eb940e26df274059228b6a) Docs: Remove readonly/writable global logic from no-undef (fixes #11963) (#12053) (Milos Djermanovic)
* [`5b5934b`](https://github.com/eslint/eslint/commit/5b5934b9513f9114f5bf8e12ff4f4981590d64d3) Sponsors: Sync README with website (ESLint Jenkins)
* [`9156760`](https://github.com/eslint/eslint/commit/915676022a100ae5dba788fa3329d34b3c1f18d3) Sponsors: Sync README with website (ESLint Jenkins)
* [`f5e0cc4`](https://github.com/eslint/eslint/commit/f5e0cc40795f175692acb05daaadb91e9e5ae5d3) Update: Check computed method keys in no-extra-parens (#11973) (Milos Djermanovic)
* [`d961438`](https://github.com/eslint/eslint/commit/d9614388df8cfb977842ed7ac4725d76a3e05df3) Docs: Fix Incorrect Documentation (#12045) (Michael Miceli)
* [`887d08c`](https://github.com/eslint/eslint/commit/887d08c244e32f1fc18359e63380e2cdb0cb3797) Sponsors: Sync README with website (ESLint Jenkins)
* [`d90183f`](https://github.com/eslint/eslint/commit/d90183ff6757cff854f4ca4d25b835143dfb4b21) Docs: add a case to func-names (#12038) (Chiawen Chen)
* [`8a5b62d`](https://github.com/eslint/eslint/commit/8a5b62de2ae574f416c0f8ad91205da9b1837275) Docs: no use eslint.linter in code example (#12037) (薛定谔的猫)
* [`5831767`](https://github.com/eslint/eslint/commit/58317673210e48be3975e317c2c566fae155c94f) Update: report location of func-names (fixes #12022) (#12028) (Pig Fang)

v6.1.0 - July 20, 2019

* [`8f86cca`](https://github.com/eslint/eslint/commit/8f86ccaa89daf10123370868c5dcb48c1fcbef7d) Upgrade: eslint-scope@^5.0.0 (#12011) (Kevin Partington)
* [`d08683e`](https://github.com/eslint/eslint/commit/d08683e3c807f92daf266894093c70f8d5ac6afa) Fix: glob processing (fixes #11940) (#11986) (Toru Nagashima)
* [`bfcf8b2`](https://github.com/eslint/eslint/commit/bfcf8b21011466b570b536ca31ec10fd228b3dca) Fix: dot-location errors with parenthesized objects (fixes #11868) (#11933) (Milos Djermanovic)
* [`79e8d09`](https://github.com/eslint/eslint/commit/79e8d099bbbebfa4d804484eeeeea9c074ede870) Fix: add parens for sequence expr in arrow-body-style (fixes #11917) (#11918) (Pig Fang)
* [`105c098`](https://github.com/eslint/eslint/commit/105c098f3cece8b83ab8d1566b8ea41dd94a60b9) Docs: update docs for object-curly-spacing (fixes #11634) (#12009) (Chiawen Chen)
* [`c90a12c`](https://github.com/eslint/eslint/commit/c90a12c283698befcafd2c86f8bd8942428fe80b) Chore: update release script for new website repo (#12006) (Kai Cataldo)
* [`e2c08a9`](https://github.com/eslint/eslint/commit/e2c08a9c8d86238955ecc8fd5a626584ee91eba5) Sponsors: Sync README with website (ESLint Jenkins)
* [`b974fcb`](https://github.com/eslint/eslint/commit/b974fcbd3321ab382a914520018d4c051b2e5c62) Update: Check computed property keys in no-extra-parens (#11952) (Milos Djermanovic)
* [`222d27c`](https://github.com/eslint/eslint/commit/222d27c32a6d6d8828233b3b99e93ecefa94c603) Update: Add for-in and for-of checks for props in no-param-reassign (#11941) (Milos Djermanovic)
* [`e4c450f`](https://github.com/eslint/eslint/commit/e4c450febc9bd77b33f6473667afa9f955be6b71) Fix: no-extra-parens autofix with `in` in a for-loop init (fixes #11706) (#11848) (Milos Djermanovic)
* [`2dafe2d`](https://github.com/eslint/eslint/commit/2dafe2d288d1e0d353bb938d12a5da888091cfdb) Fix: prefer-const produces invalid autofix (fixes #11699) (#11827) (Milos Djermanovic)
* [`cb475fd`](https://github.com/eslint/eslint/commit/cb475fd8c0bbfcb00340459966b6780f39ea87a7) Fix: Cache file error handling on read-only file system. (fixes #11945) (#11946) (Cuki)
* [`89412c3`](https://github.com/eslint/eslint/commit/89412c3cbc52e556dba590fa94e10bf40faf1fdf) Docs: Fixed a typo (fixes #11999) (#12000) (Eddie Olson)
* [`6669f78`](https://github.com/eslint/eslint/commit/6669f78a3dd305aef6191e7eea24fae2ae4fd2e8) Fix: --init with Vue.js failed (fixes #11970) (#11985) (Toru Nagashima)
* [`93633c2`](https://github.com/eslint/eslint/commit/93633c2b3716b17816bcb3dc221c49b75db41317) Upgrade: Upgrade lodash dependency (fixes #11992) (#11994) (Cyd La Luz)
* [`776dae7`](https://github.com/eslint/eslint/commit/776dae71f2f5c7b5f0650ea3c277eca26e324e41) Docs: fix wrong Node.js version in getting started (#11993) (Toru Nagashima)
* [`4448261`](https://github.com/eslint/eslint/commit/4448261f5d217d8a06eb0ef898401928b54a34e3) Docs: some typos and optimization points (#11960) (Jason Lee)
* [`2a10856`](https://github.com/eslint/eslint/commit/2a10856d1ed5880a09a5ba452bd80d49c1be4e6c) Chore: Add temporary test files to .gitignore (#11978) (Milos Djermanovic)
* [`d83b233`](https://github.com/eslint/eslint/commit/d83b23382de3b80056a7e6330ed5846316c94147) Chore: update path for release bundles (#11977) (Kai Cataldo)
* [`1fb3620`](https://github.com/eslint/eslint/commit/1fb362093a65b99456a11029967d9ee0c31fd697) Fix: creating of enabledGlobals object without prototype (fixes #11929) (#11935) (finico)
* [`c2f2db9`](https://github.com/eslint/eslint/commit/c2f2db97c6d6a415b78ee7b3e8924853d465e757) Docs: Replace global true and false with writable and readonly in rules (#11956) (Milos Djermanovic)
* [`19335b8`](https://github.com/eslint/eslint/commit/19335b8f47029b2f742d5507ba39484eaf68d07b) Fix: actual messageId and expected messageId are switched in rule tester (#11928) (Milos Djermanovic)
* [`8b216e0`](https://github.com/eslint/eslint/commit/8b216e04fb0dd0a1a4d3730ebe4b24780020b09c) Docs: Fix incorrect example comments for unicode-bom rule (fixes #11937) (#11938) (Brandon Yeager)
* [`cc3885b`](https://github.com/eslint/eslint/commit/cc3885b028e29ebc575c900f43af81cb0dabffb6) Chore: add v8-compile-cache to speed up instantiation time (#11921) (薛定谔的猫)
* [`d8f2688`](https://github.com/eslint/eslint/commit/d8f26886f19a17f2e1cdcb91e2db84fc7ba3fdfb) Upgrade: deps (#11904) (薛定谔的猫)
* [`e5f1ccc`](https://github.com/eslint/eslint/commit/e5f1ccc9e2d07ad0acf149027ffc382021d54da1) Docs: add 'stricter rule config validating' in migrating docs (#11905) (薛定谔的猫)

v6.0.1 - June 24, 2019

* [`b5bde06`](https://github.com/eslint/eslint/commit/b5bde0669bd6a7a6b8e38cdf204d8d4b932cea63) Fix: --rulesdir option didn't work (fixes #11888) (#11890) (Toru Nagashima)
* [`13f0418`](https://github.com/eslint/eslint/commit/13f041897ee31982808a57b4d06450b57c9b27dc) Fix: improve error message on --print-config (fixes #11874) (#11885) (Toru Nagashima)
* [`056c2aa`](https://github.com/eslint/eslint/commit/056c2aaf39a5f8d06de24f06946dda95032a0361) Fix: improve diagnostics for shareable-config-missing errors (#11880) (Teddy Katz)
* [`566b7aa`](https://github.com/eslint/eslint/commit/566b7aa5d61fb31cd47fe4da7820b07cf9bde1ec) Docs: Update no-confusing-arrow with the new default option (#11886) (Yuping Zuo)
* [`d07f3fa`](https://github.com/eslint/eslint/commit/d07f3fae19b901c30cf4998f10722cb3182bd237) Fix: CLIEngine#getRules() contains plugin rules (fixes #11871) (#11872) (Toru Nagashima)
* [`21f4a80`](https://github.com/eslint/eslint/commit/21f4a8057ccc941f72bb617ae3b5c135a774f6c0) Docs: Fix inconsistent linking in migration guide (#11881) (Teddy Katz)
* [`f3a0774`](https://github.com/eslint/eslint/commit/f3a0774a8879b08777a4aedc76677f13d5156242) Docs: Fix typo in 6.0.0 migration guide (#11870) (Kevin Partington)

v6.0.0 - June 21, 2019

* [`81aa06b`](https://github.com/eslint/eslint/commit/81aa06b4cc49e9c15234a2c4d27659a03fea53d8) Upgrade: espree@6.0.0 (#11869) (Teddy Katz)
* [`5f022bc`](https://github.com/eslint/eslint/commit/5f022bc91d0d93d140876ceb1ee4e08b1b7cfd49) Fix: no-else-return autofix produces name collisions (fixes #11069) (#11867) (Milos Djermanovic)
* [`ded9548`](https://github.com/eslint/eslint/commit/ded9548d881b15e771ca79b844e8159601f30f70) Fix: multiline-comment-style incorrect message (#11864) (golopot)
* [`cad074d`](https://github.com/eslint/eslint/commit/cad074d4ddb34a59183b5965ca50170713b5a711) Docs: Add JSHint W047 compat to no-floating-decimal (#11861) (Timo Tijhof)
* [`41f6304`](https://github.com/eslint/eslint/commit/41f6304ce641a82ee729251b448dceb9fb0d501d) Upgrade: sinon (#11855) (Toru Nagashima)
* [`167ce87`](https://github.com/eslint/eslint/commit/167ce87e908ec04b0d3d79960278d45c883c4285) Chore: remove unuseable profile command (#11854) (Toru Nagashima)
* [`c844c6f`](https://github.com/eslint/eslint/commit/c844c6f2ff314cfa8c6ca0e35a1ef58b7e297b79) Fix: max-len properly ignore trailing comments (fixes #11838) (#11841) (ZYSzys)
* [`1b5661a`](https://github.com/eslint/eslint/commit/1b5661ae467c227c0239e06cc1466480004aa799) Fix: no-var should not fix variables named 'let' (fixes #11830) (#11832) (Milos Djermanovic)
* [`4d75956`](https://github.com/eslint/eslint/commit/4d75956147b6fd662ee90eb21d3f762816463b88) Build: CI with Azure Pipelines (#11845) (Toru Nagashima)
* [`1db3462`](https://github.com/eslint/eslint/commit/1db346220889305a427b45a00afcf362b81b3767) Chore: rm superfluous argument & fix perf-multifiles-targets (#11834) (薛定谔的猫)
* [`c57a4a4`](https://github.com/eslint/eslint/commit/c57a4a4a993193c4208c6419df331a7bc644a536) Upgrade: @babel/polyfill => core-js v3 (#11833) (薛定谔的猫)
* [`65faa04`](https://github.com/eslint/eslint/commit/65faa04e8b42eecd4505111bbff296951179f033) Docs: Clarify prefer-destructuring array/object difference (fixes #9970) (#11851) (Oliver Sieweke)
* [`81c3823`](https://github.com/eslint/eslint/commit/81c382378923a45015bafe58362f6c8faa5c3d5f) Fix: require-atomic-updates reports parameters (fixes #11723) (#11774) (Toru Nagashima)
* [`aef8ea1`](https://github.com/eslint/eslint/commit/aef8ea1a44b9f13d468f48536c4c93f79f201d9b) Sponsors: Sync README with website (ESLint Jenkins)

v6.0.0-rc.0 - June 9, 2019

* [`f403b07`](https://github.com/eslint/eslint/commit/f403b07283f91f1285d8318d6acea851dd765755) Update: introduce minKeys option to sort-keys rule (fixes #11624) (#11625) (Christian)
* [`87451f4`](https://github.com/eslint/eslint/commit/87451f4779bc4c0ec874042b6854920f947ee258) Fix: no-octal should report NonOctalDecimalIntegerLiteral (fixes #11794) (#11805) (Milos Djermanovic)
* [`e4ab053`](https://github.com/eslint/eslint/commit/e4ab0531c4e44c23494c6a802aa2329d15ac90e5) Update: support "bigint" in valid-typeof rule (#11802) (Colin Ihrig)
* [`e0fafc8`](https://github.com/eslint/eslint/commit/e0fafc8ef59a80a6137f4170bbe46582d6fbcafc) Chore: removes unnecessary assignment in loop (#11780) (Dimitri Mitropoulos)
* [`20908a3`](https://github.com/eslint/eslint/commit/20908a38f489c285abf8fceef4d9d13bf8b87f22) Docs: removed '>' prefix from from docs/working-with-rules (#11818) (Alok Takshak)
* [`1c43eef`](https://github.com/eslint/eslint/commit/1c43eef605a9cf02a157881424ea62dcae747f69) Sponsors: Sync README with website (ESLint Jenkins)
* [`21f3131`](https://github.com/eslint/eslint/commit/21f3131aa1636afa8e5c01053e0e870f968425b1) Fix: `overrides` handle relative paths as expected (fixes #11577) (#11799) (Toru Nagashima)
* [`5509cdf`](https://github.com/eslint/eslint/commit/5509cdfa1b3d575248eef89a935f79c82e3f3071) Fix: fails the test case if autofix made syntax error (fixes #11615) (#11798) (Toru Nagashima)
* [`cb1922b`](https://github.com/eslint/eslint/commit/cb1922bdc07e58de0e55c13fd992dd8faf3292a4) Fix: show custom message for namespace import (fixes #11580) (#11791) (Pig Fang)
* [`37e5193`](https://github.com/eslint/eslint/commit/37e5193102d7544f155cdcb09c7c50dc602914d4) Update: add `endColumn` to no-useless-escape (fixes #11629) (#11790) (Pig Fang)
* [`ad4b048`](https://github.com/eslint/eslint/commit/ad4b048c6d034cbd7fd97deb4390d059bde8803f) Build: Fix typo in blog post template (fixes #11614) (#11782) (Kai Cataldo)
* [`9590587`](https://github.com/eslint/eslint/commit/9590587cef74c936ef9b7ce2d22a71e2fd0fbbc4) Update: improve reported location of arrow-parens (fixes #11773) (#11775) (Pig Fang)
* [`d662b17`](https://github.com/eslint/eslint/commit/d662b178c7dad193201564d16f7977af2f81ebcf) New: Add classname attribute to JUnit testcase (refs #11068) (#11683) (Fabio Pitino)
* [`8eaa9b2`](https://github.com/eslint/eslint/commit/8eaa9b259dc08dfb48269b1e4413d0d47698ed87) Chore: remove incorrect comment (#11769) (薛定谔的猫)
* [`4039a49`](https://github.com/eslint/eslint/commit/4039a49177f2fefacd747050b420c0c4560b7d4b) Chore: add .github/funding.yml (#11764) (Toru Nagashima)

v6.0.0-alpha.2 - May 25, 2019

* [`9b87fee`](https://github.com/eslint/eslint/commit/9b87fee9dc7b1d99a50b924cb6b81255ebb5c4a2) Chore: Fix formatter documentation generation (#11767) (Ilya Volodin)
* [`f116208`](https://github.com/eslint/eslint/commit/f11620848733a3a6f58811d9bb2c3e748d6135ac) Chore: Fix site generation script for releases (#11766) (Ilya Volodin)
* [`cf9cce8`](https://github.com/eslint/eslint/commit/cf9cce81aa68e9bc23840530cb33f4c3f551fb1e) Update: Add never option for new-parens (refs #10034) (#11379) (pfgithub)
* [`b5fa149`](https://github.com/eslint/eslint/commit/b5fa1491d2371a721e4b5029e797ae98fd30fed2) New: multiple processors support (fixes #11035, fixes #11725) (#11552) (Toru Nagashima)
* [`2d32a9e`](https://github.com/eslint/eslint/commit/2d32a9e8dd10a5927576bd50d184876c775da4af) Breaking: stricter rule config validating (fixes #9505) (#11742) (薛定谔的猫)
* [`71716eb`](https://github.com/eslint/eslint/commit/71716eba3155266d777b994a38af524952e97696) Update: add fixer for no-div-regex rule (fixes #11355) (#11744) (joe-re)
* [`53f7f4c`](https://github.com/eslint/eslint/commit/53f7f4cf8d8b66a1911db56e4f72764388a21cc4) Update: Uniform messages for the rules in "complexity" section (#11759) (Igor Novozhilov)
* [`0a801d7`](https://github.com/eslint/eslint/commit/0a801d702dc41dae7eac0c802b822493ddc3ac41) Chore: improve perf test (#11756) (薛定谔的猫)
* [`45bd336`](https://github.com/eslint/eslint/commit/45bd336e647a6fa8a502488e5cbd27ba02712083) Docs: add about RuleTester's parser to migration guide (fixes #11728) (#11761) (Toru Nagashima)
* [`1374be4`](https://github.com/eslint/eslint/commit/1374be44f7ec4b8c913c52cc84debc4012c4f3ea) Docs: add table of contents in readme (#11765) (薛定谔的猫)
* [`54e6eda`](https://github.com/eslint/eslint/commit/54e6edaa2f881aab530fa14e63d92e5e0e2ca55c) New: extends in glob-based config (fixes #8813) (#11554) (Toru Nagashima)
* [`ec105b2`](https://github.com/eslint/eslint/commit/ec105b24f7e036ecdc4267f018529ac3765e29d5) Chore: typo in JSDoc on timing.display's return value (#11755) (Dimitri Mitropoulos)
* [`e45cc3f`](https://github.com/eslint/eslint/commit/e45cc3f3dc44f3a5b6b713a1bf5ce6e46d87ca49) Docs: updated no-proto rule (fixes #11743) (#11746) (Francesco Trotta)
* [`15c6c63`](https://github.com/eslint/eslint/commit/15c6c6374c0425d5402142d012a541fa208bc9da) Chore: eslint-config-eslint require node >= 8 (#11718) (薛定谔的猫)
* [`f9790ca`](https://github.com/eslint/eslint/commit/f9790ca1baec1275f3c946586766a5713258ac32) Fix: typo: missing word in docs (#11750) (Dimitri Mitropoulos)
* [`219aecb`](https://github.com/eslint/eslint/commit/219aecb78bc646d44bad27dc775a9b3d3dc58232) Chore: restructure files (#11555) (Toru Nagashima)
* [`5dad0b1`](https://github.com/eslint/eslint/commit/5dad0b1d80c9cf380c49f46266c35d461d3cecad) Fix: Unignoring directories in .eslintignore (fixes #11684) (#11685) (Mykola Bilochub)
* [`4625090`](https://github.com/eslint/eslint/commit/462509058e46770cf70307cf8dba279f0e73b967) Docs: small fix about the migration guide (#11729) (Toru Nagashima)
* [`0e89c73`](https://github.com/eslint/eslint/commit/0e89c73177398eaf978a50d5b0f79ff8e43512f2) Sponsors: Sync README with website (ESLint Jenkins)
* [`5a296fa`](https://github.com/eslint/eslint/commit/5a296fa0c9345ad1a55e2b257e5f6c9f05fff362) Sponsors: Sync README with website (ESLint Jenkins)
* [`7c8e86b`](https://github.com/eslint/eslint/commit/7c8e86bf2c900cec7cd1dfd529a8c77cc81ef34c) Fix: wrong 'plugin-missing' error on Node.js 12 (fixes #11720) (#11722) (Toru Nagashima)
* [`67c671f`](https://github.com/eslint/eslint/commit/67c671fdc1c8b08cb8d263a9bb2151e3108c88b4) Chore: ignore deprecated rules in fuzz tests (#11710) (Pig Fang)
* [`af81cb3`](https://github.com/eslint/eslint/commit/af81cb3ecc5e6bf43a6a2d8f326103350513a1b8) Chore: make fuzzer produce minimal reproducible examples of bugs (#11700) (Teddy Katz)

v6.0.0-alpha.1 - May 10, 2019

* [`e84b6f8`](https://github.com/eslint/eslint/commit/e84b6f8b171ba4266164688f76d5ee45d278e5c2) Docs: fix example in object-curly-newline docs (#11633) (golopot)
* [`252efd3`](https://github.com/eslint/eslint/commit/252efd337b1441debb6d2cc8f51a625549b2c535) Fix: delete unnecessary duplicated question (fixes #11617) (#11618) (HelloRusk)
* [`21dd211`](https://github.com/eslint/eslint/commit/21dd2116c70b93aa8dd50d2b15e202724b11486a) New: add --resolve-plugins-relative-to flag (#11696) (Teddy Katz)
* [`1a3a88d`](https://github.com/eslint/eslint/commit/1a3a88df2f952c34631d8e1d83de47178826fce0) Fix: Curly rule incorrectly flagging lexical declarations (fixes #11663) (#11675) (Brian Kurek)
* [`f42d0af`](https://github.com/eslint/eslint/commit/f42d0afd89874b459fce1eb1998247d53f9aa42b) Chore: lazy loading for rules (#11705) (Toru Nagashima)
* [`f47d72c`](https://github.com/eslint/eslint/commit/f47d72ce2f2edb80cd38810894b9d4bda896bb29) Fix: not set ecmaVersion to 6 when sourceType=module (fixes #9687) (#11649) (薛定谔的猫)
* [`9484e9e`](https://github.com/eslint/eslint/commit/9484e9ea188ff70683c3112e397c7fddcc3f8095) Fix: ignore return statements in dead code (fixes #11647) (#11688) (Toru Nagashima)
* [`aae6f65`](https://github.com/eslint/eslint/commit/aae6f6525557ba06e73f051511646056313fcf91) Fix: don't use deprecated API (#11689) (Toru Nagashima)
* [`483239e`](https://github.com/eslint/eslint/commit/483239ec74a0c13529fc99547a784b749f41dd54) Docs: updated ImportDeclaration in docs-rules-indent (#11679) (Alok Takshak)
* [`f5bae78`](https://github.com/eslint/eslint/commit/f5bae78c19d5359f67969a2ff344025082e253f4) Chore: fix CLIEngine tests when os.tmpdir is a symlink (#11697) (Teddy Katz)
* [`e4400b5`](https://github.com/eslint/eslint/commit/e4400b5a02602bba7f67ea4cb874c231903c546a) Fix: require-atomic-updates false positive (fixes #11194, fixes #11687) (#11224) (Toru Nagashima)
* [`6ae21a4`](https://github.com/eslint/eslint/commit/6ae21a4bfe5a1566f787fbad798182a524b96d28) Breaking: fix config loading (fixes #11510, fixes #11559, fixes #11586) (#11546) (Toru Nagashima)
* [`bc0819c`](https://github.com/eslint/eslint/commit/bc0819c94aad14f7fad3cbc2338ea15658b0f272) Sponsors: Sync README with website (ESLint Jenkins)
* [`036a188`](https://github.com/eslint/eslint/commit/036a188143677384f720ff18071fc4206c54500b) Sponsors: Sync README with website (ESLint Jenkins)
* [`4b3b036`](https://github.com/eslint/eslint/commit/4b3b036d6240cdbc2d52e670de36b1117f5f34d7) Docs: replace `var` with `const` in code examples (#11655) (Niyaz Akhmetov)
* [`e4a08ba`](https://github.com/eslint/eslint/commit/e4a08bae82788136b6899262cb8b9ed4fe7964e6) Chore: update eslint-plugin-node to 9.0.0 (#11670) (Toru Nagashima)
* [`f2e7828`](https://github.com/eslint/eslint/commit/f2e78281d057f38b18cc160e81ed1bb54a5b9565) Docs: Fix Node 6 LTS EOL date (#11676) (James Ross)
* [`4052bfe`](https://github.com/eslint/eslint/commit/4052bfebb87850b901f2eb8687edfbe49c01d68f) Sponsors: Sync README with website (ESLint Jenkins)
* [`f6fc045`](https://github.com/eslint/eslint/commit/f6fc0450e749707bed44118c1205fb4e73e65628) Sponsors: Sync README with website (ESLint Jenkins)
* [`1ebf21b`](https://github.com/eslint/eslint/commit/1ebf21bc18769956366110bb62ff677639e633ae) Sponsors: Sync README with website (ESLint Jenkins)
* [`776b0fe`](https://github.com/eslint/eslint/commit/776b0fe3d93da958517ac7752682091f22eb30b4) Fix: update rule message of no-throw-literal (fixes #11637) (#11638) (Pig Fang)
* [`67c08b6`](https://github.com/eslint/eslint/commit/67c08b67509c54acd96aab2cec22efb53bfe6265) Fix: consider comments in object-curly-spacing (fixes #11656) (#11657) (Pig Fang)
* [`b6d41cb`](https://github.com/eslint/eslint/commit/b6d41cbe28a8b28b1c1d9aa36cb4c349c73f6f1d) Fix: check token before using in no-cond-assign (fixes #11611) (#11619) (Pig Fang)
* [`7f290a9`](https://github.com/eslint/eslint/commit/7f290a9044ca795884ac2e495cd31b2a35f109a6) Chore: add eslint as a devDependeny (#11654) (Toru Nagashima)
* [`139fd2f`](https://github.com/eslint/eslint/commit/139fd2f1254bcc524738f8c2645e0847df95e0d0) Chore: add markdownlint-cli (#11653) (Toru Nagashima)
* [`adc6585`](https://github.com/eslint/eslint/commit/adc6585ce074e03fc8a842e8ebc5b082a0ed0b65) Docs: update status of breaking changes in migration guide (#11652) (Teddy Katz)
* [`eef71e4`](https://github.com/eslint/eslint/commit/eef71e455e67040168c8df8a6c9c2b4fbe805a50) Docs: add missing items to migration guide (#11628) (Teddy Katz)
* [`0fc8e62`](https://github.com/eslint/eslint/commit/0fc8e62818bc8d0a0a804b59c6110818844df5f3) Breaking: eslint:recommended changes (fixes #10768) (#11518) (薛定谔的猫)
* [`1c34d4a`](https://github.com/eslint/eslint/commit/1c34d4a6313c399761281282fff3a1bbe5e17b6d) Sponsors: Sync README with website (ESLint Jenkins)
* [`33695e7`](https://github.com/eslint/eslint/commit/33695e7f7048306ac196eff6e5a16e165ad03090) Sponsors: Sync README with website (ESLint Jenkins)
* [`c94cf21`](https://github.com/eslint/eslint/commit/c94cf212d31513fde74e0ea88b79e5e0f89a18a4) Sponsors: Sync README with website (ESLint Jenkins)
* [`f62a451`](https://github.com/eslint/eslint/commit/f62a4510b007172c7160f007a6ec2aa2c9a80dd7) Build: add node 12 (#11648) (薛定谔的猫)
* [`20364cc`](https://github.com/eslint/eslint/commit/20364cc4f7fe0423adce0dd44fb24fc48e1cae4b) Breaking: make no-redeclare stricter (fixes #11370, fixes #11405) (#11509) (Toru Nagashima)
* [`ed675a6`](https://github.com/eslint/eslint/commit/ed675a6e5ac42898555c51a7cf771b79695ba591) Sponsors: Sync README with website (ESLint Jenkins)
* [`8b4dba6`](https://github.com/eslint/eslint/commit/8b4dba606f8306f8ad0a37e2174a6e3236f7ebe7) Chore: Add linting to git commit (#11556) (Nicholas C. Zakas)
* [`8684f46`](https://github.com/eslint/eslint/commit/8684f4646da33bfe81e8f7f8c2d1af8b31065564) Sponsors: Sync README with website (ESLint Jenkins)
* [`1bdacc9`](https://github.com/eslint/eslint/commit/1bdacc9b703158d5ca3563e4a9b67bb8453ac316) Sponsors: Sync README with website (ESLint Jenkins)
* [`e62c8af`](https://github.com/eslint/eslint/commit/e62c8af6a86e35dc05f30713faf87a18cc77714d) Sponsors: Sync README with website (ESLint Jenkins)
* [`1dfe077`](https://github.com/eslint/eslint/commit/1dfe077b7e47c6090277eb984e08bd472bb5595e) Fix: autofix of no-unneeded-ternary made syntax error (fixes #11579) (#11616) (Toru Nagashima)
* [`bebd079`](https://github.com/eslint/eslint/commit/bebd0793eaf122b013cca501ff2c6b0fc05d5493) Docs: fix grammar mistake in no-caller docs (#11623) (Daniel Lemay)
* [`f570be1`](https://github.com/eslint/eslint/commit/f570be17b339cb7622c512331b1653013279855a) Sponsors: Sync README with website (ESLint Jenkins)
* [`7c13a1c`](https://github.com/eslint/eslint/commit/7c13a1c144a6a7c99cd9338a24166da9f7439cd0) Sponsors: Sync README with website (ESLint Jenkins)
* [`b7bd432`](https://github.com/eslint/eslint/commit/b7bd432e1161feba8dbb81f62cf03cafad42c3d4) Sponsors: Sync README with website (ESLint Jenkins)
* [`412a76b`](https://github.com/eslint/eslint/commit/412a76b316e05ca85334c1d6bc6372df536da2db) Sponsors: Sync README with website (ESLint Jenkins)

v6.0.0-alpha.0 - April 12, 2019

* [`3d9e137`](https://github.com/eslint/eslint/commit/3d9e1372aad1e174b5438e3d6bd75fdefba06bad) Chore: fix test that fails when the CWD contains a space (#11612) (Teddy Katz)
* [`8bfd1d1`](https://github.com/eslint/eslint/commit/8bfd1d138001d4493180b2fcff3330b42d0bb7cb) Docs: add v6.0.0 migration guide (#11515) (Teddy Katz)
* [`9e49b56`](https://github.com/eslint/eslint/commit/9e49b56c08fd0e449fddab45dfeb0da8d918b460) Breaking: upgrade espree to 6.0.0-alpha.0 (fixes #9687) (#11610) (Teddy Katz)
* [`0127d10`](https://github.com/eslint/eslint/commit/0127d107590acabfdea4a68b56acbeee6a7b9daa) Fix: no-var fixed to incorrect code (fixes #11441) (#11443) (薛定谔的猫)
* [`5cfdc2d`](https://github.com/eslint/eslint/commit/5cfdc2d08307c63bec487e76d2f470ef84166867) Update: Improve no-loop-func rule description and message (#11046) (Pedro Lopes)
* [`608a02c`](https://github.com/eslint/eslint/commit/608a02c60656b96c3219d342eee7e98b55bdd580) Fix: object-shorthand ignoreConstructors option (fixes #11595) (#11596) (overlookmotel)
* [`eeea893`](https://github.com/eslint/eslint/commit/eeea89361d48494995446ddb6ee6f049457911ec) Upgrade: update js-yaml package to 3.13.1 version (#11607) (Pobegaylo Maksim)
* [`e70d5f7`](https://github.com/eslint/eslint/commit/e70d5f7573a9641d7b63394df53a3ef86183445c) Upgrade: compatible deps (#11608) (薛定谔的猫)
* [`a55913d`](https://github.com/eslint/eslint/commit/a55913d6c6fd1a7c684b8b4d7ab380cf7dc83eb8) Sponsors: Sync README with website (ESLint Jenkins)
* [`9a6e8fe`](https://github.com/eslint/eslint/commit/9a6e8fe4b025d52275f7ad2959361587f476cc58) Sponsors: Sync README with website (ESLint Jenkins)
* [`cbdee62`](https://github.com/eslint/eslint/commit/cbdee6230d22522c37259449467ace16f28ea8e8) Docs: README updates to reflect JSCS compat project is finished (#11568) (Kevin Partington)
* [`b92ca6e`](https://github.com/eslint/eslint/commit/b92ca6ea8ae46b92c258921e8b5b40f5035dbc43) Fix: getErrorResults function to not mutate passed parameter (#11592) (danielamaia)
* [`ef7801e`](https://github.com/eslint/eslint/commit/ef7801ea510e12a9ca4963bcc8ec7e3aacc75ff0) Breaking: disallow invalid rule defaults in RuleTester (fixes #11473) (#11599) (Teddy Katz)
* [`c021117`](https://github.com/eslint/eslint/commit/c021117915d5d23399233f761a237e138f1854af) Sponsors: Sync README with website (ESLint Jenkins)
* [`4e7cdca`](https://github.com/eslint/eslint/commit/4e7cdca571632a3f3c32b39eb03022fe88ca8b30) Breaking: comma-dangle enable functions: "never" (fixes #11502) (#11519) (薛定谔的猫)
* [`12f256f`](https://github.com/eslint/eslint/commit/12f256f22534c4a4e1ca0ba54c37c6db81441461) Breaking: no-confusing-arrow enable allowParens: true (fixes #11503) (#11520) (薛定谔的猫)
* [`25cc63d`](https://github.com/eslint/eslint/commit/25cc63d47e6c0aea8b88589a088c1f0de5f6f1cc) Breaking: simplify config/plugin/parser resolution (fixes #10125) (#11388) (Teddy Katz)
* [`63fead8`](https://github.com/eslint/eslint/commit/63fead86f8cf4e6b33d5424fb7db1e76a66d4cce) Sponsors: Sync README with website (ESLint Jenkins)
* [`595de40`](https://github.com/eslint/eslint/commit/595de4074fac1b5839f56b29fe0106a7fda7e3e0) Docs: edit arrow-parens as-needed explanation (fixes #11202) (#11569) (Logan Lowder)
* [`3396c3e`](https://github.com/eslint/eslint/commit/3396c3e2231b5b6e16da8751c22c86c256590f6b) Upgrade: karma@^4.0.1, drops Node 6 support, fixes vulnerability (#11570) (Kevin Partington)
* [`2f8ae13`](https://github.com/eslint/eslint/commit/2f8ae1397eef3625fe66636e95b0b312c6ff8a37) Update: support single argument on newline with function-paren-newline (#11406) (Vladlen Grachev)
* [`fd1c91b`](https://github.com/eslint/eslint/commit/fd1c91b00e8d8c3a83d21e60668d5f1fa61cb214) Breaking: throw an error for invalid global configs (refs #11338) (#11517) (Teddy Katz)
* [`be83322`](https://github.com/eslint/eslint/commit/be833229b355eafb90f3e0bbc29bb106e100bed0) Breaking: Remove extra rules from eslint:recommended (fixes #10873) (#11357) (Kevin Partington)
* [`2543f11`](https://github.com/eslint/eslint/commit/2543f11dfe8069ed5096073169cf6791d42454db) Breaking: remove deprecated experimentalObjectRestSpread option (#11420) (Teddy Katz)
* [`19248e0`](https://github.com/eslint/eslint/commit/19248e0838425748d75518fe9f0a985587793378) Fix: make `overrides[].files` matching dotfiles (fixes #11201) (#11225) (Toru Nagashima)
* [`0fb5fd4`](https://github.com/eslint/eslint/commit/0fb5fd402334098dc44cbfbb8ab25919da04843d) Breaking: interpret rule options as unicode regexes (fixes #11423) (#11516) (Teddy Katz)
* [`6e7da57`](https://github.com/eslint/eslint/commit/6e7da57dddc41830df4aee77e31c4320c1557350) Breaking: drop Node.js 6 support (fixes #11456) (#11557) (Toru Nagashima)
* [`a73b4b8`](https://github.com/eslint/eslint/commit/a73b4b8d6398b00bdaf90599d9e6d1c80f000f88) Docs: Update README team and sponsors (ESLint Jenkins)

v5.16.0 - March 29, 2019

* [`dfef227`](https://github.com/eslint/eslint/commit/dfef227091955a2f8f3fa8c76ad79de8a77e7955) Build: gensite passes rulesMeta to formatter rendering (#11567) (Kevin Partington)
* [`c06d38c`](https://github.com/eslint/eslint/commit/c06d38c81bd9203c904587396a65d3c8cc7f2944) Fix: Allow HTML formatter to handle no meta data (#11566) (Ilya Volodin)
* [`87a5c03`](https://github.com/eslint/eslint/commit/87a5c034977cf4538ff3539d2f8776a987c5942a) Docs: `func-style`: clarify when `allowArrowFunctions` is used (#11548) (Oliver Joseph Ash)
* [`bc3e427`](https://github.com/eslint/eslint/commit/bc3e427ee8875c53eac6b6762884b50074f1adfc) Update: pass rule meta to formatters RFC 10 (#11551) (Chris Meyer)
* [`b452f27`](https://github.com/eslint/eslint/commit/b452f270bc0b523d88d5d827c95be3096f82e99d) Chore: Update README to pull in reviewer data (#11506) (Nicholas C. Zakas)
* [`afe3d25`](https://github.com/eslint/eslint/commit/afe3d25f8afb88caee43f7202d0eb96f33a92a6b) Upgrade: Bump js-yaml dependency to fix Denial of Service vulnerability (#11550) (Vernon de Goede)
* [`4fe7eb7`](https://github.com/eslint/eslint/commit/4fe7eb7cecdc2395cf1eeaa20921bda8460b00c2) Chore: use nyc instead of istanbul (#11532) (Toru Nagashima)
* [`f16af43`](https://github.com/eslint/eslint/commit/f16af439694aab473c647d8fae47c402bd489447) Chore: fix formatters/table test (#11534) (Toru Nagashima)
* [`78358a8`](https://github.com/eslint/eslint/commit/78358a8f66e95c4fcc921f2497e8a5ec5f1537ec) Docs: fix duplicate punctuation in CLI docs (#11528) (Teddy Katz)

v5.15.3 - March 18, 2019

* [`71adc66`](https://github.com/eslint/eslint/commit/71adc665b9649b173adc76f80723b8de20664ae1) Fix: avoid moving comments in implicit-arrow-linebreak (fixes #11521) (#11522) (Teddy Katz)
* [`1f715a2`](https://github.com/eslint/eslint/commit/1f715a20c145d8ccc38f3310afccd838495d09d4) Chore: make test-case-property-ordering reasonable (#11511) (Toru Nagashima)

v5.15.2 - March 15, 2019

* [`29dbca7`](https://github.com/eslint/eslint/commit/29dbca73d762a809adb2f457b527e144426d54a7) Fix: implicit-arrow-linebreak adds extra characters (fixes #11268) (#11407) (Mark de Dios)
* [`5d2083f`](https://github.com/eslint/eslint/commit/5d2083fa3e14c024197f6c386ff72237a145e258) Upgrade: eslint-scope@4.0.3 (#11513) (Teddy Katz)
* [`a5dae7c`](https://github.com/eslint/eslint/commit/a5dae7c3d30231c2f5f075d98c2c8825899bab16) Fix: Empty glob pattern incorrectly expands to "/**" (#11476) (Ben Chauvette)
* [`448e8da`](https://github.com/eslint/eslint/commit/448e8da94d09b397e98ffcb6f22b55a578ef79c1) Chore: improve crash reporting (fixes #11304) (#11463) (Alex Zherdev)
* [`0f56dc6`](https://github.com/eslint/eslint/commit/0f56dc6d9eadad05dc3d5c9d1d9ddef94e10c5d3) Chore: make config validator params more consistent (#11435) (薛定谔的猫)
* [`d6c1122`](https://github.com/eslint/eslint/commit/d6c112289f0f16ade070865c8786831b7940ca79) Docs: Add working groups to maintainer guide (#11400) (Nicholas C. Zakas)
* [`5fdb4d3`](https://github.com/eslint/eslint/commit/5fdb4d3fb01b9d8a4c2dff71ed9cddb2f8feefb0) Build: compile deps to ES5 when generating browser file (fixes #11504) (#11505) (Teddy Katz)
* [`06fa165`](https://github.com/eslint/eslint/commit/06fa1655c3da8394ed9144d727115fc434b0416f) Build: update CI testing configuration (#11500) (Reece Dunham)
* [`956e883`](https://github.com/eslint/eslint/commit/956e883c21fd9f393bf6718d032a4e2e53b33f22) Docs: Fix example in no-restricted-modules docs (#11454) (Paul O’Shannessy)
* [`2c7431d`](https://github.com/eslint/eslint/commit/2c7431d6b32063f74e3837ee727f26af215eada7) Docs: fix json schema example dead link (#11498) (kazuya kawaguchi)
* [`e7266c2`](https://github.com/eslint/eslint/commit/e7266c2478aff5d66e7859313feb49e3a129f85e) Docs: Fix invalid JSON in "Specifying Parser Options" (#11492) (Mihira Jayasekera)
* [`6693161`](https://github.com/eslint/eslint/commit/6693161978a83e0730d5ea0fecdb627c5a2acdfd) Sponsors: Sync README with website (ESLint Jenkins)
* [`62fee4a`](https://github.com/eslint/eslint/commit/62fee4a976897d158c8c137339728cd280333286) Chore: eslint-config-eslint enable comma-dangle functions: "never" (#11434) (薛定谔的猫)
* [`34a5382`](https://github.com/eslint/eslint/commit/34a53829e7a63ff2f6b371d77ce283bbdd373b91) Build: copy bundled espree to website directory (#11478) (Pig Fang)
* [`f078f9a`](https://github.com/eslint/eslint/commit/f078f9a9e094ec00c61a6ef1c9550d017631e69a) Chore: use "file:" dependencies for internal rules/config (#11465) (Teddy Katz)
* [`0756128`](https://github.com/eslint/eslint/commit/075612871f85aa04cef8137bd32247e128ad600b) Docs: Add `visualstudio` to formatter list (#11480) (Patrick Eriksson)
* [`44de9d7`](https://github.com/eslint/eslint/commit/44de9d7e1aa2fcae475a97b8f597b7d8094566b2) Docs: Fix typo in func-name-matching rule docs (#11484) (Iulian Onofrei)

v5.15.1 - March 4, 2019

* [`fe1a892`](https://github.com/eslint/eslint/commit/fe1a892f85b09c3d2fea05bef011530a678a6af5) Build: bundle espree (fixes eslint/eslint.github.io#546) (#11467) (薛定谔的猫)
* [`458053b`](https://github.com/eslint/eslint/commit/458053b0b541f857bf233dacbde5ba80681820f8) Fix: avoid creating invalid regex in no-warning-comments (fixes #11471) (#11472) (Teddy Katz)

v5.15.0 - March 1, 2019

* [`4088c6c`](https://github.com/eslint/eslint/commit/4088c6c9d4578cd581ce8ff4385d90b58a75b755) Build: Remove path.resolve in webpack build (#11462) (Kevin Partington)
* [`ec59ec0`](https://github.com/eslint/eslint/commit/ec59ec09c8d001b8c04f9edc09994e2b0d0af0f9) New: add rule "prefer-named-capture-group" (fixes #11381) (#11392) (Pig Fang)
* [`a44f750`](https://github.com/eslint/eslint/commit/a44f75073306e5ea4e6722654009a99884fbca4f) Upgrade: eslint-scope@4.0.2 (#11461) (Teddy Katz)
* [`d3ce611`](https://github.com/eslint/eslint/commit/d3ce611e1c705440ccbcae357f2194134d026541) Sponsors: Sync README with website (ESLint Jenkins)
* [`ee88475`](https://github.com/eslint/eslint/commit/ee884754e4111e11994ff0df3f0c29e43e1dc3f2) Chore: add utils for rule tests (#11453) (薛定谔的猫)
* [`d4824e4`](https://github.com/eslint/eslint/commit/d4824e46d7a6ca1618454d3c6198403382108123) Sponsors: Sync README with website (ESLint Jenkins)
* [`6489518`](https://github.com/eslint/eslint/commit/64895185bde5233223648bcaf46f8deb72c9fb55) Fix: no-extra-parens crash when code is "((let))" (#11444) (Teddy Katz)
* [`9d20de2`](https://github.com/eslint/eslint/commit/9d20de2b0ac756bd62888119b8e08c7441d8a5aa) Sponsors: Sync README with website (ESLint Jenkins)
* [`3f14de4`](https://github.com/eslint/eslint/commit/3f14de458ba120e9c013f5fc7c6fe3e9b40c1460) Sponsors: Sync README with website (ESLint Jenkins)
* [`3d6c770`](https://github.com/eslint/eslint/commit/3d6c7709d47e047b25d91ca1a77d6dab92313061) Sponsors: Sync README with website (ESLint Jenkins)
* [`de5cbc5`](https://github.com/eslint/eslint/commit/de5cbc526b30405e742b35d85d04361529d49ed4) Update: remove invalid defaults from core rules (fixes #11415) (#11427) (Teddy Katz)
* [`eb0650b`](https://github.com/eslint/eslint/commit/eb0650ba20cf9f9ad78dbaccfeb7e0e7ab56e31d) Build: fix linting errors on master (#11428) (Teddy Katz)
* [`5018378`](https://github.com/eslint/eslint/commit/5018378131fd5190bbccca902c0cf4276ee1581a) Chore: enable require-unicode-regexp on ESLint codebase (#11422) (Teddy Katz)
* [`f6ba633`](https://github.com/eslint/eslint/commit/f6ba633f56eca6be20fc4b0d9496a78b9498d578) Chore: lint all files in the repo at the same time (#11425) (Teddy Katz)
* [`8f3d717`](https://github.com/eslint/eslint/commit/8f3d71754932669332ad7623bcc4c1aef3897125) Docs: Add non-attending TSC member info (#11411) (Nicholas C. Zakas)
* [`ce0777d`](https://github.com/eslint/eslint/commit/ce0777da5bc167fe0c529158fd8216d3eaf11565) Docs: use more common spelling (#11417) (薛定谔的猫)
* [`b9aabe3`](https://github.com/eslint/eslint/commit/b9aabe34311f6189b87c9d8a1aa40f3513fed773) Chore: run fuzzer along with unit tests (#11404) (Teddy Katz)
* [`db0c5e2`](https://github.com/eslint/eslint/commit/db0c5e2a7f894b7cda71007b0ba43d7814b3fb2e) Build: switch from browserify to webpack (fixes #11366) (#11398) (Pig Fang)

v5.14.1 - February 18, 2019

* [`1d6e639`](https://github.com/eslint/eslint/commit/1d6e63930073e79e52890f552cc6e9a0646b7fb4) Fix: sort-keys throws Error at SpreadElement (fixes #11402) (#11403) (Krist Wongsuphasawat)

v5.14.0 - February 15, 2019

* [`85a04b3`](https://github.com/eslint/eslint/commit/85a04b319e6dfde1458174cd1d8c9e7d33da0871) Fix: adds conditional for separateRequires in one-var (fixes #10179) (#10980) (Scott Stern)
* [`0c02932`](https://github.com/eslint/eslint/commit/0c02932f1b2e2a85809e84617efa1b8836c19cfb) Upgrade: espree@5.0.1 (#11401) (Ilya Volodin)
* [`104ae88`](https://github.com/eslint/eslint/commit/104ae881d0b21e9c64e006b2a2c21535cef0ad28) Docs: Update governance doc with reviewers status (#11399) (Nicholas C. Zakas)
* [`ab8ac6a`](https://github.com/eslint/eslint/commit/ab8ac6adaaf7a88e160899e7f438a4cfd655eb6e) Fix: Support boundary spread elements in sort-keys (#11158) (Jakub Rożek)
* [`a23d197`](https://github.com/eslint/eslint/commit/a23d1975d48841eafdead1a1357e2af842f688bc) New: add allowSingleLineBlocks opt. to padded-blocks rule (fixes #7145) (#11243) (richie3366)
* [`e25e7aa`](https://github.com/eslint/eslint/commit/e25e7aa3ea1e8c9b3cd3242acda6d4a5572c2c6a) Fix: comma-spacing ignore comma before closing paren (fixes #11295) (#11374) (Pig Fang)
* [`a1f7c44`](https://github.com/eslint/eslint/commit/a1f7c44ea9efbd9393889c1cc91b74260e0a8e02) Docs: fix space-before-blocks correct code for "classes": "never" (#11391) (PoziWorld)
* [`14f58a2`](https://github.com/eslint/eslint/commit/14f58a2bec4d6aade0de22771c378b86b1e51959) Docs: fix grammar in object-curly-spacing docs (#11389) (PoziWorld)
* [`d3e9a27`](https://github.com/eslint/eslint/commit/d3e9a27bbba30008a610df59e82b7192f0ecc3a3) Docs: fix grammar in “those who says” (#11390) (PoziWorld)
* [`ea8e804`](https://github.com/eslint/eslint/commit/ea8e8045ba0e6c1e1015104346af962f3e16fd81) Docs: Add note about support for object spread (fixes #11136) (#11395) (Steven Thomas)
* [`95aa3fd`](https://github.com/eslint/eslint/commit/95aa3fdb392d265e6c3d813d54076458e88e7ad8) Docs: Update README team and sponsors (ESLint Jenkins)
* [`51c4972`](https://github.com/eslint/eslint/commit/51c497298a15ad296a2b1f8fc397df687976b836) Update: Behavior of --init (fixes #11105) (#11332) (Nicholas C. Zakas)
* [`ad7a380`](https://github.com/eslint/eslint/commit/ad7a38097c32a91e5a831ef1bc8933601532576c) Docs: Update README team and sponsors (ESLint Jenkins)
* [`550de1e`](https://github.com/eslint/eslint/commit/550de1e611a1e9af873bcb18d74cf2056e8d2e1b) Update: use `default` keyword in JSON schema (fixes #9929) (#11288) (Pig Fang)
* [`983c520`](https://github.com/eslint/eslint/commit/983c5201210d7a4ffab0b3d05ab9919c0754e5ca) Update: Use 'readonly' and 'writable' for globals (fixes #11359) (#11384) (Nicholas C. Zakas)
* [`f1d3a7e`](https://github.com/eslint/eslint/commit/f1d3a7ee7c82365989e219b1dae379f08f6dd526) Upgrade: some deps (fixes #11372) (#11373) (薛定谔的猫)
* [`3e0c417`](https://github.com/eslint/eslint/commit/3e0c4176eff085498b813f8ba1732d7ed6ee44f8) Docs: Fix grammar in “there’s nothing prevent you” (#11385) (PoziWorld)
* [`de988bc`](https://github.com/eslint/eslint/commit/de988bc909b491366ad0cd9bc83f4d6de42d041a) Docs: Fix grammar: Spacing improve -> Spacing improves (#11386) (PoziWorld)
* [`1309dfd`](https://github.com/eslint/eslint/commit/1309dfdebb5595460b79dcac20df6a1f109e7566) Revert "Build: fix test failure on Node 11 (#11100)" (#11375) (薛定谔的猫)
* [`1e56897`](https://github.com/eslint/eslint/commit/1e56897db3e254e0aef6d2fe3274157fc379c79e) Docs: “the function actually use”: use -> uses (#11380) (PoziWorld)
* [`5a71bc9`](https://github.com/eslint/eslint/commit/5a71bc95a7e961b1b1b77022645e0bd9cdd08dc0) Docs: Update README team and sponsors (ESLint Jenkins)
* [`82a58ce`](https://github.com/eslint/eslint/commit/82a58ce26b282fd80335b3ac4fc88f21266c3ba1) Docs: Update README team and sponsors (ESLint Jenkins)
* [`546d355`](https://github.com/eslint/eslint/commit/546d355ace65631e27de859baea3ffcc50e0ad2c) Docs: Update README with latest sponsors/team data (#11378) (Nicholas C. Zakas)
* [`c0df9fe`](https://github.com/eslint/eslint/commit/c0df9febb7c7e045ababc10b88dbcbb3f28c724c) Docs: `...` is not an operator (#11232) (Felix Kling)
* [`7ecfdef`](https://github.com/eslint/eslint/commit/7ecfdefaeadb772f8b96ffe37c4a2c97fde0da16) Docs: update typescript parser (refs #11368) (#11369) (薛定谔的猫)
* [`3c90dd7`](https://github.com/eslint/eslint/commit/3c90dd7e25cf97833deddb11cfbc107a5663ac08) Update: remove prefer-spread autofix (fixes #11330) (#11365) (薛定谔的猫)
* [`5eb3121`](https://github.com/eslint/eslint/commit/5eb3121b82c1837da0c3021b7d9384bb30832e36) Update: add fixer for `prefer-destructuring` (fixes #11151) (#11301) (golopot)
* [`173eb38`](https://github.com/eslint/eslint/commit/173eb38cdb3e4673cba947521f27158828186d77) Docs: Clarify ecmaVersion doesn't imply globals (refs #9812) (#11364) (Keith Maxwell)
* [`84ce72f`](https://github.com/eslint/eslint/commit/84ce72fdeba082b7b132e4ac6b714fb1a93831b7) Fix: Remove extraneous linefeeds in `one-var` fixer (fixes #10741) (#10955) (st-sloth)
* [`389362a`](https://github.com/eslint/eslint/commit/389362a06ac6601512b872d3e843c7371f2a1bcc) Docs: clarify motivation for no-prototype-builtins (#11356) (Teddy Katz)
* [`533d240`](https://github.com/eslint/eslint/commit/533d240b0811f663494cb213b06cc9e51e1ff2d0) Update: no-shadow-restricted-names lets unassigned vars shadow undefined (#11341) (Teddy Katz)
* [`d0e823a`](https://github.com/eslint/eslint/commit/d0e823aef196a6564c87a78b72c1ef980ce67af9) Update: Make --init run js config files through linter (fixes #9947) (#11337) (Brian Kurek)
* [`92fc2f4`](https://github.com/eslint/eslint/commit/92fc2f4f3faf8aeaae8a8e71db0de405404fb6c3) Fix: CircularJSON dependency warning (fixes #11052) (#11314) (Terry)
* [`4dd19a3`](https://github.com/eslint/eslint/commit/4dd19a3c4c037adc860a65e96f2ba3eeccace1de) Docs: mention 'prefer-spread' in docs of 'no-useless-call' (#11348) (Klaus Meinhardt)
* [`4fd83d5`](https://github.com/eslint/eslint/commit/4fd83d5ec47a6a7b81cd8801c3bd63d27ea1c7c4) Docs: fix a misleading example in one-var (#11350) (薛定谔的猫)
* [`9441ce7`](https://github.com/eslint/eslint/commit/9441ce77b7228f2c4562e158a10905afe11f31f2) Chore: update incorrect tests to fix build failing (#11354) (薛定谔的猫)

v5.13.0 - February 1, 2019

* [`91c8884`](https://github.com/eslint/eslint/commit/91c8884971f5e57f5f7490d8daf92c4a9a489836) Chore: use local function to append "s" instead of a package (#11293) (Timo Tijhof)
* [`b5143bf`](https://github.com/eslint/eslint/commit/b5143bfc09e53d8da8f63421ade093b7593f4f51) Update: for-direction detection false positives/negatives (#11254) (Ruben Bridgewater)
* [`9005e63`](https://github.com/eslint/eslint/commit/9005e632d13476880c55f7e3c8a6e450762a5171) Chore: increase camelcase test coverage (#11299) (Redmond Tran)
* [`5b14ad1`](https://github.com/eslint/eslint/commit/5b14ad1003c7df9a37621dea55c6d6d0484adc05) Fix: false positive in no-constant-condition (fixes #11306) (#11308) (Pig Fang)
* [`6567c4f`](https://github.com/eslint/eslint/commit/6567c4f6665df85c3347388b29d8193cc8208d63) Fix: only remove arrow before body in object-shorthand (fixes #11305) (#11307) (Pig Fang)
* [`fa2f370`](https://github.com/eslint/eslint/commit/fa2f370affa4814dbdda278f9859d0172d4b7aa2) Docs: update rule configuration values in examples (#11323) (Kai Cataldo)
* [`0a3c3ff`](https://github.com/eslint/eslint/commit/0a3c3ff1d91e8f39943efc4a7d2bf6927d68d37e) New: Allow globals to be disabled/configured with strings (fixes #9940) (#11338) (Teddy Katz)
* [`dccee63`](https://github.com/eslint/eslint/commit/dccee63cf41234180c71bf0fe01b165c9078fc69) Chore: avoid hard-coding the list of core rules in eslint:recommended (#11336) (Teddy Katz)
* [`c1fd6f5`](https://github.com/eslint/eslint/commit/c1fd6f54d92efe615bcae529006221e122dbe9e6) Chore: remove undocumented `Linter#rules` property (refs #9161) (#11335) (Teddy Katz)
* [`36e3356`](https://github.com/eslint/eslint/commit/36e335681d61cbe3c83b653b7cc5f95730f1d86e) Chore: remove dead code for loading rules (#11334) (Teddy Katz)
* [`c464e27`](https://github.com/eslint/eslint/commit/c464e2744ec76e7e9c6c5af0f6162c92187f1ece) Docs: Rename `result` -> `foo` (#11210) (Alexis Tyler)

v5.12.1 - January 18, 2019

* [`eb5c401`](https://github.com/eslint/eslint/commit/eb5c4014f16be1c2003ed46ce9560d0d8a567d0f) Chore: use meta.messages in some rules (2/4) (refs #9870) (#10773) (薛定谔的猫)
* [`aa56247`](https://github.com/eslint/eslint/commit/aa56247746a0095996a41dd03bdbbf659f0f93b6) Fix: avoid loading core rules dynamically from FS in Linter (#11278) (Peter Metz)
* [`04450bb`](https://github.com/eslint/eslint/commit/04450bb7ed20f2412102538b238119d9764b4dc9) Docs: clarify process for adding committers (#11272) (Kai Cataldo)
* [`3ffcf26`](https://github.com/eslint/eslint/commit/3ffcf26c1c83efe7d7cf2d87f1063695ae653709) Docs: add @g-plane as committer (#11277) (Kai Cataldo)
* [`c403445`](https://github.com/eslint/eslint/commit/c40344566eff2e77a6ae2b2d2dbdbd4ad3e76b67) Fix: warn constant on RHS of || in no-constant-condition (fixes #11181) (#11253) (Merlin Mason)
* [`9194f45`](https://github.com/eslint/eslint/commit/9194f45ac7d521119a53773bf02b81670bad526e) Fix: Manage severity of 1 with TAP reporter (fixes #11110) (#11221) (Gabriel Cousin)
* [`000f495`](https://github.com/eslint/eslint/commit/000f4952ae6a4311fbbc3ed36c481235fcb0b64b) Docs: fix example for sort-imports ignoreDeclarationSort (#11242) (Remco Haszing)
* [`7c0bf2c`](https://github.com/eslint/eslint/commit/7c0bf2ca92d83125a1fa000c9c4250bae6b4fc21) Docs: Add `npx` usage to Getting Started guide (#11249) (eyal0803)
* [`da9174e`](https://github.com/eslint/eslint/commit/da9174e0798c1d785ddabb3ae405860fc5b89311) Docs: fixes typo peerDepencies (#11252) (Christian Kühl)
* [`9c31625`](https://github.com/eslint/eslint/commit/9c31625f19176664ef76dcf088ce50703c41c324) Docs: Improve custom formatter docs (#11258) (Nicholas C. Zakas)

v5.12.0 - January 4, 2019

* [`0d91e7d`](https://github.com/eslint/eslint/commit/0d91e7d28e5eba79a6032165cdef5d4549d26462) Update: Add sort-imports ignoreDeclarationSort (fixes #11019) (#11040) (Remco Haszing)
* [`f92d6f0`](https://github.com/eslint/eslint/commit/f92d6f05c4dcd4a3a0616871e10b31edae9dfad5) Build: Add karma-chrome-launcher support (#11027) (薛定谔的猫)
* [`166853d`](https://github.com/eslint/eslint/commit/166853d9c59db493f0b1bb68a67ad868662a4205) Upgrade: eslint-plugin-eslint-plugin@2.0.1 (#11220) (薛定谔的猫)
* [`bfff77a`](https://github.com/eslint/eslint/commit/bfff77ad4eaa02e2e62481c986634df38d5db6e5) Fix: no-param-reassign parameter in ternary operator (fixes #11236) (#11239) (周昊宇)
* [`258b654`](https://github.com/eslint/eslint/commit/258b6541f61dc3a9ae64e200680766a11c3dd316) Upgrade: require-uncached renamed to import-fresh (#11066) (薛定谔的猫)

v5.11.1 - December 26, 2018

* [`de79f10`](https://github.com/eslint/eslint/commit/de79f1026b7035f0296d7876f1db64f225cca1b8) Fix: handle optional catch bindings in no-useless-catch (#11205) (Colin Ihrig)

v5.11.0 - December 22, 2018

* [`b4395f6`](https://github.com/eslint/eslint/commit/b4395f671442a7e0be956382c24cce38025a6df6) New: add option `first` for VariableDeclarator in indent (fixes #8976) (#11193) (Pig Fang)
* [`2b5a602`](https://github.com/eslint/eslint/commit/2b5a60284670a3ab1281b206941ed38faf2ea10c) New: no-useless-catch rule (fixes #11174) (#11198) (Alexander Grasley)
* [`06b3b5b`](https://github.com/eslint/eslint/commit/06b3b5bfcf0429c5078d4f4af3c03bb777e4f022) Fix: Account for comments in implicit-arrow-linebreak (#10545) (Mark de Dios)
* [`4242314`](https://github.com/eslint/eslint/commit/4242314215a6f35e432860433906f47af1a29724) Update: handle computed properties in camelcase (fixes #11084) (#11113) (Bence Dányi)
* [`1009304`](https://github.com/eslint/eslint/commit/100930493d9ab802a94dac5c761515b12241ddd2) Docs: add a note for no-unused-expressions (fixes #11169) (#11192) (Pig Fang)
* [`88f99d3`](https://github.com/eslint/eslint/commit/88f99d31b88a4cde4563bc4a6f4c41f0cc557885) Docs: clarify how to use configs in plugins (#11199) (Kai Cataldo)
* [`bcf558b`](https://github.com/eslint/eslint/commit/bcf558b2f7036f487af2bdb2b2d34b6cdf7fc174) Docs: Clarify the no-unused-vars docs (#11195) (Jed Fox)
* [`a470eb7`](https://github.com/eslint/eslint/commit/a470eb73d52fae0f0bc48de5a487e23cf78fcfa9) Docs: Fix no-irregular-whitespace description (#11196) (Jed Fox)
* [`8abc8af`](https://github.com/eslint/eslint/commit/8abc8afe71691b747cbd1819a13d896e8aa5b92a) Docs: Remove a misleading example (#11204) (Bogdan Gradinariu)
* [`733d936`](https://github.com/eslint/eslint/commit/733d93618a99758a05453ab94505a9f1330950e0) Docs: link to JSDoc EOL blogpost in valid-jsdoc and require-jsdoc (#11191) (Nathan Diddle)
* [`d5eb108`](https://github.com/eslint/eslint/commit/d5eb108e17f676d0e4fcddeb1211b4bdfac760c1) Docs: Ensure `triage` label is added to new issues (#11182) (Teddy Katz)
* [`617a287`](https://github.com/eslint/eslint/commit/617a2874ed085bca36ca289aac55e3b7f7ce937e) Docs: add missing deprecation notices for jsdoc rules (#11171) (Teddy Katz)

v5.10.0 - December 8, 2018

* [`4b0f517`](https://github.com/eslint/eslint/commit/4b0f517cd317e5f1b99a1e8a0392332bd8a2e231) Upgrade: single- and multiline const, let, var statements (fixes #10721) (#10919) (Tom Panier)
* [`9666aba`](https://github.com/eslint/eslint/commit/9666abaf46c841fba7b5d4e53c6998cd25b9bc33) Update: space-infix-ops reports violating operator (#10934) (Bence Dányi)
* [`c14f717`](https://github.com/eslint/eslint/commit/c14f717f4c32860766185da47f64f8eb0c2d2998) Fix: Update all-files-ignored.txt message to be less confusing (#11075) (z.ky)
* [`9f3573d`](https://github.com/eslint/eslint/commit/9f3573dda3dc35bc220e945686cc835eaad0ac2c) Docs: Clarify the CLIEngine options (#10995) (Ed Morley)
* [`dd7b0cb`](https://github.com/eslint/eslint/commit/dd7b0cb019d94964930d30fec36f7b22ef072822) Chore: refactor template literal feature detection in 'quotes' rule (#11125) (Bryan)
* [`3bf0332`](https://github.com/eslint/eslint/commit/3bf0332508b921cb660c2e8a1ab7ddf46a2013b6) Fix: fix the fixer of lone comma with comments (fixes #10632) (#11154) (Pig Fang)
* [`f850726`](https://github.com/eslint/eslint/commit/f8507260c2091d18488fde20e466639d1a7f913c) Upgrade: Espree v5.0.0 (#11161) (Kai Cataldo)
* [`4490d7a`](https://github.com/eslint/eslint/commit/4490d7af529d4ecc18b6874f1d838869656da58a) Update: deprecate valid-jsdoc and require-jsdoc (#11145) (Teddy Katz)
* [`60dfb6c`](https://github.com/eslint/eslint/commit/60dfb6c623dfe829e5350dabe507e7850c1beacf) Docs: Update issue templates (#11163) (Teddy Katz)
* [`958987a`](https://github.com/eslint/eslint/commit/958987aa6f5630faa051d8f822f0200faff41924) Docs: Fix link to rule no-useless-rename (#11165) (Brian)
* [`62fd2b9`](https://github.com/eslint/eslint/commit/62fd2b93448966331db3eb2dfbe4e1273eb032b2) Update: Amend keyword-spacing to validate `default` keywords (#11097) (Bin Ury)
* [`4bcdfd0`](https://github.com/eslint/eslint/commit/4bcdfd07d514fd7a6b8672d33703d0b6c606f214) Chore: fix some jsdoc-related issues (#11148) (薛定谔的猫)
* [`c6471ed`](https://github.com/eslint/eslint/commit/c6471ed6feb3e71e239379a7042deb9b8ab3cf39) Docs: fix typo in issue-templates/new-rule (#11149) (薛定谔的猫)
* [`5d451c5`](https://github.com/eslint/eslint/commit/5d451c510c15abc41b5bb14b4955a7db96aeb100) Chore: Remove dependency on is-resolvable (#11128) (Matt Grande)
* [`bc50dc7`](https://github.com/eslint/eslint/commit/bc50dc7737496712463220e662946eb516e36ae1) Chore: Move ignored-paths, report-translator to lib/util (refs #10559) (#11116) (Kevin Partington)
* [`c0a80d0`](https://github.com/eslint/eslint/commit/c0a80d0ca3c80ca27694fc8aedcf84b72bfd9465) Fix: Do not strip underscores in camelcase allow (fixes #11000) (#11001) (Luke Page)
* [`a675c89`](https://github.com/eslint/eslint/commit/a675c89573836adaf108a932696b061946abf1e6) Docs: (Grammar) "the setup" -> "to set up" (#11117) (MarvinJWendt)
* [`54dfa60`](https://github.com/eslint/eslint/commit/54dfa602f62e6d183d57d60d5fdd417a263f479e) Fix: Typo in function comment parameters (#11111) (Pierre Maoui)
* [`cf296bd`](https://github.com/eslint/eslint/commit/cf296bdabf0dbbfbae491419e38aee4ecd63ec71) Docs: switch incorrect example with correct one (#11107) (Romain Le Quellec)
* [`d2d500c`](https://github.com/eslint/eslint/commit/d2d500ca5dff307189b9d4161a5e7b8282557dd6) Docs: no-console#When-Not-To-Use provides incorrect rule snippet (#11093) (Lawrence Chou)
* [`f394a1d`](https://github.com/eslint/eslint/commit/f394a1dfc5eb4874f899b7bc19685896893af7b8) Chore: Extract config comment parsing (#11091) (Nicholas C. Zakas)
* [`709190f`](https://github.com/eslint/eslint/commit/709190f8c5d7559b1e0915e25af60b50a94ba1c7) Build: fix test failure on Node 11 (#11100) (Teddy Katz)
* [`3025cdd`](https://github.com/eslint/eslint/commit/3025cddf0a2ea8461ce05575098a5714fcf6278d) Update: don't indent leading semi in line after import (fixes #11082) (#11085) (Pig Fang)
* [`e18c827`](https://github.com/eslint/eslint/commit/e18c827cc12cb1c52e5d0aa993f572cb56238704) Chore: refactor linter#parseBooleanConfig to improve readability (#11074) (薛定谔的猫)
* [`5da378a`](https://github.com/eslint/eslint/commit/5da378ac922d732ca1765f08edee0face1b1b924) Upgrade: eslint-release@1.2.0 (#11073) (Teddy Katz)

v5.9.0 - November 9, 2018

* 9436712 Fix: Unused recursive function expressions (fixes #10982) (#11032) (Sergei Startsev)
* c832cd5 Update: add `ignoreDestructuring` option to `id-match` rule (#10554) (一名宅。)
* 54687a8 Fix: prefer-const autofix multiline assignment (fixes #10582) (#10987) (Scott Stern)
* ae2b61d Update: "off" options for "space-before-blocks" (refs #10906) (#10907) (Sophie Kirschner)
* 57f357e Docs: Update require-await docs with exception (fixes #9540) (#11063) (Nicholas C. Zakas)
* 79a2797 Update: no-restricted-imports to check re-export (fixes #9678) (#11064) (Nicholas C. Zakas)
* 3dd7493 Docs: update ecmaVersion to include 2019/10 values (#11059) (Vse Mozhet Byt)
* 607635d Upgrade: eslint-plugin-node & eslint-plugin (#11067) (薛定谔的猫)
* dcc6233 Fix: Ignore empty statements in no-unreachable (fixes #9081) (#11058) (Nicholas C. Zakas)
* 7ad86de New: Add --fix-type option to CLI (fixes #10855) (#10912) (Nicholas C. Zakas)
* 0800b20 Chore: fix invalid super() calls in tests (#11054) (Teddy Katz)
* 4fe3287 Docs: Cross-reference two rules (refs #11041) (#11042) (Paul Melnikow)
* 5525eb6 Fix: rule deprecation warnings did not consider all rules (#11044) (Teddy Katz)
* 44d37ca Docs: Update steps for adding new TSC member (#11038) (Nicholas C. Zakas)
* 802e926 Update: Warn for deprecation in Node output (fixes #7443) (#10953) (Colin Chang)

v5.8.0 - October 26, 2018

* 9152417 Fix: deprecation warning in RuleTester using Node v11 (#11009) (Teddy Katz)
* e349a03 Docs: Update issue templates to ask for PRs (#11012) (Nicholas C. Zakas)
* 3d88b38 Chore: avoid using legacy report API in no-irregular-whitespace (#11013) (Teddy Katz)
* 5a31a92 Build: compile espree's deps to ES5 when generating site (fixes #11014) (#11015) (Teddy Katz)
* 3943635 Update: Create Linter.version API (fixes #9271) (#11010) (Nicholas C. Zakas)
* a940cf4 Docs: Mention version for config glob patterns (fixes #8793) (Nicholas C. Zakas)
* 6e1c530 Build: run tests on Node 11 (#11008) (Teddy Katz)
* 58ff359 Docs: add instructions for npm 2FA (refs #10631) (#10992) (Teddy Katz)
* 2f87bb3 Upgrade: eslint-release@1.0.0 (refs #10631) (#10991) (Teddy Katz)
* 57ef0fd Fix: prefer-const when using destructuring assign (fixes #8308) (#10924) (Nicholas C. Zakas)
* 577cbf1 Chore: Add typescript-specific edge case tests to space-infix-ops (#10986) (Bence Dányi)
* d45b184 Chore: Using deconstruction assignment for shelljs (#10974) (ZYSzys)

v5.7.0 - October 12, 2018

* 6cb63fd Update: Add iife to padding-line-between-statements (fixes #10853) (#10916) (Kevin Partington)
* 5fd1bda Update: no-tabs allowIndentationTabs option (fixes #10256) (#10925) (Kevin Partington)
* d12be69 Fix: no-extra-bind No autofix if arg may have side effect (fixes #10846) (#10918) (Kevin Partington)
* 847372f Fix: no-unused-vars false pos. with markVariableAsUsed (fixes #10952) (#10954) (Roy Sutton)
* 4132de7 Chore: Simplify space-infix-ops (#10935) (Bence Dányi)
* 543edfa Fix: Fix error with one-var (fixes #10937) (#10938) (Justin Krup)
* 95c4cb1 Docs: Fix typo for no-unsafe-finally (#10945) (Sergio Santoro)
* 5fe0e1a Fix: no-invalid-regexp disallows \ at end of pattern (fixes #10861) (#10920) (Toru Nagashima)
* f85547a Docs: Add 'When Not To Use' section to space-infix-ops (#10931) (Bence Dányi)
* 3dccac4 Docs: Update working-with-parsers link (#10929) (Azeem Bande-Ali)
* 557a8bb Docs: Remove old note about caching, add a new one (fixes #10739) (#10913) (Zac)
* fe8111a Chore: Add more test cases to space-infix-ops (#10936) (Bence Dányi)
* 066f7e0 Update: camelcase rule ignoreList added (#10783) (Julien Martin)
* 70bde69 Upgrade: table to version 5 (#10903) (Rouven Weßling)
* 2e52bca Chore: Update issue templates (#10900) (Nicholas C. Zakas)

v5.6.1 - September 28, 2018

* 9b26bdb Fix: avoid exponential require-atomic-updates traversal (fixes #10893) (#10894) (Teddy Katz)
* 9432b10 Fix: make separateRequires work in consecutive mode (fixes #10784) (#10886) (Pig Fang)
* e51868d Upgrade: debug@4 (fixes #10854) (#10887) (薛定谔的猫)
* d3f3994 Docs: add information about reporting security issues (#10889) (Teddy Katz)
* cc458f4 Build: fix failing tests on master (#10890) (Teddy Katz)
* a6ebfd3 Docs: clarify defaultAssignment option, fix no-unneeded-ternary examples (#10874) (CoffeeTableEspresso)
* 9d52541 Fix: Remove duplicate error message on crash (fixes #8964) (#10865) (Nicholas C. Zakas)
* 4eb9a49 Docs: Update quotes.md (#10862) (The Jared Wilcurt)
* 9159e9b Docs: Update complexity.md (#10867) (Szymon Przybylski)
* 14f4e46 Docs: Use Linter instead of linter in Nodejs API page (#10864) (Nicholas C. Zakas)
* b3e3cb1 Chore: Update debug log name to match filename (#10863) (Nicholas C. Zakas)

v5.6.0 - September 14, 2018

* c5b688e Update: Added generators option to func-names (fixes #9511) (#10697) (Oscar Barrett)
* 7da36d5 Fix: respect generator function expressions in no-constant-condition (#10827) (Julian Rosse)
* 0a65844 Chore: quote enable avoidEscape option in eslint-config-eslint (#10626) (薛定谔的猫)
* 32f41bd Chore: Add configuration wrapper markdown for the bug report template (#10669) (Iulian Onofrei)

v5.5.0 - August 31, 2018

* 6e110e6 Fix: camelcase duplicate warning bug (fixes #10801) (#10802) (Julian Rosse)
* 5103ee7 Docs: Add Brackets integration (#10813) (Jan Pilzer)
* b61d2cd Update: max-params to only highlight function header (#10815) (Ian Obermiller)
* 2b2f11d Upgrade: babel-code-frame to version 7 (#10808) (Rouven Weßling)
* 2824d43 Docs: fix comment placement in a code example (#10799) (Vse Mozhet Byt)
* 10690b7 Upgrade: devdeps and deps to latest (#10622) (薛定谔的猫)
* 80c8598 Docs: gitignore syntax updates (fixes #8139) (#10776) (Gustavo Santana)
* cb946af Chore: use meta.messages in some rules (1/4) (#10764) (薛定谔的猫)

v5.4.0 - August 17, 2018

* a70909f Docs: Add jscs-dev.github.io links (#10771) (Gustavo Santana)
* 034690f Fix: no-invalid-meta crashes for non Object values (fixes #10750) (#10753) (Sandeep Kumar Ranka)
* 11a462d Docs: Broken jscs.info URLs (fixes #10732) (#10770) (Gustavo Santana)
* 985567d Chore: rm unused dep string.prototype.matchall (#10756) (薛定谔的猫)
* f3d8454 Update: Improve no-extra-parens error message (#10748) (Timo Tijhof)
* 562a03f Fix: consistent-docs-url crashes if meta.docs is empty (fixes #10722) (#10749) (Sandeep Kumar Ranka)
* 6492233 Chore: enable no-prototype-builtins in codebase (fixes #10660) (#10664) (薛定谔的猫)
* 137140f Chore: use eslintrc overrides (#10677) (薛定谔的猫)

v5.3.0 - August 3, 2018

* dd6cb19 Docs: Updated no-return-await Rule Documentation (fixes #9695) (#10699) (Marla Foreman)
* 6009239 Chore: rename utils for consistency (#10727) (薛定谔的猫)
* 6eb972c New: require-unicode-regexp rule (fixes #9961) (#10698) (Toru Nagashima)
* 5c5d64d Fix: ignored-paths for Windows path (fixes #10687) (#10691) (Toru Nagashima)
* 5f6a765 Build: ensure URL fragments remain in documentation links (fixes #10717) (#10720) (Teddy Katz)
* 863aa78 Docs: add another example for when not to use no-await-in-loop (#10714) (Valeri Karpov)
* 6e78b7d Docs: remove links to terminated jscs.info domain (#10706) (Piotr Kuczynski)
* d56c39d Fix: ESLint cache no longer stops autofix (fixes #10679) (#10694) (Kevin Partington)
* 2cc3240 New: add no-misleading-character-class (fixes #10049) (#10511) (Toru Nagashima)
* 877f4b8 Fix: The "../.." folder is always ignored (fixes #10675) (#10682) (Sridhar)
* 5984820 Chore: Move lib/file-finder.js to lib/util/ (refs #10559) (#10695) (Kevin Partington)
* e37a593 Update: Fix incorrect default value for position (#10670) (Iulian Onofrei)
* 8084bfc Docs: change when not to use object spread (#10621) (Benny Powers)
* 7f496e2 Chore: Update require path for ast-utils (#10693) (Kevin Partington)
* 648a33a Chore: reorganize code structure of utilities (refs #10599) (#10680) (薛定谔的猫)
* f026fe1 Update: Fix 'function' in padding-line-between-statements (fixes #10487) (#10676) (Kevin Partington)
* c2bb8bb Docs: Remove superfluous object option sample code (#10652) (Iulian Onofrei)
* d34a13b Docs: add subheader in configuring/configuring-rules (#10686) (薛定谔的猫)
* d8aea28 Chore: rm unnecessary plugin in eslint-config-eslint (#10685) (薛定谔的猫)
* 9e76be7 Update: indent comments w/ nearby code if no blank lines (fixes #9733) (#10640) (Kevin Partington)
* 9e93d46 New: add no-async-promise-executor rule (fixes #10217) (#10661) (Teddy Katz)
* 5a2538c New: require-atomic-updates rule (fixes #10405) (#10655) (Teddy Katz)
* 8b83d2b Fix: always resolve default ignore patterns from CWD (fixes #9227) (#10638) (Teddy Katz)
* acb6658 Fix: ESLint crash with prefer-object-spread (fixes #10646) (#10649) (薛定谔的猫)
* 99fb7d3 Docs: fix misleading no-prototype-builtins description (#10666) (薛定谔的猫)
* 005b849 Docs: fix outdated description of `baseConfig` option (#10657) (Teddy Katz)
* 15a77c4 Docs: fix broken links (fixes eslint/eslint-jp#6) (#10658) (Toru Nagashima)
* 87cd344 Docs: Make marking a default option consistent with other rules (#10650) (Iulian Onofrei)
* 0cb5e3e Chore: Replace some function application with spread operators (#10645) (Kevin Partington)
* b6daf0e Docs: Remove superfluous section from no-unsafe-negation (#10648) (Iulian Onofrei)
* e1a3cac Chore: rm deprecated experimentalObjectRestSpread option in tests (#10647) (薛定谔的猫)

v5.2.0 - July 20, 2018

* 81283d0 Update: Cache files that failed linting (fixes #9948) (#10571) (Kevin Partington)
* 13cc63e Upgrade: ignore@4.0.2 (#10619) (Rouven Weßling)
* ac77a80 Chore: Fixing a call to Object.assign.apply in Linter (#10629) (Kevin Partington)
* 761f802 Upgrade: eslint-plugin-node to 7.0.1 (#10612) (Toru Nagashima)
* c517b2a Build: fix npm run perf failing(fixes #10577) (#10607) (薛定谔的猫)
* e596939 Chore: fix redundant equality check (#10617) (Toru Nagashima)
* 9f93d5f Docs: Updated Working with Custom Formatters (fixes #9950) (#10592) (Marla Foreman)
* 9aaf195 Chore: Extract lint result cache logic (refs #9948) (#10562) (Kevin Partington)
* 80b296e Build: package.json update for eslint-config-eslint release (ESLint Jenkins)
* e4e7ff2 Chore: fix error message in eslint-config-eslint (#10588) (薛定谔的猫)
* 1e88170 Chore: Move lib/logging and lib/timing to lib/util/ (refs #10559) (#10579) (Kevin Partington)
* 64dfa21 Build: Fix prerelease logic in blog post generation (fixes #10578) (#10581) (Kevin Partington)
* 0faf633 Chore: Simplify helper method in Linter tests (#10580) (Kevin Partington)

v5.1.0 - July 8, 2018

* 7328f99 Build: package.json update for eslint-config-eslint release (ESLint Jenkins)
* b161f6b Build: Include prerelease install info in release blog post (#10463) (Kevin Partington)
* b2df738 Fix: prefer-object-spread duplicated comma (fixes #10512, fixes #10532) (#10524) (Toru Nagashima)
* d8c3a25 Fix: wrap-regex doesn't work in some expression(fixes #10573) (#10576) (薛定谔的猫)
* 114f42e Docs: Clarify option defaults in max-lines-per-function docs (#10569) (Chris Harwood)
* 63f36f7 Fix: sort-keys in an object that contains spread (fixes #10261) (#10495) (katerberg)
* 601a5c4 Fix: Prefer-const rule crashing on array destructuring (fixes #10520) (#10527) (Michael Mason)
* 143890a Update: Adjust grammar of error/warnings fixable (#10546) (Matt Mischuk)
* 8ee39c5 Chore: small refactor config-validator (#10565) (薛定谔的猫)
* 100f1be Docs: add note about release issues to readme (#10572) (Teddy Katz)
* 02efeac Fix: do not fail on nested unknown operators (#10561) (Rubén Norte)
* 92b19ca Chore: use eslintrc overrides(dogfooding) (#10566) (薛定谔的猫)
* 076a6b6 Docs: add actionable fix to no-irregular-whitespace (#10558) (Matteo Collina)
* de663ec Docs: Only successfully linted files are cached (fixes #9802) (#10557) (Kevin Partington)
* f0e22fc Upgrade: globals@11.7.0 (#10497) (薛定谔的猫)
* 8a2ff2c Docs:  adding a section about disable rules for some files (#10536) (Wellington Soares)
* f22a3f8 Docs: fix a word in no-implied-eval (#10539) (Dan Homola)
* 20d8bbd Docs: add missing paragraph about "custom parsers" (#10547) (Pig Fang)
* b7addf6 Update: deprecate no-catch-shadow (fixes #10466) (#10526) (Toru Nagashima)
* e862dc3 Fix: Remove autofixer for no-debugger (fixes #10242) (#10509) (Teddy Katz)

v5.0.1 - June 25, 2018

* 196c102 Fix: valid-jsdoc should allow optional returns for async (fixes #10386) (#10480) (Mark Banner)
* 4c823bd Docs: Fix max-lines-per-function correct code's max value (#10513) (Rhys Bower)

v5.0.0 - June 22, 2018

* 0feedfd New: Added max-lines-per-function rule (fixes #9842) (#10188) (peteward44)
* daefbdb Upgrade: eslint-scope and espree to 4.0.0 (refs #10458) (#10500) (Brandon Mills)
* 077358b Docs: no-process-exit: recommend process.exitCode (#10478) (Andres Kalle)
* f93d6ff Fix: do not fail on unknown operators from custom parsers (fixes #10475) (#10476) (Rubén Norte)
* 05343fd Fix: add parens for yield statement (fixes #10432) (#10468) (Pig Fang)
* d477c5e Fix: check destructuring for "no-shadow-restricted-names" (fixes #10467) (#10470) (Pig Fang)
* 7a7580b Update: Add considerPropertyDescriptor option to func-name-matching (#9078) (Dieter Luypaert)
* e0a0418 Fix: crash on optional catch binding (#10429) (Toru Nagashima)
* de4dba9 Docs: styling team members (#10460) (薛定谔的猫)
* 5e453a3 Docs: display team members in tables. (#10433) (薛定谔的猫)
* b1895eb Docs: Restore intentional spelling mistake (#10459) (Wilfred Hughes)

v5.0.0-rc.0 - June 9, 2018

* abf400d Update: Add ignoreDestructing option to camelcase rule (fixes #9807) (#10373) (Andrew Lunny)
* e2b394d Upgrade: espree and eslint-scope to rc versions (#10457) (Kevin Partington)
* a370da2 Chore: small opt to improve readability (#10241) (薛定谔的猫)
* 640bf07 Update: Fixes multiline no-warning-comments rule. (fixes #9884) (#10381) (Scott Stern)
* 831c39a Build: Adding rc release script to package.json (#10456) (Kevin Partington)
* dc4075e Update: fix false negative in no-use-before-define (fixes #10227) (#10396) (Toru Nagashima)
* 3721841 Docs: Add new experimental syntax policy to README (fixes #9804) (#10408) (Kevin Partington)
* d0aae3c Docs: Create docs landing page (#10453) (Kevin Partington)
* fe8bec3 Fix: fix writing config file when `source` is `prompt` (#10422) (Pig Fang)
* 917108d Update: Add requireParamType option to valid-jsdoc (fixes #6753) (#10220) (Tomasz Sterna)
* 1984c21 Docs: move custom parsers docs into a page (fixes #9919) (#10431) (Pig Fang)
* 400d4b5 Docs: Add rest and spread operator changes to migration guide (#10416) (Yannick Croissant)
* e7bdd02 Upgrade: Consume espree@4.0.0-alpha.1 (#10410) (Kevin Partington)
* 3e9f33a Fix: prevent crashing from JSON parsing error (fixes #10364) (#10376) (Pig Fang)
* 636457d Fix: parse later ES files in `eslint --init` (fixes #10003) (#10378) (Pig Fang)

v5.0.0-alpha.4 - May 28, 2018

* ce3e62a Docs: remove test coverage badge (#10407) (薛定谔的猫)
* 240c1a4 Fix: prefer-const object destructuring false positive (fixes #9108) (#10368) (Pig Fang)
* 93c9a52 Update: config-validator should validate overrides (#10357) (Toru Nagashima)
* c2e0398 Update: Improves the prefer-object-spread rule by removing extraneous visitors (#10351) (Sharmila Jesupaul)
* d848949 Update: Support JSXFragment node (fixes #9662) (#9664) (Clement Hoang)
* f268128 Build: add Node v10 to travis (#10262) (alberto)
* 9c922ce Update: Add "consistent" option to array-element-newline (fixes #9457) (#10355) (Pig Fang)
* 65bce3a Fix: ensure --stdin flag works when stdin is piped asynchronously (#10393) (Teddy Katz)
* b9b23a9 Chore: rm unused argument (#10400) (薛定谔的猫)
* 8b7a70c Fix: handle one-var with no semicolon (fixes #10330) (#10371) (Malcolm Groves)
* 465e615 New: prompt users before installing dependencies (#10353) (Pig Fang)
* e25fc22 Chore: remove assert.doesNotThrow in tests (#10199) (Ruben Bridgewater)
* fb148aa Fix: allow no tokens after `return` keyword (fixes #10372) (#10379) (Pig Fang)
* 074bc1c Docs: polish for max-classes-per-file rule (#10377) (Pig Fang)
* a812845 Fix: allow array spread for prefer-object-spread rule (fixes #10344) (#10347) (Pig Fang)
* 448fc52 Docs: Update link to Integrations / Build tools / Start (#10354) (Kir Belevich)
* 4e5e9be Chore: avoid unnecessary filesystem accesses during config search (#10359) (Teddy Katz)
* 363da01 Chore: avoid code duplication in rule severity checking (#10358) (Teddy Katz)

v5.0.0-alpha.3 - May 11, 2018

* 1a6b399 New: Adds prefer-object-spread rule (refs: #7230) (#9955) (Sharmila Jesupaul)
* c4109b2 New: add max-classes-per-file rule (#10163) (James Garbutt)
* 41f0f6e Breaking: report multiline eslint-disable-line directives (fixes #10334) (#10335) (Teddy Katz)
* 4ccd25a Chore: add eslint-plugin-node to eslint-config-eslint(fixes #10319) (#10320) (薛定谔的猫)
* 82757b2 Docs: Adding a little guidance to rule documentation (#10301) (Justin)
* 09dde26 Breaking: new object-curly-newline/no-self-assign default (fixes #10215) (#10337) (Teddy Katz)
* d65f11d Fix: correct comma fix in spare array (fixes #10273) (#10329) (Malcolm Groves)
* c343d86 Fix: do not autofix octal escape sequence (fixes #10031) (#10240) (Malcolm Groves)
* 514013c New: Add `globInputPaths` CLIEngine option (fixes #9972) (#10191) (Pierre Vanduynslager)
* 02e7b28 Chore: upgrade deps (#10339) (薛定谔的猫)
* 1397179 Chore: unskip test for scope analysis (#10336) (Teddy Katz)
* e5b33be Update: Add --fix for one-var rule (refs #9072) (#10040) (Sebastian Malton)
* 99b842d Chore: upgrade mock-fs@4.5.0 (#10325) (Tim Schaub)
* fe91859 Chore: Update issue templates with new format (#10309) (Ilya Volodin)
* 2f30aa5 Docs: add a better vim linting engine (#10292) (Jon Smithers)
* df2c1fb Docs: improve formatter guide (refs #9550) (#10294) (Dominic Lee)
* f7330c1 Chore: Add ESLint path to plugin-missing message (#10283) (Kevin Partington)
* bb6090f Fix: Throw error when --ignore-path not a file (fixes #10076) (#10205) (Malcolm Groves)
* 1b6b2b2 Build: remove trailing spaces in blogpost template (#10280) (Teddy Katz)
* a960d69 Docs: remove outdated notes from migration guide (#10279) (Teddy Katz)

v5.0.0-alpha.2 - April 27, 2018

* 510ca8b Docs: make grammatical tweaks in migration guide (#10278) (Teddy Katz)
* 02e44a5 Breaking: remove TDZ scopes (fixes #10245) (#10270) (Toru Nagashima)
* c74933b Breaking: remove extra check in getScope (fixes #10246, fixes #10247) (#10252) (Toru Nagashima)
* 7c2e83a Chore: improve tests and checking for equality (#10182) (Ruben Bridgewater)
* 8799972 Docs: make template link wording more clear (#10219) (David Luzar)
* 8b7c6ea Breaking: report fatal error for linting nonexistent files (fixes #7390) (#10143) (Teddy Katz)
* 9100819 Breaking: fix plugin resolver in extends (fixes #9904) (#10236) (Toru Nagashima)
* c45f1d0 Breaking: add rules to recommended (fixes #8865) (#10158) (薛定谔的猫)
* 1d443a0 Fix: valid-jsdoc does not know async function returns (fixes #9881) (#10161) (Rachael Sim)
* a82cbea Update: re-enable experimentalObjectRestSpread (fixes #9990) (#10230) (Toru Nagashima)
* f9c7371 Fix: do not autofix object-shorthand with comments (fixes #10038) (#10238) (Malcolm Groves)
* 4672b56 Docs: Correct wording in the `smart-tabs` docs page (#10277) (Jed Fox)
* b32d1f4 Chore: upgrade eslump@1.6.2 (#10258) (薛定谔的猫)
* 7938bf1 Chore: update eslint-fuzzer ecmaVersion to 2018 (#10255) (薛定谔的猫)
* a2953ec Chore: small opt to improve readability (#10225) (薛定谔的猫)
* 85a5191 Docs: Update JSCS FAQ (#10221) (alberto)
* 8e89d5c Docs: Fix typo (#10223) (alberto)
* c0c331e Docs: Add Prettier to FAQ (#10222) (alberto)
* 2443627 Docs: add backticks in getter-return (#10218) (薛定谔的猫)
* 74bb5b5 Docs: Fix misspelling in changelog (#10216) (Kevin Partington)

v5.0.0-alpha.1 - April 13, 2018

* b2a48a9 Breaking: stop using fake `context._linter` property (fixes #10140) (#10209) (Teddy Katz)
* a039956 Breaking: remove deprecated browser/jest/node globals (fixes #10141) (#10210) (Teddy Katz)
* 98f1cad Docs: update migration guide with latest changes (#10212) (Teddy Katz)
* 2e60017 Chore: remove concat-stream dependency (#10173) (Teddy Katz)
* 7f69f11 Chore:  rearrange init options. (#10131) (薛定谔的猫)
* f595fd8 Upgrade: upgrade deps (#10184) (alberto)
* 71167be Docs: fix wrong config in id-length (#10196) (薛定谔的猫)
* 81629d2 Chore: enable rest/spread rules on ESLint codebase (#10211) (Teddy Katz)
* 2324570 Breaking: no-unused-vars reports all after-used params (fixes #9909) (#10119) (Kevin Partington)
* 7765fc4 Upgrade: ajv@^6.0.1, still using json schema draft 04 (#9856) (Kevin Partington)
* b77846d Breaking: drop supporting Node.js 4 (fixes #10052) (#10074) (薛定谔的猫)
* cd34d44 Chore: avoid modifying global state when tests fail (#10201) (Teddy Katz)
* 731da1e Docs: fix code in correct example. (#10195) (薛定谔的猫)
* 3780915 Docs: fix some small errors in examples (#10194) (薛定谔的猫)
* 869c9f5 Upgrade: babelify (#10185) (alberto)
* 218ee57 Fix: report no-case-declarations from declarations (fixes #10048) (#10167) (Carlo Abelli)
* b7ee1ed Upgrade: upgrade devdeps (#10178) (alberto)
* db1a582 Chore: Add debug logging for CLI args as they came in (#10174) (Kevin Partington)
* f3a0291 Upgrade: Update dependencies. (#10168) (alberto)
* 7d6e052 Upgrade: esquery@^1.0.1 (fixes #8733) (#10170) (Kevin Partington)
* 1e7252f Docs: Add more related rules for object-curly-spacing (#10175) (Saugat Acharya)
* e5cf9cc Docs: Reorder README sections (#10172) (alberto)
* c85578f Chore: Remove `esprima-fb` dependency. (#10171) (alberto)
* d0dc2e3 Docs: Add Missing Quotes (#10162) (Samarth Verma)
* 7a63bfa Upgrade: eslint-release to v0.11.1 (#10156) (Teddy Katz)
* b7a1a7a Build: Gensite creates prerelease dirs if needed (#10154) (Brandon Mills)

v5.0.0-alpha.0 - March 30, 2018

* f4b3af5 Breaking: Upgrade to Espree v4 alpha (refs #9990) (#10152) (Brandon Mills)
* 3351129 Docs: add v5.0.0 migration guide (fixes #10142) (#10147) (Teddy Katz)
* f2f98dd Build: make prerelease script publish to GitHub/website (#10151) (Teddy Katz)
* d440e84 Breaking: support @scope shorthand in plugins (fixes #9903) (#9905) (Toru Nagashima)
* 462b058 Update: Include debugging information when rule throws error (#9742) (Patrick Hayes)
* 9a020dc Chore: refactor --no-ignore flag logic (#10146) (Teddy Katz)
* 4f61a0d Chore: add noopener/noreferrer (薛定谔的猫)
* 65cc834 Docs: Ensure CLI doc sections match command line help order (#10144) (Kevin Partington)
* 9c79174 Docs: Update capitalized-comments with missing letters (fixes #10135) (#10134) (jasonfry)
* 9e66bfb Docs: remove eslint vs jshint from faq (#10108) (alberto)
* 692e383 Docs: Add modified variable examples for no-loop-func (fixes #9527) (#10098) (Rachael Sim)
* a9ee9ae Breaking: require rules to provide report messages (fixes #10011) (#10057) (Teddy Katz)
* 837edc7 Chore: Uncommented test for empty program for no-invalid-meta (#10046) (Kevin Partington)
* c383bc5 Breaking: Make require('eslint').linter non-enumerable (fixes #9270) (#9692) (Jed Fox)
* 4eaebe5 Breaking: set `parent` of AST nodes before rules run (fixes #9122) (#10014) (Teddy Katz)
* 91ece32 Breaking: remove special exception for linting empty files (fixes #9534) (#10013) (Teddy Katz)
* 27e3f24 Breaking: remove `source` property from linting messages (fixes #7358) (#10012) (Teddy Katz)
* e4c3b3c Breaking: use an exit code of 2 for fatal config problems (fixes #9384) (#10009) (Teddy Katz)
* 2a7ecaa Breaking: Use strict equality in RuleTester comparisons (fixes #9417) (#10008) (Teddy Katz)
* 0bc4a38 Fix: Make rule-tester strictly check messageId. (ref #9890) (#9908) (Jacques Favreau)
* ea6fb17 Update: Make no-cond-assign work for ternaries (fixes #10091) (#10109) (Aaron Harper)

v4.19.1 - March 21, 2018

* 3ff5d11 Fix: no-invalid-regexp not understand variable for flags (fixes #10112) (#10113) (薛定谔的猫)
* abc765c Fix: object-curly-newline minProperties w/default export (fixes #10101) (#10103) (Kevin Partington)
* 6f9e155 Docs: Update ambiguous for...in example for guard-for-in (#10114) (CJ R)
* 0360cc2 Chore: Adding debug logs on successful plugin loads (#10100) (Kevin Partington)
* a717c5d Chore: Adding log at beginning of unit tests in Makefile.js (#10102) (Kevin Partington)

v4.19.0 - March 16, 2018

* 55a1593 Update: consecutive option for one-var (fixes #4680) (#9994) (薛定谔的猫)
* 8d3814e Fix: false positive about ES2018 RegExp enhancements (fixes #9893) (#10062) (Toru Nagashima)
* 935f4e4 Docs: Clarify default ignoring of node_modules (#10092) (Matijs Brinkhuis)
* 72ed3db Docs: Wrap `Buffer()` in backticks in `no-buffer-constructor` rule description (#10084) (Stephen Edgar)
* 3aded2f Docs: Fix lodash typos, make spacing consistent (#10073) (Josh Smith)
* e33bb64 Chore: enable no-param-reassign on ESLint codebase (#10065) (Teddy Katz)
* 66a1e9a Docs: fix possible typo (#10060) (Vse Mozhet Byt)
* 2e68be6 Update: give a node at least the indentation of its parent (fixes #9995) (#10054) (Teddy Katz)
* 72ca5b3 Update: Correctly indent JSXText with trailing linebreaks (fixes #9878) (#10055) (Teddy Katz)
* 2a4c838 Docs: Update ECMAScript versions in FAQ (#10047) (alberto)

v4.18.2 - March 2, 2018

* 6b71fd0 Fix: table@4.0.2, because 4.0.3 needs "ajv": "^6.0.1" (#10022) (Mathieu Seiler)
* 3c697de Chore: fix incorrect comment about linter.verify return value (#10030) (Teddy Katz)
* 9df8653 Chore: refactor parser-loading out of linter.verify (#10028) (Teddy Katz)
* f6901d0 Fix: remove catastrophic backtracking vulnerability (fixes #10002) (#10019) (Jamie Davis)
* e4f52ce Chore: Simplify dataflow in linter.verify (#10020) (Teddy Katz)
* 33177cd Chore: make library files non-executable (#10021) (Teddy Katz)
* 558ccba Chore: refactor directive comment processing (#10007) (Teddy Katz)
* 18e15d9 Chore: avoid useless catch clauses that just rethrow errors (#10010) (Teddy Katz)
* a1c3759 Chore: refactor populating configs with defaults in linter (#10006) (Teddy Katz)
* aea07dc Fix: Make max-len ignoreStrings ignore JSXText (fixes #9954) (#9985) (Rachael Sim)

v4.18.1 - February 20, 2018

* f417506 Fix: ensure no-await-in-loop reports the correct node (fixes #9992) (#9993) (Teddy Katz)
* 3e99363 Docs: Fixed typo in key-spacing rule doc (#9987) (Jaid)
* 7c2cd70 Docs: deprecate experimentalObjectRestSpread (#9986) (Toru Nagashima)

v4.18.0 - February 16, 2018

* 70f22f3 Chore: Apply memoization to config creation within glob utils (#9944) (Kenton Jacobsen)
* 0e4ae22 Update: fix indent bug with binary operators/ignoredNodes (fixes #9882) (#9951) (Teddy Katz)
* 47ac478 Update: add named imports and exports for object-curly-newline (#9876) (Nicholas Chua)
* e8efdd0 Fix: support Rest/Spread Properties (fixes #9885) (#9943) (Toru Nagashima)
* f012b8c Fix: support Async iteration (fixes #9891) (#9957) (Toru Nagashima)
* 74fa253 Docs: Clarify no-mixed-operators options (fixes #9962) (#9964) (Ivan Hayes)
* 426868f Docs: clean up key-spacing docs (fixes #9900) (#9963) (Abid Uzair)
* 4a6f22e Update: support eslint-disable-* block comments (fixes #8781) (#9745) (Erin)
* 777283b Docs: Propose fix typo for function (#9965) (John Eismeier)
* bf3d494 Docs: Fix typo in max-len ignorePattern example. (#9956) (Tim Martin)
* d64fbb4 Docs: fix typo in prefer-destructuring.md example (#9930) (Vse Mozhet Byt)
* f8d343f Chore: Fix default issue template (#9946) (Kai Cataldo)

v4.17.0 - February 2, 2018

* 1da1ada Update: Add "multiline" type to padding-line-between-statements (#8668) (Matthew Bennett)
* bb213dc Chore: Use messageIds in some of the core rules (#9648) (Jed Fox)
* 1aa1970 Docs: remove outdated rule naming convention (#9925) (Teddy Katz)
* 3afaff6 Docs: Add prefer-destructuring variable reassignment example (#9873) (LePirlouit)
* d20f6b4 Fix: Typo in error message when running npm (#9866) (Maciej Kasprzyk)
* 51ec6a7 Docs: Use GitHub Multiple PR/Issue templates (#9911) (Kai Cataldo)
* dc80487 Update: space-unary-ops uses astUtils.canTokensBeAdjacent (fixes #9907) (#9906) (Kevin Partington)
* 084351b Docs: Fix the messageId example (fixes #9889) (#9892) (Jed Fox)
* 9cbb487 Docs: Mention the `globals` key in the no-undef docs (#9867) (Dan Dascalescu)

v4.16.0 - January 19, 2018

* e26a25f Update: allow continue instead of if wrap in guard-for-in (fixes #7567) (#9796) (Michael Ficarra)
* af043eb Update: Add NewExpression support to comma-style (#9591) (Frazer McLean)
* 4f898c7 Build: Fix JSDoc syntax errors (#9813) (Matija Marohnić)
* 13bcf3c Fix: Removing curly quotes in no-eq-null report message (#9852) (Kevin Partington)
* b96fb31 Docs: configuration hierarchy for CLIEngine options (fixes #9526) (#9855) (PiIsFour)
* 8ccbdda Docs: Clarify that -c configs merge with `.eslintrc.*` (fixes #9535) (#9847) (Kevin Partington)
* 978574f Docs: Fix examples for no-useless-escape (#9853) (Toru Kobayashi)
* cd5681d Chore: Deactivate consistent-docs-url in internal rules folder (#9815) (Kevin Partington)
* 2e87ddd Docs: Sync messageId examples' style with other examples (#9816) (Kevin Partington)
* 1d61930 Update: use doctrine range information in valid-jsdoc (#9831) (Teddy Katz)
* 133336e Update: fix indent behavior on template literal arguments (fixes #9061) (#9820) (Teddy Katz)
* ea1b15d Fix: avoid crashing on malformed configuration comments (fixes #9373) (#9819) (Teddy Katz)
* add1e70 Update: fix indent bug on comments in ternary expressions (fixes #9729) (#9818) (Teddy Katz)
* 6a5cd32 Fix: prefer-destructuring error with computed properties (fixes #9784) (#9817) (Teddy Katz)
* 601f851 Docs: Minor modification to code comments for clarity (#9821) (rgovind92)
* b9da067 Docs: fix misleading info about RuleTester column numbers (#9830) (Teddy Katz)
* 2cf4522 Update: Rename and deprecate object-property-newline option (#9570) (Jonathan Pool)
* acde640 Docs: Add ES 2018 to Configuring ESLint (#9829) (Kai Cataldo)
* ccfce15 Docs: Minor tweaks to working with rules page (#9824) (Kevin Partington)
* 54b329a Docs: fix substitution of {{ name }} (#9822) (Andres Kalle)

v4.15.0 - January 6, 2018

* 6ab04b5 New: Add context.report({ messageId }) (fixes #6740) (#9165) (Jed Fox)
* fc7f404 Docs: add url to each of the rules (refs #6582) (#9788) (Patrick McElhaney)
* fc44da9 Docs: fix sort-imports rule block language (#9805) (ferhat elmas)
* 65f0176 New: CLIEngine#getRules() (refs #6582) (#9782) (Patrick McElhaney)
* c64195f Update: More detailed assert message for rule-tester (#9769) (Weijia Wang)
* 9fcfabf Fix: no-extra-parens false positive (fixes: #9755) (#9795) (Erin)
* 61e5fa0 Docs: Add table of contents to Node.js API docs (#9785) (Patrick McElhaney)
* 4c87f42 Fix: incorrect error messages of no-unused-vars (fixes #9774) (#9791) (akouryy)
* bbabf34 Update: add `ignoreComments` option to `indent` rule (fixes #9018) (#9752) (Kevin Partington)
* db431cb Docs: HTTP -> HTTPS (fixes #9768) (#9768) (Ronald Eddy Jr)
* cbf0fb9 Docs: describe how to feature-detect scopeManager/visitorKeys support (#9764) (Teddy Katz)
* f7dcb70 Docs: Add note about "patch release pending" label to maintainer guide (#9763) (Teddy Katz)

v4.14.0 - December 23, 2017

* be2f57e Update: support separate requires in one-var. (fixes #6175) (#9441) (薛定谔的猫)
* 370d614 Docs: Fix typos (#9751) (Jed Fox)
* 8196c45 Chore: Reorganize CLI options and associated docs (#9758) (Kevin Partington)
* 75c7419 Update: Logical-and is counted in `complexity` rule (fixes #8535) (#9754) (Kevin Partington)
* eb4b1e0 Docs: reintroduce misspelling in `valid-typeof` example (#9753) (Teddy Katz)
* ae51eb2 New: Add allowImplicit option to array-callback-return (fixes #8539) (#9344) (James C. Davis)
* e9d5dfd Docs: improve no-extra-parens formatting (#9747) (Rich Trott)
* 37d066c Chore: Add unit tests for overrides glob matching. (#9744) (Robert Jackson)
* 805a94e Chore: Fix typo in CLIEngine test name (#9741) (@scriptdaemon)
* 1c2aafd Update: Improve parser integrations (fixes #8392) (#8755) (Toru Nagashima)
* 4ddc131 Upgrade: debug@^3.1.0 (#9731) (Kevin Partington)
* f252c19 Docs: Make the lint message `source` property a little more subtle (#9735) (Jed Fox)
* 5a5c23c Docs: fix the link to contributing page (#9727) (Victor Hom)
* f44ce11 Docs: change beginner to good first issue label text (#9726) (Victor Hom)
* 14baa2e Chore: improve arrow-body-style error message (refs #5498) (#9718) (Teddy Katz)
* f819920 Docs: fix typos (#9723) (Thomas Broadley)
* 43d4ba8 Fix: false positive on rule`lines-between-class-members` (fixes #9665) (#9680) (sakabar)

v4.13.1 - December 11, 2017

* b72dc83 Fix: eol-last allow empty-string to always pass (refs #9534) (#9696) (Kevin Partington)
* d80aa7c Fix: camelcase destructure leading/trailing underscore (fixes #9700) (#9701) (Kevin Partington)
* d49d9d0 Docs: Add missing period to the README (#9702) (Kevin Partington)
* 4564fe0 Chore: no-invalid-meta crash if no export assignment (refs #9534) (#9698) (Kevin Partington)

v4.13.0 - December 8, 2017

* 256481b Update: update handling of destructuring in camelcase (fixes #8511) (#9468) (Erin)
* d067ae1 Docs: Don’t use undocumented array-style configuration for max-len (#9690) (Jed Fox)
* 1ad3091 Chore: fix test-suite to work with node master (#9688) (Myles Borins)
* cdb1488 Docs: Adds an example with try/catch. (#9672) (Jaap Taal)

v4.12.1 - November 30, 2017

* 1e362a0 Revert "Fix: Use XML 1.1 on XML formatters (fixes #9607) (#9608)" (#9667) (Kevin Partington)

v4.12.0 - November 25, 2017

* 76dab18 Upgrade: doctrine@^2.0.2 (#9656) (Kevin Partington)
* 28c9c8e New: add a Linter#defineParser function (#9321) (Ives van Hoorne)
* 5619910 Update: Add autofix for `sort-vars` (#9496) (Trevin Hofmann)
* 71eedbf Update: add `beforeStatementContinuationChars` to semi (fixes #9521) (#9594) (Toru Nagashima)
* 4118f14 New: Adds implicit-arrow-linebreak rule (refs #9510) (#9629) (Sharmila Jesupaul)
* 208fb0f Fix: Use XML 1.1 on XML formatters (fixes #9607) (#9608) (Daniel Reigada)
* 6e04f14 Upgrade: `globals` to 11.0.1 (fixes #9614) (#9632) (Toru Nagashima)
* e13d439 Fix: space-in-parens crash (#9655) (Toru Nagashima)
* 92171cc Docs: Updating migration guide for single-line disable (#9385) (Justin Helmer)
* f39ffe7 Docs: remove extra punctuation from readme (#9640) (Teddy Katz)
* a015234 Fix: prefer-destructuring false positive on "super" (fixes #9625) (#9626) (Kei Ito)
* 0cf081e Update: add importNames option to no-restricted-imports (#9506) (Benjamin R Gibson)
* 332c214 Docs: Add @platinumazure to TSC (#9618) (Ilya Volodin)

v4.11.0 - November 10, 2017

* d4557a6 Docs: disallow use of the comma operator using no-restricted-syntax (#9585) (薛定谔的猫)
* d602f9e Upgrade: espree v3.5.2 (#9611) (Kai Cataldo)
* 4def876 Chore: avoid handling rules instances in config-validator (#9364) (Teddy Katz)
* fe5ac7e Chore: fix incorrect comment in safe-emitter.js (#9605) (Teddy Katz)
* 6672fae Docs: Fixed a typo on lines-between-class-members doc (#9603) (Moinul Hossain)
* 980ecd3 Chore: Update copyright and license info (#9599) (薛定谔的猫)
* cc2c7c9 Build: use Node 8 in appveyor (#9595) (薛定谔的猫)
* 2542f04 Docs: Add missing options for `lines-around-comment` (#9589) (Clément Fiorio)
* b6a7490 Build: ensure fuzzer tests get run with `npm test` (#9590) (Teddy Katz)
* 1073bc5 Build: remove shelljs-nodecli (refs #9533) (#9588) (Teddy Katz)
* 7e3bf6a Fix: edge-cases of semi-style (#9560) (Toru Nagashima)
* e5a37ce Fix: object-curly-newline for flow code (#9458) (Tiddo Langerak)
* 9064b9c Chore: add equalTokens in ast-utils. (#9500) (薛定谔的猫)
* b7c5b19 Fix: Correct [object Object] output of error.data. (#9561) (Jonathan Pool)
* 51c8cf0 Docs: Disambiguate definition of Update tag (#9584) (Jonathan Pool)
* afc3c75 Docs: clarify what eslint-config-eslint is (#9582) (Teddy Katz)
* aedae9d Docs: fix spelling in valid-typeof example (#9574) (Maksim Degtyarev)
* 4c5aaf3 Docs: Fix typo in no-underscore-dangle rule (#9567) (Fabien Lucas)
* 3623600 Chore: upgrade ajv@5.3.0 (#9557) (薛定谔的猫)
* 1b606cd Chore: Remove an indirect dependency on jsonify (#9444) (Rouven Weßling)
* 4d7d7ab Update: Resolve npm installed formatters (#5900) (#9464) (Tom Erik Støwer)
* accc490 Fix: Files with no failures get "passing" testcase (#9547) (Samuel Levy)
* ab0f66d Docs: Add examples to better show rule coverage. (#9548) (Jonathan Pool)
* 88d2303 Chore: Add object-property-newline tests to increase coverage. (#9553) (Jonathan Pool)
* 7f37b1c Build: test Node 9 on Travis (#9556) (Teddy Katz)
* acccfbd Docs: Minor rephrase in `no-invalid-this`. (#9542) (Francisc)
* 8f9c0fe Docs: improve id-match usage advice (#9544) (Teddy Katz)
* a9606a3 Fix: invalid tests with super (fixes #9539) (#9545) (Teddy Katz)
* 8e1a095 Chore: enable a modified version of multiline-comment-style on codebase (#9452) (Teddy Katz)
* cb60285 Chore: remove commented test for HTML formatter (#9532) (Teddy Katz)
* 06b491e Docs: fix duplicate entries in changelog (#9530) (Teddy Katz)
* 2224733 Chore: use eslint-plugin-rulesdir instead of --rulesdir for self-linting (#9164) (Teddy Katz)
* 9cf4ebe Docs: add .md to link(for github users) (#9529) (薛定谔的猫)

v4.10.0 - October 27, 2017

* bb6e60a Fix: Improve the doc for no-restricted-modules rule (fixes #9437) (#9495) (vibss2397)
* c529de9 Docs: Amend rule document to correct and complete it (refs #6251). (#9498) (Jonathan Pool)
* f9c6673 Chore: Add tests to cover array and object values and leading commas. (#9502) (Jonathan Pool)
* 9169258 Chore: remove `npm run check-commit` script (#9513) (Teddy Katz)
* 7d390b2 Docs: Revise contributor documentation on issue labels. (#9469) (Jonathan Pool)
* d80b9d0 Fix: no-var don't fix globals (fixes #9520) (#9525) (Toru Nagashima)
* b8aa071 Fix: allow linting the empty string from stdin (fixes #9515) (#9517) (Teddy Katz)
* 350a72c Chore: regex.test => string.startsWith (#9518) (薛定谔的猫)
* de0bef4 Chore: remove obsolete eslintbot templates (#9512) (Teddy Katz)
* 720b6d5 Docs: Update ISSUE_TEMPLATE.md (#9504) (薛定谔的猫)
* 2fa64b7 Fix: should not convert non-consecutive line comments to a single blo… (#9475) (薛定谔的猫)
* 9725146 Fix: multiline-comment-style fix produces invalid code (fixes #9461). (#9463) (薛定谔的猫)
* b12cff8 Fix: Expected order of jsdoc tags (fixes #9412) (#9451) (Orlando Wenzinger)
* f054ab5 Docs: add `.md` to link (for github users) (#9501) (薛定谔的猫)
* 5ed9cfc Docs: Correct violations of “Variable Declarations” in Code Conventions (#9447) (Jonathan Pool)
* 3171097 Docs: Clears confusion on usage of global and local plugins.(#9492) (Vasili Sviridov)
* 3204773 Chore: enable max-len. (#9414) (薛定谔的猫)
* 0f71fef Docs: Unquote booleans in lines-between-class-members docs (#9497) (Brandon Mills)
* b3d7532 Docs: use consistent terminology & fix link etc. (#9490) (薛定谔的猫)
* 87db8ae Docs: Fix broken links (#9488) (gpiress)
* 51bdb2f Docs: Incorrect link to related rule (#9477) (Gavin King)
* 1a962e8 Docs: Add FAQ for when ESLint cannot find plugin (#9467) (Kevin Partington)
* 8768b2d Fix: multiline-comment-style autofixer added trailing space (#9454) (Teddy Katz)
* e830aa1 Fix: multiline-comment-style reports block comments followed by code (#9450) (Teddy Katz)
* b12e5fe Docs: Repair broken links and add migration links. (#9473) (Jonathan Pool)
* eca01ed Docs: Add missing info about special status of home-dir config files. (#9472) (Jonathan Pool)
* eb8cfb1 Fix: change err report in constant condition (fixes #9398) (#9436) (Victor Hom)
* da77eb4 Chore: Revise no-config-file test to prevent false failure. (#9443) (Jonathan Pool)
* 47e5f6f Docs: ensure "good commit message" examples actually follow guidelines (#9466) (Teddy Katz)
* ebb530d Update: Don't ignore comments (no-trailing-spaces) (#9416) (Chris van Marle)
* 5012661 Build: fix `npm run profile` script (fixes #9397) (#9455) (Teddy Katz)
* ecac0fd Docs: Remove blockBindings references (#9446) (Jan Pilzer)
* 0b89865 Chore: ensure tests for internal rules get run (#9453) (Teddy Katz)
* 052c504 Docs: suggest deleting branches after merging PRs (#9449) (Teddy Katz)
* b31e55a Chore: move internal rules out of lib/ (#9448) (Teddy Katz)
* a7521e3 Docs: improve examples for multiline-comment-style (#9440) (Teddy Katz)

v4.9.0 - October 14, 2017

* 85388fb Fix: Correct error and test messages to fit config search path (#9428) (Jonathan Pool)
* 62a323c Fix: Add class options for `lines-around-comment` (fixes #8564) (#8565) (Ed Lee)
* 8eb4aae New: multiline-comment-style rule (fixes #8320) (#9389) (薛定谔的猫)
* db41408 Chore: avoid applying eslint-env comments twice (#9278) (Teddy Katz)
* febb897 Chore: avoid loose equality assertions (#9415) (Teddy Katz)
* 2247efa Update: Add FunctionExpression to require-jsdoc (fixes #5867) (#9395) (Kai Cataldo)
* 6791d18 Docs: Corrected noun to verb. (#9438) (Jonathan Pool)
* b02fbb6 Update: custom messages for no-restricted-* (refs #8400) (Maja Wichrowska)
* 02732bd Docs: Reorganized to avoid misunderstandings. (#9434) (Jonathan Pool)
* d9466b8 Docs: Correct time forecast for tests. (#9432) (Jonathan Pool)
* f7ed84f Docs: Add instruction re home-directory config files (refs #7729) (#9426) (Jonathan Pool)
* 30d018b Chore: Add Aladdin-ADD & VictorHom to README (#9424) (Kai Cataldo)
* 2d8a303 Docs: fix examples for prefer-numeric-literals (#9155) (Lutz Lengemann)
* d7610f5 Docs: Add jquery warning to prefer-destructuring (#9409) (Thomas Grainger)
* e835dd1 Docs: clarify no-mixed-operators (fixes #8051) (Ruxandra Fediuc)
* 51360c8 Docs: update block-spacing details (fixes #8743) (#9375) (Victor Hom)
* 6767857 Update: fix ignored nodes in indent rule when using tabs (fixes #9392) (#9393) (Robin Houston)
* 37dde77 Chore: Refactor SourceCode#getJSDocComment (#9403) (Kai Cataldo)
* 9fedd51 Chore: Add missing space in blog post template (#9407) (Kevin Partington)
* 7654c99 Docs: add installing prerequisites in readme. (#9401) (薛定谔的猫)
* 786cc73 Update: Add "consistent" option to array-bracket-newline (fixes #9136) (#9206) (Ethan Rutherford)
* e171f6b Docs: add installing prerequisites. (#9394) (薛定谔的猫)
* 74dfc87 Docs: update doc for class-methods-use-this (fixes #8910) (#9374) (Victor Hom)
* b4a9dbf Docs: show console call with no-restricted-syntax (fixes #7806) (#9376) (Victor Hom)
* 8da525f Fix: recognise multiline comments as multiline arrays (fixes #9211) (#9369) (Phil Quinn)
* c581b77 Chore: Error => TypeError (#9390) (薛定谔的猫)
* ee99876 New: lines-between-class-members rule (fixes #5949) (#9141) (薛定谔的猫)
* 9d3f5ad Chore: report unused eslint-disable directives in ESLint codebase (#9371) (Teddy Katz)
* 1167638 Update: add allowElseIf option to no-else-return (fixes #9228) (#9229) (Thomas Grainger)
* 4567ab1 New: Add the fix-dry-run flag (fixes #9076) (#9073) (Rafał Ruciński)

v4.8.0 - September 29, 2017

* 3f2b908 New: add option to report unused eslint-disable directives (fixes #9249) (#9250) (Teddy Katz)
* ff2be59 Fix: dot notation rule failing to catch string template (fixes #9350) (#9357) (Phil Quinn)
* b1372da Chore: remove sourceCode property from Linter (refs #9161) (#9363) (Teddy Katz)
* cef6f8c Docs: remove line about removing rules from semver policy (#9367) (Teddy Katz)
* 06efe87 Fix: Add meta element with charset attribute. (#9365) (H1Gdev)
* 458ca67 Docs: update architecture page (fixes #9337) (#9345) (Victor Hom)
* 1c6bc67 Fix: special EventEmitter keys leak information about other rules (#9328) (Teddy Katz)
* d593e61 Docs: update eslint.org links to use https (#9358) (Teddy Katz)
* 38d0cb2 Fix: fix wrong code-path about try-for-in (fixes #8848) (#9348) (Toru Nagashima)
* 434d9e2 Fix: Invalid font-size property value issue. (#9341) (H1Gdev)
* a7668c2 Chore: Remove unnecessary slice from logging utility (#9343) (Gyandeep Singh)
* 2ff6fb6 Chore: remove unused arguments in codebase (#9340) (Teddy Katz)

v4.7.2 - September 21, 2017

* 4f87732 Fix: Revert setting node.parent early (fixes #9331) (#9336) (Teddy Katz)

v4.7.1 - September 18, 2017

* 08656db Fix: Handle nested disable directive correctly (fixes #9318) (#9322) (Gyandeep Singh)
* 9226495 Revert "Chore: rewrite parseListConfig for a small perf gain." (#9325) (薛定谔的猫)

v4.7.0 - September 15, 2017

* 787b78b Upgrade: Espree v3.5.1 (fixes #9153) (#9314) (Brandon Mills)
* 1488b51 Update: run rules after `node.parent` is already set (fixes #9122) (#9283) (Teddy Katz)
* 4431d68 Docs: fix wrong config in max-len example. (#9309) (薛定谔的猫)
* 7d24dde Docs: Fix code snippet to refer to the correct option (#9313) (Ruben Tytgat)
* 12388d4 Chore: rewrite parseListConfig for a small perf gain. (#9300) (薛定谔的猫)
* ce1f084 Update: fix MemberExpression handling in no-extra-parens (fixes #9156) (jackyho112)
* 0c720a3 Update: allow autofixing when using processors (fixes #7510) (#9090) (Teddy Katz)
* 838df76 Chore: upgrade deps. (#9289) (薛定谔的猫)
* f12def6 Update: indent flatTernary option to handle `return` (fixes #9285) (#9296) (Teddy Katz)
* e220687 Fix: remove autofix for var undef inits (fixes #9231) (#9288) (Victor Hom)
* 002e199 Docs: fix no-restricted-globals wrong config. (#9305) (薛定谔的猫)
* fcfe91a Docs: fix wrong config in id-length example. (#9303) (薛定谔的猫)
* 2731f94 Update: make newline-per-chained-call fixable (#9149) (João Granado)
* 61f1093 Chore: avoid monkeypatching Linter instances in RuleTester (#9276) (Teddy Katz)
* 28929cb Chore: remove Linter#reset (refs #9161) (#9268) (Teddy Katz)
* abc8634 Build: re-run browserify when generating site (#9275) (Teddy Katz)
* 7685fed Fix: IIFE and arrow functions in no-invalid-this (fixes #9126) (#9258) (Toru Nagashima)
* 2b1eba2 Chore: enable eslint-plugin/no-deprecated-context-methods (#9279) (Teddy Katz)
* 981f933 Fix: reuse the AST of source code object in verify (#9256) (Toru Nagashima)
* cd698ba Docs: move RuleTester documentation to Node.js API page (#9273) (Teddy Katz)
* 4ae7ad3 Docs: fix inaccuracy in `npm run perf` description (#9274) (Teddy Katz)
* cad45bd Docs: improve documentation for rule contexts (#9272) (Teddy Katz)
* 3b0c6fd Chore: remove extraneous linter properties (refs #9161) (#9267) (Teddy Katz)
* c3231b3 Docs: Fix typo in array-bracket-newline.md (#9269) (宋文强)
* 51132d6 Fix: Formatters keep trailing '.' if preceded by a space (fixes #9154) (#9247) (i-ron-y)
* 88d5d4d Chore: remove undocumented Linter#markVariableAsUsed method (refs #9161) (#9266) (Teddy Katz)
* 09414cf Chore: remove internal Linter#getDeclaredVariables method (refs #9161) (#9264) (Teddy Katz)
* f31f59d Chore: prefer smaller scope for variables in codebase (#9265) (Teddy Katz)
* 3693e4e Chore: remove undocumented Linter#getScope method (#9253) (Teddy Katz)
* 5d7eb81 Chore: refactor config hash caching in CLIEngine (#9260) (Teddy Katz)
* 1a76c4d Chore: remove SourceCode passthroughs from Linter.prototype (refs #9161) (#9263) (Teddy Katz)
* 40ae27b Chore: avoid relying on Linter#getScope/markVariableAsUsed in tests (#9252) (Teddy Katz)
* b383d81 Chore: make executeOnFile a pure function in CLIEngine (#9262) (Teddy Katz)
* 5e0e579 Chore: avoid internal SourceCode methods in Linter tests (refs #9161) (#9223) (Teddy Katz)
* adab827 Chore: remove unused eslint-disable comment (#9251) (Teddy Katz)
* 31e4ec8 Chore: use consistent names for apply-disable-directives in tests (#9246) (Teddy Katz)
* 7ba46e6 Fix: shebang error in eslint-disable-new-line; add tests (fixes #9238) (#9240) (i-ron-y)
* 8f6546c Chore: remove undocumented defaults() method (refs #9161) (#9237) (Teddy Katz)
* 82d8b73 Docs: Fix error in example code for sort-imports (fixes #8734) (#9245) (i-ron-y)
* a32ec36 Update: refactor eslint-disable comment processing (#9216) (Teddy Katz)
* 583f0b8 Chore: avoid using globals in CLIEngine tests (#9242) (Teddy Katz)
* c8bf687 Chore: upgrade eslint-plugin-eslint-plugin@1.0.0 (#9234) (薛定谔的猫)
* 3c41a05 Chore: always normalize rules to new API in rules.js (#9236) (Teddy Katz)
* c5f4227 Chore: move logic for handling missing rules to rules.js (#9235) (Teddy Katz)
* bf1e344 Chore: create report translators lazily (#9221) (Teddy Katz)
* 2eedc1f Chore: remove currentFilename prop from Linter instances (refs #9161) (#9219) (Teddy Katz)
* 5566e94 Docs: Replace misleading CLA links (#9133) (#9232) (i-ron-y)
* c991630 Chore: remove ConfigOps.normalize in favor of ConfigOps.getRuleSeverity (#9224) (Teddy Katz)
* 171962a Chore: remove internal Linter#getAncestors helper (refs #9161) (#9222) (Teddy Katz)
* a567499 Chore: avoid storing list of problems on Linter instance (refs #9161) (#9214) (Teddy Katz)
* ed6d088 Chore: avoid relying on undocumented Linter#getFilename API in tests (#9218) (Teddy Katz)

v4.6.1 - September 3, 2017

* bdec46d Build: avoid process leak when generating website (#9217) (Teddy Katz)
* cb74b87 Fix: avoid adding globals when an env is used with `false` (fixes #9202) (#9203) (Teddy Katz)
* f9b7544 Docs: Correct a typo in generator-star-spacing documentation (#9205) (Ethan Rutherford)
* e5c5e83 Build: Fixing issue with docs generation (Fixes #9199) (#9200) (Ilya Volodin)

v4.6.0 - September 1, 2017

* 56dd769 Docs: fix link format in prefer-arrow-callback.md (#9198) (Vse Mozhet Byt)
* 6becf91 Update: add eslint version to error output. (fixes #9037) (#9071) (薛定谔的猫)
* 0e09973 New: function-paren-newline rule (fixes #6074) (#8102) (Teddy Katz)
* 88a64cc Chore: Make parseJsonConfig() a pure function in Linter (#9186) (Teddy Katz)
* 1bbac51 Fix: avoid breaking eslint-plugin-eslint-comments (fixes #9193) (#9196) (Teddy Katz)
* 3e8b70a Fix: off-by-one error in eslint-disable comment checking (#9195) (Teddy Katz)
* 73815f6 Docs: rewrite prefer-arrow-callback documentation (fixes #8950) (#9077) (Charles E. Morgan)
* 0d3a854 Chore: avoid mutating report descriptors in report-translator (#9189) (Teddy Katz)
* 2db356b Update: no-unused-vars Improve message to include the allowed patterns (#9176) (Eli White)
* 8fbaf0a Update: Add configurability to generator-star-spacing (#8985) (Ethan Rutherford)
* 8ed779c Chore: remove currentScopes property from Linter instances (refs #9161) (#9187) (Teddy Katz)
* af4ad60 Fix: Handle error when running init without npm (#9169) (Gabriel Aumala)
* 4b94c6c Chore: make parse() a pure function in Linter (refs #9161) (#9183) (Teddy Katz)
* 1be5634 Chore: don't make Linter a subclass of EventEmitter (refs #9161) (#9177) (Teddy Katz)
* e95af9b Chore: don't include internal test helpers in npm package (#9160) (Teddy Katz)
* 6fb32e1 Chore: avoid using private Linter APIs in astUtils tests (refs #9161) (#9173) (Teddy Katz)
* de6dccd Docs: add documentation for Linter methods (refs #6525) (#9151) (Teddy Katz)
* 2d90030 Chore: remove unused assignment. (#9182) (薛定谔的猫)
* d672aef Chore: refactor reporting logic (refs #9161) (#9168) (Teddy Katz)
* 5ab0434 Fix: indent crash on sparse arrays with "off" option (fixes #9157) (#9166) (Teddy Katz)
* c147b97 Chore: Make SourceCodeFixer accept text instead of a SourceCode instance (#9178) (Teddy Katz)
* f127423 Chore: avoid using private Linter APIs in Linter tests (refs #9161) (#9175) (Teddy Katz)
* 2334335 Chore: avoid using private Linter APIs in SourceCode tests (refs #9161) (#9174) (Teddy Katz)
* 2dc243a Chore: avoid using internal Linter APIs in RuleTester (refs #9161) (#9172) (Teddy Katz)
* d6e436f Fix: no-extra-parens reported some parenthesized IIFEs (fixes #9140) (#9158) (Teddy Katz)
* e6b115c Build: Add an edit link to the rule docs’ metadata (#9049) (Jed Fox)
* fcb7bb4 Chore: avoid unnecessarily complex forEach calls in no-extra-parens (#9159) (Teddy Katz)
* ffa021e Docs: quotes rule - when does \n require backticks (#9135) (avimar)
* 60c5148 Chore: improve coverage in lib/*.js (#9130) (Teddy Katz)

v4.5.0 - August 18, 2017

* decdd2c Update: allow arbitrary nodes to be ignored in `indent` (fixes #8594) (#9105) (Teddy Katz)
* 79062f3 Update: fix indentation of multiline `new.target` expressions (#9116) (Teddy Katz)
* d00e24f Upgrade: `chalk` to 2.x release (#9115) (Stephen Edgar)
* 6ef734a Docs: add missing word in processor documentation (#9106) (Teddy Katz)
* a4f53ba Fix: Include files with no messages in junit results (#9093) (#9094) (Sean DuBois)
* 1d6a9c0 Chore: enable eslint-plugin/test-case-shorthand-strings (#9067) (薛定谔的猫)
* f8add8f Fix: don't autofix with linter.verifyAndFix when `fix: false` is used (#9098) (Teddy Katz)
* 77bcee4 Docs: update instructions for adding TSC members (#9086) (Teddy Katz)
* bd09cd5 Update: avoid requiring NaN spaces of indentation (fixes #9083) (#9085) (Teddy Katz)
* c93a853 Chore: Remove extra space in blogpost template (#9088) (Kai Cataldo)

v4.4.1 - August 7, 2017

* ec93614 Fix: no-multi-spaces to avoid reporting consecutive tabs (fixes #9079) (#9087) (Teddy Katz)

v4.4.0 - August 5, 2017

* 89196fd Upgrade: Espree to 3.5.0 (#9074) (Gyandeep Singh)
* b3e4598 Fix: clarify AST and don't use `node.start`/`node.end` (fixes #8956) (#8984) (Toru Nagashima)
* 62911e4 Update: Add ImportDeclaration option to indent rule (#8955) (David Irvine)
* de75f9b Chore: enable object-curly-newline & object-property-newline.(fixes #9042) (#9068) (薛定谔的猫)
* 5ae8458 Docs: fix typo in object-shorthand.md (#9066) (Jon Berry)
* c3d5b39 Docs: clarify options descriptions (fixes #8875) (#9060) (Brandon Mailhiot)
* 37158c5 Docs: clarified behavior of globalReturn option (fixes #8953) (#9058) (Brandon Mailhiot)
* c2f3553 Docs: Update example for MemberExpression option of indent (fixes #9056) (#9057) (Jeff)
* 78a85e0 Fix: no-extra-parens incorrectly reports async function expressions (#9035) (薛定谔的猫)
* c794f86 Fix: getter-return reporting method named 'get' (fixes #8919) (#9004) (薛定谔的猫)
* d0f78ec Docs: update rule deprecation policy (fixes #8635) (#9033) (Teddy Katz)
* 5ab282f Fix: Print error message in bin/eslint.js (fixes #9011) (#9041) (Victor Hom)
* 50e3cf3 Docs: Update sort-keys doc to define natural ordering (fixes #9043) (#9045) (Karan Sharma)
* 7ecfe6a Chore: enable eslint-plugin/test-case-property-ordering (#9040) (薛定谔的猫)
* ad32697 Upgrade: js-yaml to 3.9.1 (refs #9011) (#9044) (Teddy Katz)
* 66c1d43 Docs: Create SUPPORT.md (#9031) (Teddy Katz)
* 7247b6c Update: handle indentation of custom destructuring syntax (fixes #8990) (#9027) (Teddy Katz)
* cdb82f2 Fix: padding-line-between-statements crash on semicolons after blocks (#8748) (Alexander Madyankin)
* 3141872 Chore: remove unnecessary eslint-disable comments in codebase (#9032) (Teddy Katz)
* 0f97279 Fix: refactor no-multi-spaces to avoid regex backtracking (fixes #9001) (#9008) (Teddy Katz)
* b74514d Fix: refactor RuleContext to not modify report locations (fixes #8980) (#8997) (Teddy Katz)
* 31d7fd2 Fix: inconsistent `indent` behavior on computed properties (fixes #8989) (#8999) (Teddy Katz)
* 3393894 Fix: avoid reporting the entire AST for missing rules (#8998) (Teddy Katz)
* b3b95b8 Chore: enable additional rules on ESLint codebase (#9013) (Teddy Katz)
* 9b6c552 Upgrade: eslint-plugin-eslint-plugin@0.8.0 (#9012) (薛定谔的猫)
* acbe86a Chore: disallow .substr and .substring in favor of .slice (#9010) (Teddy Katz)
* d0536d6 Chore: Optimizes adding Linter methods (fixes #9000) (#9007) (Sean C Denison)
* 0a0401f Chore: fix spelling error. (#9003) (薛定谔的猫)
* 3d020b9 Update: emit a warning for ecmaFeatures rather than throwing an error (#8974) (Teddy Katz)
* d2f8f9f Fix: include name of invalid config in validation messages (fixes #8963) (#8973) (Teddy Katz)
* c3ee46b Chore: fix misleading comment in RuleTester (#8995) (Teddy Katz)

v4.3.0 - July 21, 2017

* 91dccdf Update: support more options in prefer-destructuring (#8796) (Victor Hom)
* 3bebcfd Update: Support generator yields in no constant condition (#8762) (Victor Hom)
* 96df8c9 Fix: Handle fixing objects containing comments (fixes #8484) (#8944) (Brian Schemp)
* e39d41d Docs: Make `peerDependencies` package.json snippet valid JSON (#8971) (Sam Adams)
* a5fd101 Fix: duplicated error message if a crash occurs (fixes #8964) (#8965) (Teddy Katz)
* f8d122c Docs: trailing commas not allowed in json (#8969) (Scott Fletcher)
* d09288a Chore: Use `output: null` to assert that a test case is not autofixed. (#8960) (薛定谔的猫)
* e639358 Update: add question to confirm downgrade (fixes #8870) (#8911) (Toru Nagashima)
* 601039d Docs: fix badge in eslint-config-eslint readme (#8954) (Teddy Katz)
* 3c231fa Update: add enforceInMethodNames to no-underscore-dangle (fixes #7065) (#7234) (Gabriele Petronella)
* 128591f Update: prefer-numeric-literals warns Number.parseInt (fixes #8913) (#8929) (Kevin Partington)
* 846f8b1 Docs: Clarified that core PRs require issue in maintainer guide (#8927) (Kevin Partington)
* 55bc35d Fix: Avoid shell mangling during eslint --init (#8936) (Anders Kaseorg)
* 10c3d78 Chore: fix misleading `indent` test (#8925) (Teddy Katz)
* fb8005d Update: no-restricted-globals custom error messages (fixes #8315) (#8932) (Kevin Partington)
* a747b6f Chore: make minor improvements to `indent` internals (#8947) (Teddy Katz)
* 1ea3723 Update: fix indentation of parenthesized MemberExpressions (fixes #8924) (#8928) (Teddy Katz)
* 9abc6f7 Update: fix BinaryExpression indentation edge case (fixes #8914) (#8930) (Teddy Katz)
* 0e90453 Docs: Fixing broken cyclomatic complexity link (fixes #8396) (#8937) (Chris Bargren)
* a8a8350 Chore: improve performance of `indent` rule (#8905) (Teddy Katz)
* 764b2a9 Chore: update header info in `indent` (#8926) (Teddy Katz)
* 597c217 Fix: confusing error if plugins from config is not an array (#8888) (Calvin Freitas)
* 3c1dd6d Docs: add description of no-sync `allowAtRootLevel` option (fixes #8902) (#8906) (Teddy Katz)
* 933a9cf Chore: add a fuzzer to detect bugs in core rules (#8422) (Teddy Katz)
* 45f8cd9 Docs: fix verifyAndFix result property name (#8903) (Tino Vyatkin)
* 1a89e1c Docs: Fix always-multiline example in multiline-ternary docs (#8904) (Nathan Woltman)

v4.2.0 - July 8, 2017

* e0f0101 Update: fix indentation of nested function parameters (fixes #8892) (#8900) (Teddy Katz)
* 9f95a3e Chore: remove unused helper method from `indent` (#8901) (Teddy Katz)
* 11ffe6b Fix: no-regex-spaces rule incorrectly fixes quantified spaces (#8773) (Keri Warr)
* 975dacf Update: fix indentation of EmptyStatements (fixes #8882) (#8885) (Teddy Katz)
* 88ed041 Build: Turnoff CI branch build (fixes #8804) (#8873) (Gyandeep Singh)
* 72f22eb Chore: replace is-my-json-valid with Ajv (#8852) (Gajus Kuizinas)
* 7c8de92 Docs: Clarified PR guidelines in maintainer guide (#8876) (Kevin Partington)
* d1fc408 Docs: Update CLA link in Contributing docs (#8883) (Calvin Freitas)
* 931a9f1 Fix: indent false positive with multi-line await expression (#8837) (薛定谔的猫)
* 3767cda Update: add no-sync option to allow at root level (fixes #7985) (#8859) (Victor Hom)
* 1ce553d Docs: Fix wording of minProperties in object-curly-newline (fixes #8874) (#8878) (solmsted)
* f00854e Fix: --quiet no longer fixes warnings (fixes #8675) (#8858) (Kevin Partington)
* b678535 Chore: Add collapsible block for config in ISSUE_TEMPLATE (#8872) (Gyandeep Singh)
* 1f5bfc2 Update: Add always-multiline option to multiline-ternary (fixes #8770) (#8841) (Nathan Woltman)
* 22116f2 Fix: correct comma-dangle JSON schema (#8864) (Evgeny Poberezkin)
* 676af9e Update: fix indentation of JSXExpressionContainer contents (fixes #8832) (#8850) (Teddy Katz)
* 330dd58 Chore: fix title of linter test suite (#8861) (Teddy Katz)
* 60099ed Chore: enable for-direction rule on ESLint codebase (#8853) (薛定谔的猫)
* e0d1a84 Chore: upgrade eslint-plugin-eslint-plugin & eslint-plugin-node (#8856) (薛定谔的猫)
* 0780d86 Chore: remove identical tests (#8851) (Teddy Katz)
* 5c3ac8e Fix: arrow-parens fixer gets tripped up with trailing comma in args (#8838) (薛定谔的猫)
* c4f2e29 Build: fix race condition in demo (#8827) (Teddy Katz)
* c693be5 New: Allow passing a function as `fix` option (fixes #8039) (#8730) (Ian VanSchooten)
* 8796d55 Docs: add missing item to 4.0 migration guide table of contents (#8835) (薛定谔的猫)
* 742998c doc md update: false -> `false` (#8825) (Erik Vold)
* ce969f9 Docs: add guidelines for patch release communication (fixes #7277) (#8823) (Teddy Katz)
* 5c83c99 Docs: Clarify arrow function parens in no-extra-parens (fixes #8741) (#8822) (Kevin Partington)
* 84d921d Docs: Added note about Node/CJS scoping to no-redeclare (fixes #8814) (#8820) (Kevin Partington)
* 85c9327 Update: fix parenthesized CallExpression indentation (fixes #8790) (#8802) (Teddy Katz)
* be8d354 Update: simplify variable declarator indent handling (fixes #8785) (#8801) (Teddy Katz)
* 9417818 Fix: no-debugger autofixer produced invalid syntax (#8806) (Teddy Katz)
* 8698a92 New: getter-return rule (fixes #8449) (#8460) (薛定谔的猫)
* eac06f2 Fix: no-extra-parens false positives for variables called "let" (#8808) (Teddy Katz)
* 616587f Fix: dot-notation autofix produces syntax errors for object called "let" (#8807) (Teddy Katz)
* a53ef7e Fix: don't require a third argument in linter.verifyAndFix (fixes #8805) (#8809) (Teddy Katz)
* 5ad8b70 Docs: add minor formatting improvement to paragraph about parsers (#8816) (Teddy Katz)

v4.1.1 - June 25, 2017

* f307aa0 Fix: ensure configs from a plugin are cached separately (fixes #8792) (#8798) (Teddy Katz)
* 8b48ae8 Docs: Add doc on parser services (fixes #8390) (#8795) (Victor Hom)
* 0d041e7 Fix: avoid crashing when using baseConfig with extends (fixes #8791) (#8797) (Teddy Katz)
* 03213bb Chore: improve comment explanation of `indent` internal functions (#8800) (Teddy Katz)
* d2e88ed Chore: Fix misleading comment in ConfigCache.js (#8799) (Teddy Katz)

v4.1.0 - June 23, 2017

* e8f1362 Docs: Remove wrong descriptions in `padded-block` rule (#8783) (Plusb Preco)
* 291a783 Update: `enforceForArrowConditionals` to `no-extra-parens` (fixes #6196) (#8439) (Evilebot Tnawi)
* a21dd32 New: Add `overrides`/`files` options for glob-based config (fixes #3611) (#8081) (Sylvan Mably)
* 879688c Update: Add ignoreComments option to no-trailing-spaces (#8061) (Jake Roussel)
* b58ae2e Chore: Only instantiate fileEntryCache when cache flage set (perf) (#8763) (Gyandeep Singh)
* 9851288 Update: fix indent errors on multiline destructure (fixes #8729) (#8756) (Victor Hom)
* 3608f06 Docs: Increase visibility of code of conduct (fixes #8758) (#8764) (Kai Cataldo)
* 673a58b Update: support multiple fixes in a report (fixes #7348) (#8101) (Toru Nagashima)
* 7a1bc38 Fix: don't pass default parserOptions to custom parsers (fixes #8744) (#8745) (Teddy Katz)
* c5b4052 Chore: enable computed-property-spacing on ESLint codebase (#8760) (Teddy Katz)
* 3419f64 Docs: describe how to use formatters on the formatter demo page (#8754) (Teddy Katz)
* a3ff8f2 Chore: combine tests in tests/lib/eslint.js and tests/lib/linter.js (#8746) (Teddy Katz)
* b7cc1e6 Fix: Space-infix-ops should ignore type annotations in TypeScript (#8341) (Reyad Attiyat)
* 46e73ee Fix: eslint --init installs wrong dependencies of popular styles (fixes #7338) (#8713) (Toru Nagashima)
* a82361b Chore: Prevent package-lock.json files from being created (fixes #8742) (#8747) (Teddy Katz)
* 5f81a68 New: Add eslintIgnore support to package.json (fixes #8458) (#8690) (Victor Hom)
* b5a70b4 Update: fix multiline binary operator/parentheses indentation (#8719) (Teddy Katz)
* ab8b016 Update: fix MemberExpression indentation with "off" option (fixes #8721) (#8724) (Teddy Katz)
* eb5d12c Update: Add Fixer method to Linter API (#8631) (Gyandeep Singh)
* 26a2daa Chore: Cache fs reads in ignored-paths (fixes #8363) (#8706) (Victor Hom)

v4.0.0 - June 11, 2017

* 4aefb49 Chore: avoid using deprecated rules on ESLint codebase (#8708) (Teddy Katz)
* 389feba Chore: upgrade deps. (#8684) (薛定谔的猫)
* 3da7b5e Fix: Semi-Style only check for comments when tokens exist (fixes #8696) (#8697) (Reyad Attiyat)
* 3cfe9ee Fix: Add space between async and param on fix (fixes #8682) (#8693) (Reyad Attiyat)
* c702858 Chore: enable no-multiple-empty-lines on ESLint codebase (#8694) (Teddy Katz)
* 34c4020 Update: Add support for parens on left side for-loops (fixes: #8393) (#8679) (Victor Hom)
* 735cd09 Docs: Correct the comment in an example for `no-mixed-requires` (#8686) (Fangzhou Li)
* 026f048 Chore: remove dead code from prefer-const (#8683) (Teddy Katz)

v4.0.0-rc.0 - June 2, 2017

* 0058b0f8 Update: add --fix to no-debugger (#8660) (薛定谔的猫)
* b4daa225 Docs: Note to --fix option for strict rule (#8680) (Vitaliy Potapov)
* 4df33e7c Chore: check for root:true in project sooner (fixes #8561) (#8638) (Victor Hom)
* c9b980ce Build: Add Node 8 on travis (#8669) (Gyandeep Singh)
* ******** Fix: Don't check object destructing in integer property (fixes #8654) (#8657) (flowmemo)
* c4ac969c Update: fix parenthesized ternary expression indentation (fixes #8637) (#8649) (Teddy Katz)
* 4f2f9fcb Build: update license checker to allow LGPL (fixes #8647) (#8652) (Teddy Katz)
* b0c83bd1 Docs: suggest pushing new commits to a PR instead of amending (#8632) (Teddy Katz)
* d0e9fd2d Fix: Config merge to correctly account for extends (fixes #8193) (#8636) (Gyandeep Singh)
* 705d88f7 Docs: Update CLA link on Pull Requests page (#8642) (Teddy Katz)
* 794d4d6c Docs: missing paren on readme (#8640) (Dan Beam)
* 7ebd9d6f New: array-element-newline rule (fixes #6075) (#8375) (Jan Peer Stöcklmair)
* f62cff66 Chore: Remove dependency to user-home (fixes #8604) (#8629) (Pavol Madar)
* 936bc174 Docs: Add missing documentation for scoped modules in sharable config developer-guide (#8610) (Jonathan Samines)

v4.0.0-beta.0 - May 19, 2017

* 2f7015b6 New: semi-style rule (fixes #8169) (#8542) (Toru Nagashima)
* 1eaef580 Revert "Breaking: Traverse into type annotations (fixes #7129) (#8365)" (#8584) (Kai Cataldo)
* eb14584a Fix: no-unneeded-ternary change code behavior after fix (fixes #8507) (#8624) (Jan Peer Stöcklmair)
* 3ec436ee Breaking: New Linter API (fixes #8454) (#8465) (Gyandeep Singh)
* 3fc9653a Fix: Call expression consistency in variable declaration (fixes #8607) (#8619) (Reyad Attiyat)
* 5b6093ef Docs: Remove .eslintignore reference to transpiled file filtering (#8622) (Alex Summer)
* 729bbcdb Chore: Fix lgtm alerts. (#8611) (Max Schaefer)
* 3418479a Update: improve indent of `flatTernaryExpressions` (fixes #8481) (#8587) (Toru Nagashima)
* 268d52ef Update: Use sane defaults for JSX indentation (fixes #8425) (#8593) (Teddy Katz)
* d21f5283 Chore: make shelljs a devDependency instead of a dependency (#8608) (Teddy Katz)
* 11493781 Docs: Rephrase in about section (#8609) (Sudarsan G P)
* 23401626 Chore: remove strip-bom dependency (refs #8603) (#8606) (Teddy Katz)
* a93a2f95 New: padding-line-between-statements rule (fixes #7356) (#8099) (Toru Nagashima)
* 0ef09ea0 New: for-direction rule (fixes #8387) (#8519) (薛定谔的猫)
* a73e6c09 Fix: Fix failing uknown node test since #8569 indents class bodies (#8588) (Reyad Attiyat)
* c6c639d6 Fix: Ignore unknown nodes for Indent rule (fixes #8440) (#8504) (Reyad Attiyat)
* df17bc87 Fix: object-shorthand crash on some computed keys (fixes #8576) (#8577) (Teddy Katz)
* 482d5720 New: switch-colon-spacing rule (fixes #7981) (#8540) (Toru Nagashima)
* afa35c68 Update: check allman-style classes correctly in indent (fixes #8493) (#8569) (Teddy Katz)
* de0b4ad7 Fix: Indent Ignore Variable Declaration init operator (fixes #8546) (#8563) (Reyad Attiyat)
* 927ca0dc Fix: invalid syntax from prefer-arrow-callback autofixer (fixes #8541) (#8555) (Teddy Katz)
* 25db3d22 Chore: avoid skipping test for env overrides (refs #8291) (#8556) (Teddy Katz)
* 456f519b Update: make indent MemberExpression handling more robust (fixes #8552) (#8554) (Teddy Katz)
* 873310e5 Fix: run no-unexpected-multiline only if needed (fixes #8550) (#8551) (Ruben Bridgewater)
* 833a0cad Fix: confusing RuleTester error message when options is not an array (#8557) (Teddy Katz)

v4.0.0-alpha.2 - May 5, 2017

* 74ab344 Update: check allman-style blocks correctly in indent rule (fixes #8493) (#8499) (Teddy Katz)
* f6256d4 Update: no-extend-native checks global scope refs only (fixes #8461) (#8528) (Kevin Partington)
* b463045 Docs: add typescript-eslint-parser (#8388) (#8534) (薛定谔的猫)
* 99c56d5 Update: handle multiline parents consistently in indent (fixes #8455) (#8498) (Teddy Katz)
* cf940c6 Update: indent `from` tokens in import statements (fixes #8438) (#8466) (Teddy Katz)
* 0a9a90f Fix: max-len doesn't allow comments longer than code (#8532) (Ken Gregory)
* 734846b Breaking: validate eslintrc properties (fixes #8213) (#8295) (alberto)
* 025e97a Chore: delete duplicated test. (#8527) (薛定谔的猫)
* 6a333ff Upgrade: espree@^3.4.2 (#8526) (Kevin Partington)
* e52d998 Docs: Configuring Cascading and Hierarchy example correction (#8512) (Cheong Yip)
* e135aa5 Docs: Correct code of conduct link on Readme.md (#8517) (Zander Mackie)
* 37e3ba1 Chore: Add license report and scan status (#8503) (Kevin Wang)
* afbea78 Chore: don't pull default options from eslint:recommended (fixes #8374) (#8381) (Teddy Katz)
* d49acc3 Update: fix no-self-compare false negative on non-literals (fixes #7677) (#8492) (Teddy Katz)
* aaa1a81 Fix: avoid creating extra whitespace in brace-style fixer (fixes #7621) (#8491) (Teddy Katz)
* 9c3da77 Docs: list another related rule in no-undefined (#8467) (Ethan)
* f987814 Docs: Update CHANGELOG.md for v4.0.0-alpha.1 release (#8488) (Kai Cataldo)

v4.0.0-alpha.1 - April 21, 2017

* b0dadfe3 Docs: Update comments section of Migrating to v4.0.0 (#8486) (Kai Cataldo)
* b337738f Update: Add `consistent` option to `object-curly-newline` (fixes #6488) (#7720) (Evilebot Tnawi)
* 53fefb3b Update: add fix for no-confusing-arrow (#8347) (Mordy Tikotzky)
* 735d02d5 Update: Deprecate sourceCode.getComments() (fixes #8408) (#8434) (Kai Cataldo)
* ac39e3b0 Update: no-unexpected-multiline to flag confusing division (fixes #8469) (#8475) (Teddy Katz)
* e35107f0 Fix: indent crash on arrow functions without parens at start of line (#8477) (Teddy Katz)
* 973adeb6 Docs: State that functions option only applies in ES2017 (fixes #7809) (#8468) (Thenaesh Elango)
* 7bc6fe0a New: array-bracket-newline rule (#8314) (Jan Peer Stöcklmair)
* 10a1a2d7 Chore: Do not use cache when testing (#8464) (Kai Cataldo)
* 9f540fd2 Update: no-unused-vars false negative about destructuring (fixes #8442) (#8459) (Toru Nagashima)
* 741ed393 Docs: Clarify how to run local ESLint installation (#8463) (Kai Cataldo)
* fac53890 Breaking: Remove array-callback-return from recommended (fixes #8428) (#8433) (Kai Cataldo)
* 288c96c1 Upgrade: dependencies (#8304) (alberto)
* 48700fc8 Docs: Remove extra header line from LICENSE (#8448) (Teddy Katz)
* 161ee4ea Chore: avoid cloning comments array in TokenStore (#8436) (Teddy Katz)
* 0c2a386e Docs: clarify new indent behavior with MemberExpressions (#8432) (Teddy Katz)
* 446b8876 Docs: update space-before-function-paren docs for 4.0 (fixes #8430) (#8431) (Teddy Katz)

v4.0.0-alpha.0 - April 7, 2017

* 950874f Docs: add 4.0.0 migration guide (fixes #8306) (#8313) (Teddy Katz)
* 2754141 Fix: more autofix token-combining bugs (#8394) (Teddy Katz)
* f5a7e42 Breaking: log number of fixable problems (fixes #7364) (#8324) (alberto)
* 769b121 Chore: Fix indentation errors in indent-legacy (#8424) (Kai Cataldo)
* 8394e48 Update: add deprecated indent-legacy rule as v3.x indent rule snapshot (#8286) (Teddy Katz)
* 3c87e85 Fix: no-multi-spaces false positive with irregular indent whitespace (#8412) (Teddy Katz)
* cc53481 Breaking: rewrite indent (fixes #1801, #3737, #3845, #6007, ...16 more) (#7618) (Teddy Katz)
* 867dd2e Breaking: Calculate leading/trailing comments in core (#7516) (Kai Cataldo)
* de9f1a0 Docs: ES6 syntax vs globals configuration (fixes #7984) (#8350) (Zander Mackie)
* 66af53e Breaking: Traverse into type annotations (fixes #7129) (#8365) (Kai Cataldo)
* 86cf3e4 New: no-buffer-constructor rule (fixes #5614) (#8413) (Teddy Katz)
* f560c06 Update: fix space-unary-ops behavior with postfix UpdateExpressions (#8391) (Teddy Katz)
* 936af66 Fix: no-multiple-empty-lines crash on space after last \n (fixes #8401) (#8402) (Teddy Katz)
* e395919 Breaking: Resolve patterns from .eslintignore directory (fixes #6759) (#7678) (Ian VanSchooten)
* c778676 Breaking: convert RuleTester to ES6 class (refs #8231) (#8263) (Teddy Katz)
* 6f7757e Breaking: convert SourceCode to ES6 class (refs #8231) (#8264) (Teddy Katz)
* 8842d7e Chore: fix comment spacing in tests (#8405) (Teddy Katz)
* 9a9d916 Breaking: update eslint:recommended for 4.0.0 (fixes #8236) (#8372) (Teddy Katz)
* b0c63f0 Breaking: infer endLine and endColumn from a reported node (fixes #8004) (#8234) (Teddy Katz)
* 40b8c69 Breaking: no-multi-spaces check around inline comments (fixes #7693) (#7696) (Kai Cataldo)
* 034a575 Breaking: convert CLIEngine to ES6 class (refs #8231) (#8262) (Teddy Katz)
* 7dd890d Breaking: tweak space-before-function-paren default option (fixes #8267) (#8285) (Teddy Katz)
* 0e0dd27 Breaking: Remove `ecmaFeatures` from `eslint:recommended` (#8239) (alberto)
* 2fa7502 Breaking: disallow scoped plugin references without scope (fixes #6362) (#8233) (Teddy Katz)
* 4673f6e Chore: Switch to eslint-scope from escope (#8280) (Corbin Uselton)
* e232464 Breaking: change defaults for padded-blocks (fixes #7879) (#8134) (alberto)

v3.19.0 - March 31, 2017

* e09132f Fix: no-extra-parens false positive with exports and object literals (#8359) (Teddy Katz)
* 91baed4 Update: allow custom messages in no-restricted-syntax (fixes #8298) (#8357) (Vitor Balocco)
* 35c93e6 Fix: prevent space-before-function-paren from checking type annotations (#8349) (Teddy Katz)
* 3342e9f Fix: don't modify operator precedence in operator-assignment autofixer (#8358) (Teddy Katz)
* f88375f Docs: clarify that no-unsafe-negation is in eslint:recommended (#8371) (Teddy Katz)
* 02f0d27 Docs: Add soda0289 to Development Team (#8367) (Kai Cataldo)
* 155424c Fix: ignore empty path in patterns (fixes #8362) (#8364) (alberto)
* 27616a8 Fix: prefer-const false positive with object spread (fixes #8187) (#8297) (Vitor Balocco)
* 8569a90 Docs: add note about git's linebreak handling to linebreak-style docs (#8361) (Teddy Katz)
* 5878593 Chore: fix invalid syntax in no-param-reassign test (#8360) (Teddy Katz)
* 1b1046b Fix: don't classify plugins that throw errors as "missing" (fixes #6874) (#8323) (Teddy Katz)
* 29f4ba5 Fix: no-useless-computed-key invalid autofix for getters and setters (#8335) (Teddy Katz)
* 0541eaf Fix: no-implicit-coercion invalid autofix with consecutive identifiers (#8340) (Teddy Katz)
* 41b9786 Fix: no-extra-parens false positive with objects following arrows (#8339) (Teddy Katz)
* 3146167 Fix: `eslint.verify` should not mutate config argument (fixes #8329) (#8334) (alberto)
* 927de90 Fix: dot-notation autofix produces invalid syntax for integer properties (#8332) (Teddy Katz)
* a9d1bea Fix: comma-style autofix produces errors on parenthesized elements (#8331) (Teddy Katz)
* d52173f Fix: don't generate invalid options in config-rule (#8326) (Teddy Katz)
* 6eda3b5 Fix: no-extra-parens invalid autofix in for-of statements (#8337) (Teddy Katz)
* 6c819d8 Fix: dot-notation autofix produces errors on parenthesized computed keys (#8330) (Teddy Katz)
* 2d883d7 Fix: object-shorthand autofix produces errors on parenthesized functions (#8328) (Teddy Katz)
* cd9b774 Fix: quotes false positive with backtick option in method names (#8327) (Teddy Katz)
* d064ba2 Fix: no-else-return false positive for ifs in single-statement position (#8338) (Teddy Katz)
* 6a718ba Chore: enable max-statements-per-line on ESLint codebase (#8321) (Teddy Katz)
* 614b62e Chore: update sinon calls to deprecated API. (#8310) (alberto)
* 0491572 Chore: use precalculated counts in codeframe formatter (#8296) (Vitor Balocco)
* 8733e6a Chore: Fix incorrect error location properties in tests (#8307) (alberto)
* c4ffb49 Chore: Fix typos in test option assertions (#8305) (Teddy Katz)
* 79a97cb Upgrade: devDependencies (#8303) (alberto)
* e4da200 Upgrade: Mocha to 3.2.0 (#8299) (Ilya Volodin)
* 2f144ca Fix: operator-assignment autofix errors with parentheses (fixes #8293) (#8294) (Teddy Katz)
* 7521cd5 Chore: update token logic in rules to use ast-utils (#8288) (Teddy Katz)
* 9b509ce Chore: refactor space-before-function-paren rule (#8284) (Teddy Katz)
* ddc6350 Fix: no-param-reassign false positive on destructuring (fixes #8279) (#8281) (Teddy Katz)
* f8176b3 Chore: improve test coverage for node-event-generator (#8287) (Teddy Katz)
* 602e9c2 Docs: fix incorrect selector examples (#8278) (Teddy Katz)

v3.18.0 - March 17, 2017

* 85f74ca Fix: broken code path of direct nested loops (fixes #8248) (#8274) (Toru Nagashima)
* a61c359 Fix: Ignore hidden folders when resolving globs (fixes #8259) (#8270) (Ian VanSchooten)
* 6f05546 Chore: convert StubModuleResolver in config tests to ES6 class (#8265) (Teddy Katz)
* 0c0fc31 Fix: false positive of no-extra-parens about spread and sequense (#8275) (Toru Nagashima)
* e104973 Docs: remove self-reference in no-restricted-syntax docs (#8277) (Vitor Balocco)
* 23eca51 Update: Add allowTaggedTemplates to no-unused-expressions (fixes #7632) (#8253) (Kevin Partington)
* f9ede3f Upgrade: doctrine to 2.0.0 (#8269) (alberto)
* 1b678a6 New: allow rules to listen for AST selectors (fixes #5407) (#7833) (Teddy Katz)
* 63ca0c5 Chore: use precalculated counts in stylish formatter (#8251) (alberto)
* 47c3171 Fix: typo in console.error (#8258) (Jan Peer Stöcklmair)
* e74ed6d Chore: convert Traverser to ES6 class (refs #7849) (#8232) (Teddy Katz)
* 13eead9 Fix: sort-vars crash on mixed destructuring declarations (#8245) (Teddy Katz)
* 133f489 Fix: func-name-matching crash on destructuring assignment to functions (#8247) (Teddy Katz)
* a34b9c4 Fix: func-name-matching crash on non-string literal computed keys (#8246) (Teddy Katz)
* 7276e6d Docs: remove unneeded semicolons in arrow-parens.md (#8249) (Dmitry Gershun)
* 8c40a25 concat-stream known to be vulnerable prior 1.5.2 (#8228) (Samuel)
* 149c055 Upgrade: mock-fs to v4.2.0 (fixes #8194) (#8243) (Teddy Katz)
* a83bff9 Build: remove unneeded json config in demo (fixes #8237) (#8242) (alberto)
* df12137 Docs: fix typos (#8235) (Gyandeep Singh)
* b5e9788 Chore: rename no-extra-parens methods (#8225) (Vitor Balocco)
* 7f8afe6 Update: no-extra-parens overlooked spread and superClass (fixes #8175) (#8209) (Toru Nagashima)
* ce6ff56 Docs: set recommended true for no-global-assign (fixes #8215) (#8218) (BinYi LIU)
* 5b5c236 Fix: wrong comment when module not found in config (fixes #8192) (#8196) (alberto)

v3.17.1 - March 6, 2017

* f8c8e6e Build: change mock-fs path without SSH (fixes #8207) (#8208) (Toru Nagashima)
* f713f11 Fix: nonblock-statement-body-position multiline error (fixes #8202) (#8203) (Teddy Katz)
* 41e3d9c Fix: `operator-assignment` with parenthesized expression (fixes #8190) (#8197) (alberto)
* 5e3bca7 Chore: add eslint-plugin-eslint-plugin (#8198) (Teddy Katz)
* 580da36 Chore: add missing `output` property to tests (#8195) (alberto)

v3.17.0 - March 3, 2017

* 4fdf6d7 Update: deprecate `applyDefaultPatterns` in `line-comment-position` (#8183) (alberto)
* 25e5817 Fix: Don't autofix `+ +a` to `++a` in space-unary-ops (#8176) (Alan Pierce)
* a6ce8f9 Build: Sort rules before dumping them to doc files (#8154) (Danny Andrews)
* 0af9057 Chore: Upgrade to a patched version of mock-fs (fixes #8177) (#8188) (Teddy Katz)
* bf4d8cf Update: ignore eslint comments in lines-arount-comment (fixes #4345) (#8155) (alberto)
* dad20ad New: add SourceCode#getLocFromIndex and #getIndexFromLoc (fixes #8073) (#8158) (Teddy Katz)
* 18a519f Update: let RuleTester cases assert that no autofix occurs (fixes #8157) (#8163) (Teddy Katz)
* a30eb8d Docs: improve documentation for RuleTester cases (#8162) (Teddy Katz)
* a78ec9f Chore: upgrade `coveralls` to ^2.11.16 (#8161) (alberto)
* d02bd11 Fix: padded-blocks autofix problems with comments (#8149) (alberto)
* 9994889 Docs: Add missing space to `create` in `no-use-before-define` (#8166) (Justin Anastos)
* 4d542ba Docs: Remove unneeded statement about autofix (#8164) (alberto)
* 20daea5 New: no-compare-neg-zero rule (#8091) (薛定谔的猫)
* 4d35a81 Fix: Add a utility to avoid autofix conflicts (fixes #7928, fixes #8026) (#8067) (Alan Pierce)
* 287e882 New: nonblock-statement-body-position rule (fixes #6067) (#8108) (Teddy Katz)
* 7f1f4e5 Chore: remove unneeded devDeps `linefix` and `gh-got` (#8160) (alberto)
* ca1694b Update: ignore negative ranges in fixes (#8133) (alberto)
* 163d751 Docs: `lines-around-comment` doesn't disallow empty lines (#8151) (alberto)
* 1c84922 Chore: upgrade eslint-plugin-node (#8156) (alberto)
* 1ee5c27 Fix: Make RuleTester handle empty-string cases gracefully (fixes #8142) (#8143) (Teddy Katz)
* 044bc10 Docs: Add details about "--fix" option for "sort-imports" rule (#8077) (Olivier Audard)
* 3fec54a Add option to ignore property in no-param-reassign (#8087) (Christian Bundy)
* 4e52cfc Fix: Improve keyword-spacing typescript support (fixes #8110) (#8111) (Reyad Attiyat)
* 7ff42e8 New: Allow regexes in RuleTester (fixes #7837) (#8115) (Daniel Lo Nigro)
* cbd7ded Build: display rules’ meta data in their docs (fixes #5774) (#8127) (Wilson Kurniawan)
* da8e8af Update: include function name in report message if possible (fixes #7260) (#8058) (Dieter Luypaert)
* 8f91e32 Fix: `ignoreRestSiblings` option didn't cover arguments (fixes #8119) (#8120) (Toru Nagashima)

v3.16.1 - February 22, 2017

* ff8a80c Fix: duplicated autofix output for inverted fix ranges (fixes #8116) (#8117) (Teddy Katz)
* a421897 Docs: fix typo in arrow-parens.md (#8132) (Will Chen)
* 22d7fbf Chore: fix invalid redeclared variables in tests (#8130) (Teddy Katz)
* 8d95598 Chore: fix output assertion typos in rule tests (#8129) (Teddy Katz)
* 9fa2559 Docs: Add missing quotes in key-spacing rule (#8121) (Glenn Reyes)
* f3a6ced Build: package.json update for eslint-config-eslint release (ESLint Jenkins)

v3.16.0 - February 20, 2017

* d89d0b4 Update: fix quotes false negative for string literals as template tags (#8107) (Teddy Katz)
* 21be366 Chore: Ensuring eslint:recommended rules are sorted. (#8106) (Kevin Partington)
* 360dbe4 Update: Improve error message when extend config missing (fixes #6115) (#8100) (alberto)
* f62a724 Chore: use updated token iterator methods (#8103) (Kai Cataldo)
* daf6f26 Fix: check output in RuleTester when errors is a number (fixes #7640) (#8097) (alberto)
* cfb65c5 Update: make no-lone-blocks report blocks in switch cases (fixes #8047) (#8062) (Teddy Katz)
* 290fb1f Update: Add includeComments to getTokenByRangeStart (fixes #8068) (#8069) (Kai Cataldo)
* ff066dc Chore: Incorrect source code test text (#8096) (Jack Ford)
* 14d146d Docs: Clarify --ext only works with directories (fixes #7939) (#8095) (alberto)
* 013a454 Docs: Add TSC meeting quorum requirement (#8086) (Kevin Partington)
* 7516303 Fix: `sourceCode.getTokenAfter` shouldn't skip tokens after comments (#8055) (Toru Nagashima)
* c53e034 Fix: unicode-bom fixer insert BOM in appropriate location (fixes #8083) (#8084) (pantosha)
* 55ac302 Chore: fix the timing to define rules for tests (#8082) (Toru Nagashima)
* c7e64f3 Upgrade: mock-fs (#8070) (Toru Nagashima)
* acc3301 Update: handle uncommon linebreaks consistently in rules (fixes #7949) (#8049) (Teddy Katz)
* 591b74a Chore: enable operator-linebreak on ESLint codebase (#8064) (Teddy Katz)
* 6445d2a Docs: Add documentation for /* exported */ (fixes #7998) (#8065) (Lee Yi Min)
* fcc38db Chore: simplify and improve performance for autofix (#8035) (Toru Nagashima)
* b04fde7 Chore: improve performance of SourceCode constructor (#8054) (Teddy Katz)
* 90fd555 Update: improve null detection in eqeqeq for ES6 regexes (fixes #8020) (#8042) (Teddy Katz)
* 16248e2 Fix: no-extra-boolean-cast incorrect Boolean() autofixing (fixes #7977) (#8037) (Jonathan Wilsson)
* 834f45d Update: rewrite TokenStore (fixes #7810) (#7936) (Toru Nagashima)
* 329dcdc Chore: unify checks for statement list parents (#8048) (Teddy Katz)
* c596690 Docs: Clarify generator-star-spacing config example (fixes #8027) (#8034) (Hòa Trần)
* a11d4a6 Docs: fix a typo in shareable configs documentation (#8036) (Dan Homola)
* 1e3d4c6 Update: add fixer for no-unused-labels (#7841) (Teddy Katz)
* f47fb98 Update: ensure semi-spacing checks import/export declarations (#8033) (Teddy Katz)
* e228d56 Update: no-undefined handles properties/classes/modules (fixes #7964) (#7966) (Kevin Partington)
* 7bc92d9 Chore: fix invalid test cases (#8030) (Toru Nagashima)

v3.15.0 - February 3, 2017

* f2a3580 Fix: `no-extra-parens` incorrect precedence (fixes #7978) (#7999) (alberto)
* d6b6ba1 Fix: no-var should fix ForStatement.init (#7993) (Toru Nagashima)
* 99d386d Upgrade: Espree v3.4.0 (#8019) (Kai Cataldo)
* 42390fd Docs: update README.md for team (#8016) (Toru Nagashima)
* d7ffd88 Chore: enable template-tag-spacing on ESLint codebase (#8005) (Teddy Katz)
* f2be7e3 Docs: Fix typo in object-curly-newline.md (#8002) (Danny Andrews)
* df2351a Docs: Fix misleading section in brace-style documentation (#7996) (Teddy Katz)
* 5ae6e00 Chore: avoid unnecessary feature detection for Symbol (#7992) (Teddy Katz)
* 5d57c57 Chore: fix no-else-return lint error (refs #7986) (#7994) (Vitor Balocco)
* 62fb054 Chore: enable no-else-return on ESLint codebase (#7986) (Teddy Katz)
* c59a0ba Update: add ignoreRestSiblings option to no-unused-vars (#7968) (Zack Argyle)
* 5cdfa99 Chore: enable no-unneeded-ternary on ESLint codebase (#7987) (Teddy Katz)
* fbd7c13 Update: ensure operator-assignment handles exponentiation operators (#7970) (Teddy Katz)
* c5066ce Update: add "variables" option to no-use-before-define (fixes #7111) (#7948) (Teddy Katz)
* 09546a4 New: `template-tag-spacing` rule (fixes #7631) (#7913) (Jonathan Wilsson)

v3.14.1 - January 25, 2017

* 791f32b Fix: brace-style false positive for keyword method names (fixes #7974) (#7980) (Teddy Katz)
* d7a0add Docs: Add ESLint tutorial embed to getting started (#7971) (Jamis Charles)
* 72d41f0 Fix: no-var autofix syntax error in single-line statements (fixes #7961) (#7962) (Teddy Katz)
* b9e5b68 Fix: indent rule crash on sparse array with object (fixes #7959) (#7960) (Gyandeep Singh)
* a7bd66a Chore: Adding assign/redeclare tests to no-undefined (refs #7964) (#7965) (Kevin Partington)
* 8bcbf5d Docs: typo in prefer-promise-reject-errors (#7958) (Patrick McElhaney)

v3.14.0 - January 20, 2017

* 506324a Fix: `no-var` does not fix if causes ReferenceError (fixes #7950) (#7953) (Toru Nagashima)
* 05e7432 New: no-chained-assignments rule (fixes #6424) (#7904) (Stewart Rand)
* 243e47d Update: Add fixer for no-else-return (fixes #7863) (#7864) (Xander Dumaine)
* f091d95 New: `prefer-promise-reject-errors` rule (fixes #7685) (#7689) (Teddy Katz)
* ca01e00 Fix: recognize all line terminators in func-call-spacing (fixes #7923) (#7924) (Francesco Trotta)
* a664e8a Update: add ignoreJSX option to no-extra-parens (Fixes #7444) (#7926) (Robert Rossmann)
* 8ac3518 Fix: no-useless-computed-key false positive with `__proto__` (#7934) (Teddy Katz)
* c835e19 Docs: remove reference to deleted rule (#7942) (Alejandro Oviedo)
* 3c1e63b Docs: Improve examples for no-case-declarations (fixes #6716) (#7920) (Kevin Rangel)
* 7e04b33 Fix: Ignore inline plugin rule config in autoconfig (fixes #7860) (#7919) (Ian VanSchooten)
* 6448ba0 Fix: add parentheses in no-extra-boolean-cast autofixer (fixes #7912) (#7914) (Szymon Przybylski)
* b3f2094 Fix: brace-style crash with lone block statements (fixes #7908) (#7909) (Teddy Katz)
* 5eb2e88 Docs: Correct typos in configuring.md (#7916) (Gabriel Delépine)
* bd5e219 Update: ensure brace-style validates class bodies (fixes #7608) (#7871) (Teddy Katz)
* 427543a Fix: catastrophic backtracking in astUtils linebreak regex (fixes #7893) (#7898) (Teddy Katz)
* 995554c Fix: Correct typos in no-alert.md and lib/ast-utils.js (#7905) (Stewart Rand)
* d6150e3 Chore: Enable comma-dangle on ESLint codebase (fixes #7725) (#7906) (Teddy Katz)
* 075ec25 Chore: update to use ES6 classes (refs #7849) (#7891) (Claire Dranginis)
* 55f0cb6 Update: refactor brace-style and fix inconsistencies (fixes #7869) (#7870) (Teddy Katz)

v3.13.1 - January 9, 2017

* 3fc4e3f Fix: prefer-destructuring reporting compound assignments (fixes #7881) (#7882) (Teddy Katz)
* f90462e Fix: no-extra-label autofix should not remove labels used elsewhere (#7885) (Teddy Katz)

v3.13.0 - January 6, 2017

* cd4c025 Update: add fixer for no-extra-label (#7840) (Teddy Katz)
* aa75c92 Fix: Ensure prefer-const fixes destructuring assignments (fixes #7852) (#7859) (Teddy Katz)
* 4008022 Chore: Refactor to use ES6 Classes (Part 3)(refs #7849) (#7865) (Gyandeep Singh)
* c9ba40a Update: add fixer for `no-unneeded-ternary` (#7540) (Teddy Katz)
* dd56d87 Update: add object-shorthand option for arrow functions (fixes #7564) (#7746) (Teddy Katz)
* fbafdc0 Docs: `padded-blocks` `never` case (fixes #7868) (#7878) (alberto)
* ca1f841 Fix: no-useless-return stack overflow on loops after throw (fixes #7855) (#7856) (Teddy Katz)
* d80d994 Update: add fixer for object-property-newline (fixes #7740) (#7808) (Teddy Katz)
* bf3ea3a Fix: capitalized-comments: Ignore consec. comments if first is invalid (#7835) (Kevin Partington)
* 616611a Chore: Refactor to use ES6 Classes (Part 2)(refs #7849) (#7847) (Gyandeep Singh)
* 856084b Chore: Refactor to use ES6 Classes (Part 1)(refs #7849) (#7846) (Gyandeep Singh)
* bf45893 Docs: Clarify that we only support Stage 4 proposals (#7845) (Kevin Partington)
* 0fc24f7 Fix: adapt new-paren rule so it handles TypeScript (fixes #7817) (#7820) (Philipp A)
* df0b06b Fix: no-multiple-empty-lines perf issue on large files (fixes #7803) (#7843) (Teddy Katz)
* 18fa521 Chore: use ast-utils helper functions in no-multiple-empty-lines (#7842) (Teddy Katz)
* 7122205 Docs: Array destructuring example for no-unused-vars (fixes #7838) (#7839) (Remco Haszing)
* e21b36b Chore: add integration tests for cache files (refs #7748) (#7794) (Teddy Katz)
* 2322733 Fix: Throw error if ruletester is missing required test scenarios (#7388) (Teddy Katz)
* 1beecec Update: add fixer for `operator-linebreak` (#7702) (Teddy Katz)
* c5c3b21 Fix: no-implied-eval false positive on 'setTimeoutFoo' (fixes #7821) (#7836) (Teddy Katz)
* 00dd96c Chore: enable array-bracket-spacing on ESLint codebase (#7830) (Teddy Katz)
* ebcae1f Update: no-return-await with with complex `return` argument (fixes #7594) (#7595) (Dalton Santos)
* fd4cd3b Fix: Disable no-var autofixer in some incorrect cases in loops (#7811) (Alan Pierce)
* 1f25834 Docs: update outdated info in Architecture page (#7816) (Teddy Katz)
* f20b9e9 Fix: Relax no-useless-escape's handling of ']' in regexes (fixes #7789) (#7793) (Teddy Katz)
* 3004c1e Fix: consistent-return shouldn't report class constructors (fixes #7790) (#7797) (Teddy Katz)
* b938f1f Docs: Add an example for the spread operator to prefer-spread.md (#7802) (#7804) (butlermd)
* b8ce2dc Docs: Remove .html extensions from links in developer-guide (#7805) (Kevin Partington)
* aafebb2 Docs: Wrap placeholder sample in {% raw %} (#7798) (Daniel Lo Nigro)
* bb6b73b Chore: replace unnecessary function callbacks with arrow functions (#7795) (Teddy Katz)
* 428fbdf Fix: func-call-spacing "never" doesn't fix w/ line breaks (fixes #7787) (#7788) (Kevin Partington)
* 6e61070 Fix: `semi` false positive before regex/template literals (fixes #7782) (#7783) (Teddy Katz)
* ff0c050 Fix: remove internal property from config generation (fixes #7758) (#7761) (alberto)
* 27424cb New: `prefer-destructuring` rule (fixes #6053) (#7741) (Alex LaFroscia)
* bb648ce Docs: fix unclear example for no-useless-escape (#7781) (Teddy Katz)
* 8c3a962 Fix: syntax errors from object-shorthand autofix (fixes #7744) (#7745) (Teddy Katz)
* 8b296a2 Docs: fix in semi.md: correct instead of incorrect (#7779) (German Prostakov)
* 3493241 Upgrade: strip-json-comments ~v2.0.1 (Janus Troelsen)
* 75b7ba4 Chore: enable object-curly-spacing on ESLint codebase (refs #7725) (#7770) (Teddy Katz)
* 7d1dc7e Update: Make default-case comment case-insensitive (fixes #7673) (#7742) (Robert Rossmann)
* f1bf5ec Chore: convert remaining old-style context.report() calls to the new API (#7763) (Teddy Katz)

v3.12.2 - December 14, 2016

* dec3ec6 Fix: indent bug with AssignmentExpressions (fixes #7747) (#7750) (Teddy Katz)
* 5344751 Build: Don't create blogpost links from rule names within other words (#7754) (Teddy Katz)
* 639b798 Docs: Use `Object.prototype` in examples (#7755) (Alex Reardon)

v3.12.1 - December 12, 2016

* 0ad4d33 Fix: `indent` regression with function calls (fixes #7732, fixes #7733) (#7734) (Teddy Katz)
* ab246dd Docs: Rules restricting globals/properties/syntax are linked together (#7743) (Kevin Partington)
* df2f115 Docs: Add eslint-config-mdcs to JSCS Migration Guide (#7737) (Joshua Koo)
* 4b77333 Build: avoid creating broken rule links in the changelog (#7731) (Teddy Katz)

v3.12.0 - December 9, 2016

* e569225 Update: fix false positive/negative of yoda rule (fixes #7676) (#7695) (Toru Nagashima)
* e95a230 Fix: indent "first" option false positive on nested arrays (fixes #7727) (#7728) (Teddy Katz)
* 81f9e7d Fix: Allow duplicated let declarations in `prefer-const` (fixes #7712) (#7717) (Teddy Katz)
* 1d0d61d New: Add no-await-in-loop rule (#7563) (Nat Mote)
* 2cdfb4e New: Additional APIs (fixes #6256) (#7669) (Ilya Volodin)
* 4278c42 Update: make no-obj-calls report errors for Reflect (fixes #7700) (#7710) (Tomas Echeverri Valencia)
* 4742d82 Docs: clarify the default behavior of `operator-linebreak` (fixes #7459) (#7726) (Teddy Katz)
* a8489e2 Chore: Avoid parserOptions boilerplate in tests for ES6 rules (#7724) (Teddy Katz)
* b921d1f Update: add `indent` options for array and object literals (fixes #7473) (#7681) (Teddy Katz)
* 7079c89 Update: Add airbnb-base to init styleguides (fixes #6986) (#7699) (alberto)
* 63bb3f8 Docs: improve the documentation for the autofix API (#7716) (Teddy Katz)
* f8786fb Update: add fixer for `capitalized-comments` (#7701) (Teddy Katz)
* abfd24f Fix: don't validate schemas for disabled rules (fixes #7690) (#7692) (Teddy Katz)
* 2ac07d8 Upgrade: Update globals dependency to 9.14.0 (#7683) (Aleksandr Oleynikov)
* 90a5d29 Docs: Remove incorrect info about issue requirements from PR guide (#7691) (Teddy Katz)
* f80c278 Docs: Add sails-hook-lint to integrations list (#7679) (Anthony M)
* e96da3f Docs: link first instance of `package.json` (#7684) (Kent C. Dodds)
* bf20e20 Build: include links to rule pages in release blogpost (#7671) (Teddy Katz)
* b30116c Docs: Fix code-blocks in spaced-comment docs (#7524) (Michał Gołębiowski)
* 0a2a7fd Fix: Allow \u2028 and \u2029 as string escapes in no-useless-escape (#7672) (Teddy Katz)
* 76c33a9 Docs: Change Sails.js integration to active npm package (#7675) (Anthony M)

v3.11.1 - November 28, 2016

* be739d0 Fix: capitalized-comments fatal error fixed (fixes #7663) (#7664) (Rich Trott)
* cc4cedc Docs: Fix a typo in array-bracket-spacing documentation (#7667) (Alex Guerrero)
* f8adadc Docs: fix a typo in capitalized-comments documentation (#7666) (Teddy Katz)

v3.11.0 - November 25, 2016

* ad56694 New: capitalized-comments rule (fixes #6055) (#7415) (Kevin Partington)
* 7185567 Update: add fixer for `operator-assignment` (#7517) (Teddy Katz)
* faf5f56 Update: fix false negative of `quotes` with \n in template (fixes #7646) (#7647) (Teddy Katz)
* 474e444 Update: add fixer for `sort-imports` (#7535) (Teddy Katz)
* f9b70b3 Docs: Enable example highlighting in rules examples (ref #6444) (#7644) (Alex Guerrero)
* d50f6c1 Fix: incorrect location for `no-useless-escape` errors (fixes #7643) (#7645) (Teddy Katz)
* 54a993c Docs: Fix a typo in the require-yield.md (#7652) (Vse Mozhet Byt)
* eadd808 Chore: Fix prefer-arrow-callback lint errors (#7651) (Kevin Partington)
* 89bd8de New: `require-await` rule (fixes #6820) (#7435) (Toru Nagashima)
* b7432bd Chore: Ensure JS files are checked out with LF (#7624) (Kevin Partington)
* 32a3547 Docs: Add absent quotes in rules documentation (#7625) (Denis Sikuler)
* 5c9a4ad Fix: Prevent `quotes` from fixing templates to directives (fixes #7610) (#7617) (Teddy Katz)
* d90ca46 Upgrade: Update markdownlint dependency to 0.3.1 (fixes #7589) (#7592) (David Anson)
* 07124d1 Docs: add missing quote mark (+=" → "+=") (#7613) (Sean Juarez)
* 8998043 Docs: fix wording in docs for no-extra-parens config (Michael Ficarra)

v3.10.2 - November 15, 2016

* 0643bfe Fix: correctly handle commented code in `indent` autofixer (fixes #7604) (#7606) (Teddy Katz)
* bd0514c Fix: syntax error after `key-spacing` autofix with comment (fixes #7603) (#7607) (Teddy Katz)
* f56c1ef Fix: `indent` crash on parenthesized global return values (fixes #7573) (#7596) (Teddy Katz)
* 100c6e1 Docs: Fix example for curly "multi-or-nest" option (#7597) (Will Chen)
* 6abb534 Docs: Update code of conduct link (#7599) (Nicholas C. Zakas)
* 8302cdb Docs: Update no-tabs to match existing standards & improve readbility (#7590) (Matt Stow)

v3.10.1 - November 14, 2016

* 8a0e92a Fix: handle try/catch correctly in `no-return-await` (fixes #7581) (#7582) (Teddy Katz)
* c4dd015 Fix: no-useless-return stack overflow on unreachable loops (fixes #7583) (#7584) (Teddy Katz)

v3.10.0 - November 11, 2016

* 7ee039b Update: Add comma-style options for calls, fns, imports (fixes #7470) (Max Englander)
* 670e060 Chore: make the `object-shorthand` tests more readable (#7580) (Teddy Katz)
* c3f4809 Update: Allow `func-names` to recognize inferred ES6 names (fixes #7235) (#7244) (Logan Smyth)
* b8d6e48 Fix: syntax errors created by `object-shorthand` autofix (fixes #7574) (#7575) (Teddy Katz)
* 1b3b65c Chore: ensure that files in tests/conf are linted (#7579) (Teddy Katz)
* 2bd1dd7 Update: avoid creating extra whitespace in `arrow-body-style` fixer (#7504) (Teddy Katz)
* 66fe9ff New: `no-return-await` rule. (fixes #7537) (#7547) (Jordan Harband)
* 759525e Chore: Use process.exitCode instead of process.exit() in bin/eslint.js (#7569) (Teddy Katz)
* 0d60db7 Fix: Curly rule doesn't account for leading comment (fixes #7538) (#7539) (Will Chen)
* 5003b1c Update: fix in/instanceof handling with `space-infix-ops` (fixes #7525) (#7552) (Teddy Katz)
* 3e6131e Docs: explain config option merging (#7499) (Danny Andrews)
* 1766524 Update: "Error type should be" assertion in rule-tester (fixes 6106) (#7550) (Frans Jaspers)
* 44eb274 Docs: Missing semicolon report was missing a comma (#7553) (James)
* 6dbda15 Docs: Document the optional defaults argument for RuleTester (#7548) (Teddy Katz)
* e117b80 Docs: typo fix (#7546) (oprogramador)
* 25e5613 Chore: Remove incorrect test from indent.js. (#7531) (Scott Stern)
* c0f4937 Fix: `arrow-parens` supports type annotations (fixes #7406) (#7436) (Toru Nagashima)
* a838b8e Docs: `func-name-matching`: update with “always”/“never” option (#7536) (Jordan Harband)
* 3c379ff Update: `no-restricted-{imports,modules}`: add “patterns” (fixes #6963) (#7433) (Jordan Harband)
* f5764ee Docs: Update example of results returned from `executeOnFiles` (#7362) (Simen Bekkhus)
* 4613ba0 Fix: Add support for escape char in JSX. (#7461) (Scott Stern)
* ea0970d Fix: `curly` false positive with no-semicolon style (#7509) (Teddy Katz)
* af1fde1 Update: fix `brace-style` false negative on multiline node (fixes #7493) (#7496) (Teddy Katz)
* 3798aea Update: max-statements to report function name (refs #7260) (#7399) (Nicholas C. Zakas)
* 0c215fa Update: Add `ArrowFunctionExpression` support to `require-jsdoc` rule (#7518) (Gyandeep Singh)
* 578c373 Build: handle deprecated rules with no 'replacedBy' (refs #7471) (#7494) (Vitor Balocco)
* a7f3976 Docs: Specify min ESLint version for new rule format (#7501) (cowchimp)
* 8a3e717 Update: Fix `lines-around-directive` semicolon handling (fixes #7450) (#7483) (Teddy Katz)
* e58cead Update: add a fixer for certain statically-verifiable `eqeqeq` cases (#7389) (Teddy Katz)
* 0dea0ac Chore: Add Node 7 to travis ci build (#7506) (Gyandeep Singh)
* 36338f0 Update: add fixer for `no-extra-boolean-cast` (#7387) (Teddy Katz)
* 183def6 Chore: enable `prefer-arrow-callback` on ESLint codebase (fixes #6407) (#7503) (Teddy Katz)
* 4f1fa67 Docs: Update copyright (#7497) (Nicholas C. Zakas)

v3.9.1 - October 31, 2016

* 2012258 Fix: incorrect `indent` check for array property access (fixes #7484) (#7485) (Teddy Katz)
* 8a71d4a Fix: `no-useless-return` false positive on conditionals (fixes #7477) (#7482) (Teddy Katz)
* 56a662b Fix: allow escaped backreferences in `no-useless-escape` (fixes #7472) (#7474) (Teddy Katz)
* fffdf13 Build: Fix prefer-reflect rule to not crash site gen build (#7471) (Ilya Volodin)
* 8ba68a3 Docs: Update broken link (#7490) (Devinsuit)
* 65231d8 Docs: add the "fixable" icon for `no-useless-return` (#7480) (Teddy Katz)

v3.9.0 - October 28, 2016

* d933516 New: `no-useless-return` rule (fixes #7309) (#7441) (Toru Nagashima)
* 5e7af30 Update: Add `CallExpression` option for `indent` (fixes #5946) (#7189) (Teddy Katz)
* b200086 Fix: Support type annotations in array-bracket-spacing (#7445) (Jimmy Jia)
* 5ed8b9b Update: Deprecate prefer-reflect (fixes #7226) (#7464) (Kai Cataldo)
* 92ad43b Chore: Update deprecated rules in conf/eslint.json (#7467) (Kai Cataldo)
* e46666b New: Codeframe formatter (fixes #5860) (#7437) (Vitor Balocco)
* fe0d903 Upgrade: Shelljs to ^0.7.5 (fixes #7316) (#7465) (Gyandeep Singh)
* 1d5146f Update: fix wrong indentation about `catch`,`finally` (#7371) (Toru Nagashima)
* 77e3a34 Chore: Pin mock-fs dev dependency (#7466) (Gyandeep Singh)
* c675d7d Update: Fix `no-useless-escape` false negative in regexes (fixes #7424) (#7425) (Teddy Katz)
* ee3bcea Update: add fixer for `newline-after-var` (fixes #5959) (#7375) (Teddy Katz)
* 6e9ff08 Fix: indent.js to support multiline array statements. (#7237) (Scott Stern)
* f8153ad Build: Ensure absolute links in docs retain .md extensions (fixes #7419) (#7438) (Teddy Katz)
* 16367a8 Fix: Return statement spacing. Fix for indent rule. (fixes #7164) (#7197) (Imad Elyafi)
* 3813988 Update: fix false negative of `no-extra-parens` (fixes #7122) (#7432) (Toru Nagashima)
* 23062e2 Docs: Fix typo in no-unexpected-multiline (fixes #7442) (#7447) (Denis Sikuler)
* d257428 Update: `func-name-matching`: add “always”/“never” option (fixes #7391) (#7428) (Jordan Harband)
* c710584 Fix: support for MemberExpression with function body. (#7400) (Scott Stern)
* 2c8ed2d Build: ensure that all files are linted on bash (fixes #7426) (#7427) (Teddy Katz)
* 18ff70f Chore: Enable `no-useless-escape` (#7403) (Vitor Balocco)
* 8dfd802 Fix: avoid `camelcase` false positive with NewExpressions (fixes #7363) (#7409) (Teddy Katz)
* e8159b4 Docs: Fix typo and explain static func calls for class-methods-use-this (#7421) (Scott O'Hara)
* 85d7e24 Docs: add additional examples for MemberExpressions in Indent rule. (#7408) (Scott Stern)
* 2aa1107 Docs: Include note on fatal: true in the node.js api section (#7376) (Simen Bekkhus)
* e064a25 Update: add fixer for `arrow-body-style` (#7240) (Teddy Katz)
* e0fe727 Update: add fixer for `brace-style` (fixes #7074) (#7347) (Teddy Katz)
* cbbe420 New: Support enhanced parsers (fixes #6974) (#6975) (Nicholas C. Zakas)
* 644d25b Update: Add an ignoreRegExpLiterals option to max-len (fixes #3229) (#7346) (Wilfred Hughes)
* 6875576 Docs: Remove broken links to jslinterrors.com (fixes #7368) (#7369) (Dannii Willis)

v3.8.1 - October 17, 2016

* 681c78a Fix: `comma-dangle` was confused by type annotations (fixes #7370) (#7372) (Toru Nagashima)
* 7525042 Fix: Allow useless escapes in tagged template literals (fixes #7383) (#7384) (Teddy Katz)
* 9106964 Docs: Fix broken link for stylish formatter (#7386) (Vitor Balocco)
* 49d3c1b Docs: Document the deprecated meta property (#7367) (Randy Coulman)
* 19d2996 Docs: Relax permission for merging PRs (refs eslint/tsc-meetings#20) (#7360) (Brandon Mills)

v3.8.0 - October 14, 2016

* ee60acf Chore: add integration tests for autofixing (fixes #5909) (#7349) (Teddy Katz)
* c8796e9 Update: `comma-dangle` supports trailing function commas (refs #7101) (#7181) (Toru Nagashima)
* c4abaf0 Update: `space-before-function-paren` supports async/await (refs #7101) (#7180) (Toru Nagashima)
* d0d3b28 Fix: id-length rule incorrectly firing on member access (fixes #6475) (#7365) (Burak Yiğit Kaya)
* 2729d94 Fix: Don't report setter params in class bodies as unused (fixes #7351) (#7352) (Teddy Katz)
* 0b85004 Chore: Enable prefer-template (fixes #6407) (#7357) (Kai Cataldo)
* ca1947b Chore: Update pull request template (refs eslint/tsc-meetings#20) (#7359) (Brandon Mills)
* d840afe Docs: remove broken link from no-loop-func doc (#7342) (Michael McDermott)
* 5266793 Update: no-useless-escape checks template literals (fixes #7331) (#7332) (Kai Cataldo)
* b08fb91 Update: add source property to LintResult object (fixes #7098) (#7304) (Vitor Balocco)
* 0db4164 Chore: run prefer-template autofixer on test files (refs #6407) (#7354) (Kai Cataldo)
* c1470b5 Update: Make the `prefer-template` fixer unescape quotes (fixes #7330) (#7334) (Teddy Katz)
* 5d08c33 Fix: Handle parentheses correctly in `yoda` fixer (fixes #7326) (#7327) (Teddy Katz)
* cd72bba New: `func-name-matching` rule (fixes #6065) (#7063) (Annie Zhang)
* 55b5146 Fix: `RuleTester` didn't support `mocha --watch` (#7287) (Toru Nagashima)
* f8387c1 Update: add fixer for `prefer-spread` (#7283) (Teddy Katz)
* 52da71e Fix: Don't require commas after rest properties (fixes #7297) (#7298) (Teddy Katz)
* 3b11d3f Chore: refactor `no-multiple-empty-lines` (#7314) (Teddy Katz)
* 16d495d Docs: Updating CLI overview with latest changes (#7335) (Kevin Partington)
* 52dfce5 Update: add fixer for `one-var-declaration-per-line` (#7295) (Teddy Katz)
* 0e994ae Update: Improve the error messages for `no-unused-vars` (fixes #7282) (#7315) (Teddy Katz)
* 93214aa Chore: Convert non-lib/test files to template literals (refs #6407) (#7329) (Kai Cataldo)
* 72f394d Update: Fix false negative of `no-multiple-empty-lines` (fixes #7312) (#7313) (Teddy Katz)
* 756bc5a Update: Use characters instead of code units for `max-len` (#7299) (Teddy Katz)
* c9a7ec5 Fix: Improving optionator configuration for --print-config (#7206) (Kevin Partington)
* 51bfade Fix: avoid `object-shorthand` crash with spread properties (fixes #7305) (#7306) (Teddy Katz)
* a12d1a9 Update: add fixer for `no-lonely-if` (#7202) (Teddy Katz)
* 1418384 Fix: Don't require semicolons before `++`/`--` (#7252) (Adrian Heine né Lang)
* 2ffe516 Update: add fixer for `curly` (#7105) (Teddy Katz)
* ac3504d Update: add functionPrototypeMethods to wrap-iife (fixes #7212) (#7284) (Eli White)
* 5e16fb4 Update: add fixer for `no-extra-bind` (#7236) (Teddy Katz)

v3.7.1 - October 3, 2016

* 3dcae13 Fix: Use the correct location for `comma-dangle` errors (fixes #7291) (#7292) (Teddy Katz)
* cb7ba6d Fix: no-implicit-coercion should not fix ~. (fixes #7272) (#7289) (Eli White)
* ce590e2 Chore: Add additional tests for bin/eslint.js (#7290) (Teddy Katz)
* 8ec82ee Docs: change links of templates to raw data (#7288) (Toru Nagashima)

v3.7.0 - September 30, 2016

* 2fee8ad Fix: object-shorthand's consistent-as-needed option (issue #7214) (#7215) (Naomi Jacobs)
* c05a19c Update: add fixer for `prefer-numeric-literals` (#7205) (Teddy Katz)
* 2f171f3 Update: add fixer for `no-undef-init` (#7210) (Teddy Katz)
* 876d747 Docs: Steps for adding new committers/TSCers (#7221) (Nicholas C. Zakas)
* dffb4fa Fix: `no-unused-vars` false positive (fixes #7250) (#7258) (Toru Nagashima)
* 4448cec Docs: Adding missing ES8 reference to configuring (#7271) (Kevin Partington)
* 332d213 Update: Ensure `indent` handles nested functions correctly (fixes #7249) (#7265) (Teddy Katz)
* c36d842 Update: add fixer for `no-useless-computed-key` (#7207) (Teddy Katz)
* 18376cf Update: add fixer for `lines-around-directive` (#7217) (Teddy Katz)
* f8e8fab Update: add fixer for `wrap-iife` (#7196) (Teddy Katz)
* 558b444 Docs: Add @not-an-aardvark to development team (#7279) (Ilya Volodin)
* cd1dc57 Update: Add a fixer for `dot-location` (#7186) (Teddy Katz)
* 89787b2 Update: for `yoda`, add a fixer (#7199) (Teddy Katz)
* 742ae67 Fix: avoid indent and no-mixed-spaces-and-tabs conflicts (fixes #7248) (#7266) (Teddy Katz)
* 85b8714 Fix: Use error templates even when reading from stdin (fixes #7213) (#7223) (Teddy Katz)
* 66adac1 Docs: correction in prefer-reflect docs (fixes #7069) (#7150) (Scott Stern)
* e3f95de Update: Fix `no-extra-parens` false negative (fixes #7229) (#7231) (Teddy Katz)
* 2909c19 Docs: Fix typo in object-shorthand docs (#7267) (Brian Donovan)
* 7bb800d Chore: add internal rule to enforce meta.docs conventions (fixes #6954) (#7155) (Vitor Balocco)
* 722c68c Docs: add code fences to the issue template (#7254) (Teddy Katz)

v3.6.1 - September 26, 2016

* b467436 Upgrade: Upgrade Espree to 3.3.1 (#7253) (Ilya Volodin)
* 299a563 Build: Do not strip .md extension from absolute URLs (#7222) (Kai Cataldo)
* 27042d2 Chore: removed unused code related to scopeMap (#7218) (Yang Su)
* d154204 Chore: Lint bin/eslint.js (#7243) (Kevin Partington)
* 87625fa Docs: Improve eol-last examples in docs (#7227) (Chainarong Tangsurakit)
* de8eaa4 Docs: `class-methods-use-this`: fix option name (#7224) (Jordan Harband)
* 2355f8d Docs: Add Brunch plugin to integrations (#7225) (Aleksey Shvayka)
* a5817ae Docs: Default option from `operator-linebreak` is `after`and not always (#7228) (Konstantin Pschera)

v3.6.0 - September 23, 2016

* 1b05d9c Update: add fixer for `strict` (fixes #6668) (#7198) (Teddy Katz)
* 0a36138 Docs: Update ecmaVersion instructions (#7195) (Nicholas C. Zakas)
* aaa3779 Update: Allow `space-unary-ops` to handle await expressions (#7174) (Teddy Katz)
* 91bf477 Update: add fixer for `prefer-template` (fixes #6978) (#7165) (Teddy Katz)
* 745343f Update: `no-extra-parens` supports async/await (refs #7101) (#7178) (Toru Nagashima)
* 8e1fee1 Fix: Handle number literals correctly in `no-whitespace-before-property` (#7185) (Teddy Katz)
* 462a3f7 Update: `keyword-spacing` supports async/await (refs #7101) (#7179) (Toru Nagashima)
* 709a734 Update: Allow template string in `valid-typeof` comparison (fixes #7166) (#7168) (Teddy Katz)
* f71937a Fix: Don't report async/generator callbacks in `array-callback-return` (#7172) (Teddy Katz)
* 461b015 Fix: Handle async functions correctly in `prefer-arrow-callback` fixer (#7173) (Teddy Katz)
* 7ea3e4b Fix: Handle await expressions correctly in `no-unused-expressions` (#7175) (Teddy Katz)
* 16bb802 Update: Ensure `arrow-parens` handles async arrow functions correctly (#7176) (Teddy Katz)
* 2d10657 Chore: add tests for `generator-star-spacing` and async (refs #7101) (#7182) (Toru Nagashima)
* c118d21 Update: Let `no-restricted-properties` check destructuring (fixes #7147) (#7151) (Teddy Katz)
* 9e0b068 Fix: valid-jsdoc does not throw on FieldType without value (fixes #7184) (#7187) (Kai Cataldo)
* 4b5d9b7 Docs: Update process for evaluating proposals (fixes #7156) (#7183) (Kai Cataldo)
* 95c777a Update: Make `no-restricted-properties` more flexible (fixes #7137) (#7139) (Teddy Katz)
* 0fdf23c Update: fix `quotes` rule's false negative (fixes #7084) (#7141) (Toru Nagashima)
* f2a789d Update: fix `no-unused-vars` false negative (fixes #7124) (#7143) (Toru Nagashima)
* 6148d85 Fix: Report columns for `eol-last` correctly (fixes #7136) (#7149) (kdex)
* e016384 Update: add fixer for quote-props (fixes #6996) (#7095) (Teddy Katz)
* 35f7be9 Upgrade: espree to 3.2.0, remove tests with SyntaxErrors (fixes #7169) (#7170) (Teddy Katz)
* 28ddcf8 Fix: `max-len`: `ignoreTemplateLiterals`: handle 3+ lines (fixes #7125) (#7138) (Jordan Harband)
* 660e091 Docs: Update rule descriptions (fixes #5912) (#7152) (Kenneth Williams)
* 8b3fc32 Update: Make `indent` report lines with mixed spaces/tabs (fixes #4274) (#7076) (Teddy Katz)
* b39ac2c Update: add fixer for `no-regex-spaces` (#7113) (Teddy Katz)
* cc80467 Docs: Update PR templates for formatting (#7128) (Nicholas C. Zakas)
* 76acbb5 Fix: include LogicalExpression in indent length calc  (fixes #6731) (#7087) (Alec)
* a876673 Update: no-implicit-coercion checks TemplateLiterals (fixes #7062) (#7121) (Kai Cataldo)
* 8db4f0c Chore: Enable `typeof` check for `no-undef` rule in eslint-config-eslint (#7103) (Teddy Katz)
* 7e8316f Docs: Update release process (#7127) (Nicholas C. Zakas)
* 22edd8a Update: `class-methods-use-this`: `exceptMethods` option (fixes #7085) (#7120) (Jordan Harband)
* afd132a Fix: line-comment-position "above" string option now works (fixes #7100) (#7102) (Kevin Partington)
* 1738b2e Chore: fix name of internal-no-invalid-meta test file (#7142) (Vitor Balocco)
* ac0bb62 Docs: Fixes examples for allowTemplateLiterals (fixes #7115) (#7135) (Zoe Ingram)
* bcfa3e5 Update: Add `always`/`never` option to `eol-last` (fixes #6938) (#6952) (kdex)
* 0ca26d9 Docs: Distinguish examples for space-before-blocks (#7132) (Timo Tijhof)
* 9a2aefb Chore: Don't require an issue reference in check-commit npm script (#7104) (Teddy Katz)
* c85fd84 Fix: max-statements-per-line rule to force minimum to be 1 (fixes #7051) (#7092) (Scott Stern)
* e462e47 Docs: updates category of no-restricted-properties (fixes #7112) (#7118) (Alec)
* 6ae660b Fix: Don't report comparisons of two typeof expressions (fixes #7078) (#7082) (Teddy Katz)
* 710f205 Docs: Fix typos in Issues section of Maintainer's Guide (#7114) (Kai Cataldo)
* 546a3ca Docs: Clarify that linter does not process configuration (fixes #7108) (#7110) (Kevin Partington)
* 0d50943 Docs: Elaborate on `guard-for-in` best practice (fixes #7071) (#7094) (Dallon Feldner)
* 58e6d76 Docs: Fix examples for no-restricted-properties (#7099) (not-an-aardvark)
* 6cfe519 Docs: Corrected typo in line-comment-position rule doc (#7097) (Alex Mercier)
* f02e52a Docs: Add fixable note to no-implicit-coercion docs (#7096) (Brandon Mills)

v3.5.0 - September 9, 2016

* 08fa538 Update: fix false negative of `arrow-spacing` (fixes #7079) (#7080) (Toru Nagashima)
* cec65e3 Update: add fixer for no-floating-decimal (fixes #7070) (#7081) (not-an-aardvark)
* 2a3f699 Fix: Column number for no-multiple-empty-lines (fixes #7086) (#7088) (Ian VanSchooten)
* 6947299 Docs: Add info about closing accepted issues to docs (fixes #6979) (#7089) (Kai Cataldo)
* d30157a Docs: Add link to awesome-eslint in integrations page (#7090) (Vitor Balocco)
* 457be1b Docs: Update so issues are not required (fixes #7015) (#7072) (Nicholas C. Zakas)
* d9513b7 Fix: Allow linting of .hidden files/folders (fixes #4828) (#6844) (Ian VanSchooten)
* 6d97c18 New: `max-len`: `ignoreStrings`+`ignoreTemplateLiterals` (fixes #5805) (#7049) (Jordan Harband)
* 538d258 Update: make no-implicit-coercion support autofixing. (fixes #7056) (#7061) (Eli White)
* 883316d Update: add fixer for prefer-arrow-callback (fixes #7002) (#7004) (not-an-aardvark)
* 7502eed Update: auto-fix for `comma-style` (fixes #6941) (#6957) (Gyandeep Singh)
* 645dda5 Update: add fixer for dot-notation (fixes #7014) (#7054) (not-an-aardvark)
* 2657846 Fix: `no-console` ignores user-defined console (fixes #7010) (#7058) (Toru Nagashima)
* 656bb6e Update: add fixer for newline-before-return (fixes #5958) (#7050) (Vitor Balocco)
* 1f995c3 Fix: no-implicit-coercion string concat false positive (fixes #7057) (#7060) (Kai Cataldo)
* 6718749 Docs: Clarify that `es6` env also sets `ecmaVersion` to 6 (#7067) (Jérémie Astori)
* e118728 Update: add fixer for wrap-regex (fixes #7013) (#7048) (not-an-aardvark)
* f4fcd1e Update: add more `indent` options for functions (fixes #6052) (#7043) (not-an-aardvark)
* 657eee5 Update: add fixer for new-parens (fixes #6994) (#7047) (not-an-aardvark)
* ff19aa9 Update: improve `max-statements-per-line` message (fixes #6287) (#7044) (Jordan Harband)
* 3960617 New: `prefer-numeric-literals` rule (fixes #6068) (#7029) (Annie Zhang)
* fa760f9 Chore: no-regex-spaces uses internal rule message format (fixes #7052) (#7053) (Kevin Partington)
* 22c7e09 Update: no-magic-numbers false negative on reassigned vars (fixes #4616) (#7028) (not-an-aardvark)
* be29599 Update: Throw error if whitespace found in plugin name (fixes #6854) (#6960) (Jesse Ostrander)
* 4063a79 Fix: Rule message placeholders can be inside braces (fixes #6988) (#7041) (Kevin Partington)
* 52e8d9c Docs: Clean up sort-vars (#7045) (Matthew Dunsdon)
* 4126f12 Chore: Rule messages use internal rule message format (fixes #6977) (#6989) (Kevin Partington)
* 46cb690 New: `no-restricted-properties` rule (fixes #3218) (#7017) (Eli White)
* 00b3042 Update: Pass file path to parse function (fixes #5344) (#7024) (Annie Zhang)
* 3f13325 Docs: Add kaicataldo and JamesHenry to our teams (#7039) (alberto)
* 8e77f16 Update: `new-parens` false negative (fixes #6997) (#6999) (Toru Nagashima)
* 326f457 Docs: Add missing 'to' in no-restricted-modules (#7022) (Oskar Risberg)
* 8277357 New: `line-comment-position` rule (fixes #6077) (#6953) (alberto)
* c1f0d76 New: `lines-around-directive` rule (fixes #6069) (#6998) (Kai Cataldo)
* 61f1de0 Docs: Fix typo in no-debugger (#7019) (Denis Ciccale)
* 256c4a2 Fix: Allow separate mode option for multiline and align (fixes #6691) (#6991) (Annie Zhang)
* a989a7c Docs: Declaring dependency on eslint in shared config (fixes #6617) (#6985) (alberto)
* 6869c60 Docs: Fix minor typo in no-extra-parens doc (#6992) (Jérémie Astori)
* 28f1619 Docs: Update the example of SwitchCase (#6981) (fish)

v3.4.0 - August 26, 2016

* c210510 Update: add fixer for no-extra-parens (fixes #6944) (#6950) (not-an-aardvark)
* ca3d448 Fix: `prefer-const` false negative about `eslintUsed` (fixes #5837) (#6971) (Toru Nagashima)
* 1153955 Docs: Draft of JSCS migration guide (refs #5859) (#6942) (Nicholas C. Zakas)
* 3e522be Fix: false negative of `indent` with `else if` statements (fixes #6956) (#6965) (not-an-aardvark)
* 2dfb290 Docs: Distinguish examples in rules under Stylistic Issues part 7 (#6760) (Kenneth Williams)
* 3c710c9 Fix: rename "AirBnB" => "Airbnb" init choice (fixes #6969) (Harrison Shoff)
* 7660b39 Fix: `object-curly-spacing` for type annotations (fixes #6940) (#6945) (Toru Nagashima)
* 21ab784 New: do not remove non visited files from cache. (fixes #6780) (#6921) (Roy Riojas)
* 3a1763c Fix: enable `@scope/plugin/ruleId`-style specifier (refs #6362) (#6939) (Toru Nagashima)
* d6fd064 Update: Add never option to multiline-ternary (fixes #6751) (#6905) (Kai Cataldo)
* 0d268f1 New: `symbol-description` rule (fixes #6778) (#6825) (Jarek Rencz)
* a063d4e Fix: no-cond-assign within a function expression (fixes #6908) (#6909) (Patrick McElhaney)
* 16db93a Build: Tag docs, publish release notes (fixes #6892) (#6934) (Nicholas C. Zakas)
* 0cf1d55 Chore: Fix object-shorthand errors (fixes #6958) (#6959) (Kai Cataldo)
* 8851ddd Fix: Improve pref of globbing by inheriting glob.GlobSync (fixes #6710) (#6783) (Kael Zhang)
* cf2242c Update: `requireStringLiterals` option for `valid-typeof` (fixes #6698) (#6923) (not-an-aardvark)
* 8561389 Fix: `no-trailing-spaces` wrong fixing (fixes #6933) (#6937) (Toru Nagashima)
* 6a92be5 Docs: Update semantic versioning policy (#6935) (alberto)
* a5189a6 New: `class-methods-use-this` rule (fixes #5139) (#6881) (Gyandeep Singh)
* 1563808 Update: add support for ecmaVersion 20xx (fixes #6750) (#6907) (Kai Cataldo)
* d8b770c Docs: Change rule descriptions for consistent casing (#6915) (Brandon Mills)
* c676322 Chore: Use object-shorthand batch 3 (refs #6407) (#6914) (Kai Cataldo)

v3.3.1 - August 15, 2016

* a2f06be Build: optimize rule page title for small browser tabs (fixes #6888) (#6904) (Vitor Balocco)
* 02a00d6 Docs: clarify rule details for no-template-curly-in-string (#6900) (not-an-aardvark)
* b9b3446 Fix: sort-keys ignores destructuring patterns (fixes #6896) (#6899) (Kai Cataldo)
* 3fe3a4f Docs: Update options in `object-shorthand` (#6898) (Grant Snodgrass)
* cd09c96 Chore: Use object-shorthand batch 2 (refs #6407) (#6897) (Kai Cataldo)
* 2841008 Chore: Use object-shorthand batch 1 (refs #6407) (#6893) (Kai Cataldo)

v3.3.0 - August 12, 2016

* 683ac56 Build: Add CI release scripts (fixes #6884) (#6885) (Nicholas C. Zakas)
* ebf8441 Update: `prefer-rest-params` relax for member accesses (fixes #5990) (#6871) (Toru Nagashima)
* df01c4f Update: Add regex support for exceptions (fixes #5187) (#6883) (Annie Zhang)
* 055742c Fix: `no-dupe-keys` type errors (fixes #6886) (#6889) (Toru Nagashima)
* e456fd3 New: `sort-keys` rule (fixes #6076) (#6800) (Toru Nagashima)
* 3e879fc Update: Rule "eqeqeq" to have more specific null handling (fixes #6543) (#6849) (Simon Sturmer)
* e8cb7f9 Chore: use eslint-plugin-node (refs #6407) (#6862) (Toru Nagashima)
* e37bbd8 Docs: Remove duplicate statement (#6878) (Richard Käll)
* 11395ca Fix: `no-dupe-keys` false negative (fixes #6801) (#6863) (Toru Nagashima)
* 1ecd2a3 Update: improve error message in `no-control-regex` (#6839) (Jordan Harband)
* d610d6c Update: make `max-lines` report the actual number of lines (fixes #6766) (#6764) (Jarek Rencz)
* b256c50 Chore: Fix glob for core js files for lint (fixes #6870) (#6872) (Gyandeep Singh)
* f8ab8f1 New: func-call-spacing rule (fixes #6080) (#6749) (Brandon Mills)
* be68f0b New: no-template-curly-in-string rule (fixes #6186) (#6767) (Jeroen Engels)
* 80789ab Chore: don't throw if rule is in old format (fixes #6848) (#6850) (Vitor Balocco)
* d47c505 Fix: `newline-after-var` false positive (fixes #6834) (#6847) (Toru Nagashima)
* bf0afcb Update: validate void operator in no-constant-condition (fixes #5726) (#6837) (Vitor Balocco)
* 5ef839e New: Add consistent and ..-as-needed to object-shorthand (fixes #5438) (#5439) (Martijn de Haan)
* 7e1bf01 Fix: update peerDependencies of airbnb option for `--init` (fixes #6843) (#6846) (Vitor Balocco)
* 8581f4f Fix: `no-invalid-this` false positive (fixes #6824) (#6827) (Toru Nagashima)
* 90f78f4 Update: add `props` option to `no-self-assign` rule (fixes #6718) (#6721) (Toru Nagashima)
* 30d71d6 Update: 'requireForBlockBody' modifier for 'arrow-parens' (fixes #6557) (#6558) (Nicolas Froidure)
* cdded07 Chore: use native `Object.assign` (refs #6407) (#6832) (Gyandeep Singh)
* 579ec49 Chore: Add link to rule change guidelines in "needs info" template (fixes #6829) (#6831) (Kevin Partington)
* 117e7aa Docs: Remove incorrect "constructor" statement from `no-new-symbol` docs (#6830) (Jarek Rencz)
* aef18b4 New: `no-unsafe-negation` rule (fixes #2716) (#6789) (Toru Nagashima)
* d94e945 Docs: Update Getting Started w/ Readme installation instructions (#6823) (Kai Cataldo)
* dfbc112 Upgrade: proxyquire to 1.7.10 (fixes #6821) (#6822) (alberto)
* 4c5e911 Chore: enable `prefer-const` and apply it to our codebase (refs #6407) (#6805) (Toru Nagashima)
* e524d16 Update: camelcase rule fix for import declarations (fixes #6755) (#6784) (Lorenzo Zottar)
* 8f3509d Update: make `eslint:all` excluding deprecated rules (fixes #6734) (#6756) (Toru Nagashima)
* 2b17459 New: `no-global-assign` rule (fixes #6586) (#6746) (alberto)

v3.2.2 - August 1, 2016

* 510ce4b Upgrade: file-entry-cache@^1.3.1 (fixes #6816, refs #6780) (#6819) (alberto)
* 46b14cd Fix: ignore MemberExpression in VariableDeclarators (fixes #6795) (#6815) (Nicholas C. Zakas)

v3.2.1 - August 1, 2016

* 584577a Build: Pin file-entry-cache to avoid licence issue (refs #6816) (#6818) (alberto)
* 38d0d23 Docs: clarify minor releases and suggest using `~ to version (#6804) (Henry Zhu)
* 4ca809e Fix: Normalizes messages so all end with a period (fixes #6762) (#6807) (Patrick McElhaney)
* c7488ac Fix: Make MemberExpression option opt-in (fixes #6797) (#6798) (Rich Trott)
* 715e8fa Docs: Update issue closing policy (fixes #6765) (#6808) (Nicholas C. Zakas)
* 288f7bf Build: Fix site generation (fixes #6791) (#6793) (Nicholas C. Zakas)
* 261a9f3 Docs: Update JSCS status in README (#6802) (alberto)
* 5ae0887 Docs: Update no-void.md (#6799) (Daniel Hritzkiv)

v3.2.0 - July 29, 2016

* 2438ee2 Upgrade: Update markdownlint dependency to 0.2.0 (fixes #6781) (#6782) (David Anson)
* 4fc0018 Chore: dogfooding `no-var` rule and remove `var`s (refs #6407) (#6757) (Toru Nagashima)
* b22eb5c New: `no-tabs` rule (fixes #6079) (#6772) (Gyandeep Singh)
* ddea63a Chore: Updated no-control-regex tests to cover all cases (fixes #6438) (#6752) (Efe Gürkan YALAMAN)
* 1025772 Docs: Add plugin example to disabling with comments guide (fixes #6742) (#6747) (Brandon Mills)
* 628aae4 Docs: fix inconsistent spacing inside block comment (#6768) (Brian Jacobel)
* 2983c32 Docs: Add options to func-names config comments (#6748) (Brandon Mills)
* 2f94443 Docs: fix wrong path (#6763) (molee1905)
* 6f3faa4 Revert "Build: Remove support for Node v5 (fixes #6743)" (#6758) (Nicholas C. Zakas)
* 99dfd1c Docs: fix grammar issue in rule-changes page (#6761) (Vitor Balocco)
* e825458 Fix: Rule no-unused-vars had missing period (fixes #6738) (#6739) (Brian Mock)
* 71ae64c Docs: Clarify cache file deletion (fixes #4943) (#6712) (Nicholas C. Zakas)
* 26c85dd Update: merge warnings of consecutive unreachable nodes (fixes #6583) (#6729) (Toru Nagashima)
* 106e40b Fix: Correct grammar in object-curly-newline reports (fixes #6725) (#6728) (Vitor Balocco)
* e00754c Chore: Dogfooding ES6 rules (refs #6407) (#6735) (alberto)
* 181b26a Build: Remove support for Node v5 (fixes #6743) (#6744) (alberto)
* 5320a6c Update: `no-use-before-define` false negative on for-in/of (fixes #6699) (#6719) (Toru Nagashima)
* a2090cb Fix: space-infix-ops doesn't fail for  type annotations(fixes #5211) (#6723) (Nicholas C. Zakas)
* 9c36ecf Docs: Add @vitorbal and @platinumazure to development team (Ilya Volodin)
* e09d1b8 Docs: describe all RuleTester options (fixes #4810, fixes #6709) (#6711) (Nicholas C. Zakas)
* a157f47 Chore: Update CLIEngine option desc (fixes #5179) (#6713) (Nicholas C. Zakas)
* a0727f9 Chore: fix `.gitignore` for vscode (refs #6383) (#6720) (Toru Nagashima)
* 75d2d43 Docs: Clarify Closure type hint expectation (fixes #5231) (#6714) (Nicholas C. Zakas)
* 95ea25a Update: Check indentation of multi-line chained properties (refs #1801) (#5940) (Rich Trott)
* e7b1e1c Docs: Edit issue/PR waiting period docs (fixes #6009) (#6715) (Nicholas C. Zakas)
* 053aa0c Update: Added 'allowSuper' option to `no-underscore-dangle` (fixes #6355) (#6662) (peteward44)
* 8929045 Build: Automatically generate rule index (refs #2860) (#6658) (Ilya Volodin)
* f916ae5 Docs: Fix multiline-ternary typos (#6704) (Cédric Malard)
* c64b0c2 Chore: First ES6 refactoring (refs #6407) (#6570) (Nicholas C. Zakas)

v3.1.1 - July 18, 2016

* 565e584 Fix: `eslint:all` causes regression in 3.1.0 (fixes #6687) (#6696) (alberto)
* cb90359 Fix: Allow named recursive functions (fixes #6616) (#6667) (alberto)
* 3f206dd Fix: `balanced` false positive in `spaced-comment` (fixes #6689) (#6692) (Grant Snodgrass)
* 57f1676 Docs: Add missing brackets from code examples (#6700) (Plusb Preco)
* 124f066 Chore: Remove fixable key from multiline-ternary metadata (fixes #6683) (#6688) (Kai Cataldo)
* 9f96086 Fix: Escape control characters in XML. (fixes #6673) (#6672) (George Chung)

v3.1.0 - July 15, 2016

* e8f8c6c Fix: incorrect exitCode when eslint is called with --stdin (fixes #6677) (#6682) (Steven Humphrey)
* 38639bf Update: make `no-var` fixable (fixes #6639) (#6644) (Toru Nagashima)
* dfc20e9 Fix: `no-unused-vars` false positive in loop (fixes #6646) (#6649) (Toru Nagashima)
* 2ba75d5 Update: relax outerIIFEBody definition (fixes #6613) (#6653) (Stephen E. Baker)
* 421e4bf Chore: combine multiple RegEx replaces with one (fixes #6669) (#6661) (Sakthipriyan Vairamani)
* 089ee2c Docs: fix typos,wrong path,backticks (#6663) (molee1905)
* ef827d2 Docs: Add another pre-commit hook to integrations (#6666) (David Alan Hjelle)
* a343b3c Docs: Fix option typo in no-underscore-dangle (Fixes #6674) (#6675) (Luke Page)
* 5985eb2 Chore: add internal rule that validates meta property (fixes #6383) (#6608) (Vitor Balocco)
* 4adb15f Update: Add `balanced` option to `spaced-comment` (fixes #4133) (#6575) (Annie Zhang)
* 1b13c25 Docs: fix incorrect example being mark as correct (#6660) (David Björklund)
* a8b4e40 Fix: Install required eslint plugin for "standard" guide (fixes #6656) (#6657) (Feross Aboukhadijeh)
* 720686b New: `endLine` and `endColumn` of the lint result. (refs #3307) (#6640) (Toru Nagashima)
* 54faa46 Docs: Small tweaks to CLI documentation (fixes #6627) (#6642) (Kevin Partington)
* e108850 Docs: Added examples and structure to `padded-blocks` (fixes #6628) (#6643) (alberto)
* 350e1c0 Docs: Typo (#6650) (Peter Rood)
* b837c92 Docs: Correct a term in max-len.md (fixes #6637) (#6641) (Vse Mozhet Byt)
* baeb313 Fix: Warning behavior for executeOnText (fixes #6611) (#6632) (Nicholas C. Zakas)
* e6004be Chore: Enable preferType in valid-jsdoc (refs #5188) (#6634) (Nicholas C. Zakas)
* ca323cf Fix: Use default assertion messages (fixes #6532) (#6615) (Dmitrii Abramov)
* 2bdf22c Fix: Do not throw exception if baseConfig is provided (fixes #6605) (#6625) (Kevin Partington)
* e42cacb Upgrade: mock-fs to 3.10, fixes for Node 6.3 (fixes #6621) (#6624) (Tim Schaub)
* 8a263ae New: multiline-ternary rule (fixes #6066) (#6590) (Kai Cataldo)
* e951303 Update: Adding new `key-spacing` option (fixes #5613) (#5907) (Kyle Mendes)
* 10c3e91 Docs: Remove reference from 3.0.0 migration guide (refs #6605) (#6618) (Kevin Partington)
* 5010694 Docs: Removed non-existing resource (#6609) (Moritz Kröger)
* 6d40d85 Docs: Note that PR requires ACCEPTED issue (refs #6568) (#6604) (Patrick McElhaney)

v3.0.1 - July 5, 2016

* 27700cf Fix: `no-unused-vars` false positive around callback (fixes #6576) (#6579) (Toru Nagashima)
* 124d8a3 Docs: Pull request template (#6568) (Nicholas C. Zakas)
* e9a2ed9 Docs: Fix rules\id-length exceptions typos (fixes #6397) (#6593) (GramParallelo)
* a2cfa1b Fix: Make outerIIFEBody work correctly (fixes #6585) (#6596) (Nicholas C. Zakas)
* 9c451a2 Docs: Use string severity in example (#6601) (Kenneth Williams)
* 8308c0b Chore: remove path-is-absolute in favor of the built-in (fixes #6598) (#6600) (shinnn)
* 7a63717 Docs: Add missing pull request step (fixes #6595) (#6597) (Nicholas C. Zakas)
* de3ed84 Fix: make `no-unused-vars` ignore for-in (fixes #2342) (#6126) (Oleg Gaidarenko)
* 6ef2cbe Fix: strip Unicode BOM of config files (fixes #6556) (#6580) (Toru Nagashima)
* ee7fcfa Docs: Correct type of `outerIIFEBody` in `indent` (fixes #6581) (#6584) (alberto)
* 25fc7b7 Fix: false negative of `max-len` (fixes #6564) (#6565) (not-an-aardvark)
* f6b8452 Docs: Distinguish examples in rules under Stylistic Issues part 6 (#6567) (Kenneth Williams)

v3.0.0 - July 1, 2016

* 66de9d8 Docs: Update installation instructions on README (#6569) (Nicholas C. Zakas)
* dc5b78b Breaking: Add `require-yield` rule to `eslint:recommended` (fixes #6550) (#6554) (Gyandeep Singh)
* 7988427 Fix: lib/config.js tests pass if personal config exists (fixes #6559) (#6566) (Kevin Partington)
* 4c05967 Docs: Update rule docs for new format (fixes #5417) (#6551) (Nicholas C. Zakas)
* 70da5a8 Docs: Correct link to rules page (#fixes 6553) (#6561) (alberto)
* e2b2030 Update: Check RegExp strings for `no-regex-spaces` (fixes #3586) (#6379) (Jackson Ray Hamilton)
* 397e51b Update: Implement outerIIFEBody for indent rule (fixes #6259) (#6382) (David Shepherd)
* 666da7c Docs: 3.0.0 migration guide (#6521) (Nicholas C. Zakas)
* b9bf8fb Docs: Update Governance Policy (fixes #6452) (#6522) (Nicholas C. Zakas)
* 1290657 Update: `no-unused-vars` ignores read it modifies itself (fixes #6348) (#6535) (Toru Nagashima)
* d601f6b Fix: Delete cache only when executing on files (fixes #6459) (#6540) (Kai Cataldo)
* e0d4b19 Breaking: Error thrown/printed if no config found (fixes #5987) (#6538) (Kevin Partington)
* 18663d4 Fix: false negative of `no-useless-rename` (fixes #6502) (#6506) (Toru Nagashima)
* 0a7936d Update: Add fixer for prefer-const (fixes #6448) (#6486) (Nick Heiner)
* c60341f Chore: Update index and `meta` for `"eslint:recommended"` (refs #6403) (#6539) (Mark Pedrotti)
* 73da28d Better wording for the error reported by the rule "no-else-return" #6411 (#6413) (Olivier Thomann)
* e06a5b5 Update: Add fixer for arrow-parens (fixes #4766) (#6501) (madmed88)
* 5f8f3e8 Docs: Remove Box as a sponsor (#6529) (Nicholas C. Zakas)
* 7dfe0ad Docs: fix max-lines samples (fixes #6516) (#6515) (Dmitriy Shekhovtsov)
* fa05119 Breaking: Update eslint:recommended (fixes #6403) (#6509) (Nicholas C. Zakas)
* e96177b Docs: Add "Proposing a Rule Change" link to CONTRIBUTING.md (#6511) (Kevin Partington)
* bea9096 Docs: Update pull request steps (fixes #6474) (#6510) (Nicholas C. Zakas)
* 7bcf6e0 Docs: Consistent example headings & text pt3 (refs #5446) (#6492) (Guy Fraser)
* 1a328d9 Docs: Consistent example headings & text pt4 (refs #5446) (#6493) (Guy Fraser)
* ff5765e Docs: Consistent example headings & text pt2 (refs #5446)(#6491) (Guy Fraser)
* 01384fa Docs: Fixing typos (refs #5446)(#6494) (Guy Fraser)
* 4343ae8 Fix: false negative of `object-shorthand` (fixes #6429) (#6434) (Toru Nagashima)
* b7d8c7d Docs: more accurate yoda-speak (#6497) (Tony Lukasavage)
* 3b0ab0d Fix: add warnIgnored flag to CLIEngine.executeOnText (fixes #6302) (#6305) (Robert Levy)
* c2c6cec Docs: Mark object-shorthand as fixable. (#6485) (Nick Heiner)
* 5668236 Fix: Allow objectsInObjects exception when destructuring (fixes #6469) (#6470) (Adam Renklint)
* 17ac0ae Fix: `strict` rule reports a syntax error for ES2016 (fixes #6405) (#6464) (Toru Nagashima)
* 4545123 Docs: Rephrase documentation for `no-duplicate-imports` (#6463) (Simen Bekkhus)
* 1b133e3 Docs: improve `no-native-reassign` and specifying globals (fixes #5358) (#6462) (Toru Nagashima)
* b179373 Chore: Remove dead code in excuteOnFiles (fixes #6467) (#6466) (Andrew Hutchings)
* 18fbc4b Chore: Simplify eslint process exit code (fixes #6368) (#6371) (alberto)
* 58542e4 Breaking: Drop support for node < 4 (fixes #4483) (#6401) (alberto)
* f50657e Breaking: use default for complexity in eslint:recommended (fixes #6021) (#6410) (alberto)
* 3e690fb Fix: Exit init early if guide is chosen w/ no package.json (fixes #6476) (#6478) (Kai Cataldo)

v2.13.1 - June 20, 2016

* 434de7f Fix: wrong baseDir (fixes #6450) (#6457) (Toru Nagashima)
* 3c9ce09 Fix: Keep indentation when fixing `padded-blocks` "never" (fixes #6454) (#6456) (Ed Lee)
* a9d4cb2 Docs: Fix typo in max-params examples (#6471) (J. William Ashton)
* 1e185b9 Fix: no-multiple-empty-lines errors when no line breaks (fixes #6449) (#6451) (strawbrary)

v2.13.0 - June 17, 2016

* cf223dd Fix: add test for a syntax error (fixes #6013) (#6378) (Toru Nagashima)
* da30cf9 Update: Add fixer for object-shorthand (fixes #6412) (#6418) (Nick Heiner)
* 2cd90eb Chore: Fix rule meta description inconsistencies (refs #5417) (#6422) (Mark Pedrotti)
* d798b2c Added quotes around "classes" option key (#6441) (Guy Fraser)
* 852b6df Docs: Delete empty table of links from Code Path Analysis (#6423) (Mark Pedrotti)
* 5e9117e Chore: sort rules in eslint.json (fixes #6425) (#6426) (alberto)
* c2b5277 Docs: Add gitter chat link to Reporting Bugs (#6430) (Mark Pedrotti)
* 1316db0 Update: Add `never` option for `func-names` (fixes #6059) (#6392) (alberto)
* 1c123e2 Update: Add autofix for `padded-blocks` (fixes #6320) (#6393) (alberto)
* 8ec89c8 Fix: `--print-config` return config inside subdir (fixes #6329) (#6385) (alberto)
* 4f73240 Fix: `object-curly-newline` multiline with comments (fixes #6381) (#6396) (Toru Nagashima)
* 77697a7 Chore: Fake config hierarchy fixtures (fixes #6206) (#6402) (Gyandeep Singh)
* 73a9a6d Docs: Fix links in Configuring ESLint (#6421) (Mark Pedrotti)
* ed84c4c Fix: improve `newline-per-chained-call` message (fixes #6340) (#6360) (Toru Nagashima)
* 9ea4e44 Docs: Update parser reference to `espree` instead of `esprima` (#6404) (alberto)
* 7f57467 Docs: Make `fix` param clearer (fixes #6366) (#6367) (Nick Heiner)
* fb49c7f Fix: nested `extends` with relative path (fixes #6358) (#6359) (Toru Nagashima)
* 5122f73 Update: no-multiple-empty-lines fixer (fixes #6225) (#6226) (Ruurd Moelker)
* 0e7ce72 Docs: Fix rest-spread-spacing's name (#6365) (cody)
* cfdd524 Fix: allow semi as braceless body of statements (fixes #6386) (#6391) (alberto)
* 6b08cfc Docs: key-spacing fixable documenation notes (fixes #6375) (#6376) (Ruurd Moelker)
* 4b4be3b Docs: `max-lines` option: fix `skipComments` typo (#6374) (Jordan Harband)
* 20ab4f6 Docs: Fix wrong link in object-curly-newline (#6373) (Grant Snodgrass)
* 412ce8d Docs: Fix broken links in no-mixed-operators (#6372) (Grant Snodgrass)

v2.12.0 - June 10, 2016

* 54c30fb Update: Add explicit default option `always` for `eqeqeq` (refs #6144) (#6342) (alberto)
* 2d63370 Update: max-len will warn indented comment lines (fixes #6322) (#6324) (Kai Cataldo)
* dcd4ad7 Docs: clarify usage of inline disable comments (fixes #6335) (#6347) (Kai Cataldo)
* c03300b Docs: Clarified how plugin rules look in plugin configs (fixes #6346) (#6351) (Kevin Partington)
* 9c87709 Docs: Add semantic versioning policy (fixes #6244) (#6343) (Nicholas C. Zakas)
* 5affab1 Docs: Describe values under Extending Configuration Files (refs #6240) (#6336) (Mark Pedrotti)
* 2520f5a New: `max-lines` rule (fixes #6078) (#6321) (alberto)
* 9bfbc64 Update: Option for object literals in `arrow-body-style` (fixes #5936) (#6216) (alberto)
* 977cdd5 Chore: remove unused method from FileFinder (fixes #6344) (#6345) (alberto)
* 477fbc1 Docs: Add section about customizing RuleTester (fixes #6227) (#6331) (Jeroen Engels)
* 0e14016 New: `no-mixed-operators` rule (fixes #6023) (#6241) (Toru Nagashima)
* 6e03c4b Update: Add never option to arrow-body-style (fixes #6317) (#6318) (Andrew Hyndman)
* f804397 New: Add `eslint:all` option (fixes #6240) (#6248) (Robert Fletcher)
* dfe05bf Docs: Link JSCS rules to their corresponding page. (#6334) (alberto)
* 1cc4356 Docs: Remove reference to numeric config (fixes #6309) (#6327) (Kevin Partington)
* 2d4efbe Docs: Describe options in rule under Strict Mode (#6312) (Mark Pedrotti)
* c1953fa Docs: Typo fix 'and' -> 'any' (#6326) (Stephen Edgar)
* d49ab4b Docs: Code conventions improvements (#6313) (Kevin Partington)
* 316a507 Fix: one-var allows uninitialized vars in ForIn/ForOf (fixes #5744) (#6272) (Kai Cataldo)
* 6cbee31 Docs: Typo fix 'colum' -> 'column' (#6306) (Andrew Cobby)
* 2663569 New: `object-curly-newline` (fixes #6072) (#6223) (Toru Nagashima)
* 72c2ea5 Update: callback-return allows for object methods (fixes #4711) (#6277) (Kai Cataldo)
* 89580a4 Docs: Distinguish examples in rules under Stylistic Issues part 5 (#6291) (Kenneth Williams)
* 1313804 New: rest-spread-spacing rule (fixes #5391) (#6278) (Kai Cataldo)
* 61dfe68 Fix: `no-useless-rename` false positive in babel-eslint (fixes #6266) (#6290) (alberto)
* c78c8cb Build: Remove commit check from appveyor (fixes #6292) (#6294) (alberto)
* 3e38fc1 Chore: more tests for comments at the end of blocks (refs #6090) (#6273) (Kai Cataldo)
* 38dccdd Docs: `--no-ignore` disables all forms of ignore (fixes #6260) (#6304) (alberto)
* bb69380 Fix: no-useless-rename handles ExperimentalRestProperty (fixes #6284) (#6288) (Kevin Partington)
* fca0679 Update: Improve perf not traversing default ignored dirs (fixes #5679) (#6276) (alberto)
* 320e8b0 Docs: Describe options in rules under Possible Errors part 4 (#6270) (Mark Pedrotti)
* 3e052c1 Docs: Mark no-useless-rename as fixable in rules index (#6297) (Dalton Santos)

v2.11.1 - May 30, 2016

* 64b0d0c Fix: failed to parse `/*eslint` comments by colon (fixes #6224) (#6258) (Toru Nagashima)
* c8936eb Build: Don't check commit count (fixes #5935) (#6263) (Nicholas C. Zakas)
* 113c1a8 Fix: `max-statements-per-line` false positive at exports (fixes #6264) (#6268) (Toru Nagashima)
* 03beb27 Fix: `no-useless-rename` false positives (fixes #6266) (#6267) (alberto)
* fe89037 Docs: Fix rule name in example (#6279) (Kenneth Williams)

v2.11.0 - May 27, 2016

* 77dd2b4 Fix: On --init, print message when package.json is invalid (fixes #6257) (#6261) (Kai Cataldo)
* 7f60186 Fix: `--ignore-pattern` can't uningnore files (fixes #6127) (#6253) (alberto)
* fea8fe6 New: no-useless-rename (fixes #6058) (#6249) (Kai Cataldo)
* b4cff9d Fix: Incorrect object-property-newline behavior (fixes #6207) (#6213) (Rafał Ruciński)
* 35b4656 Docs: Edit arrow-parens.md to show correct output value (#6245) (Adam Terlson)
* ee0cd58 Fix: `newline-before-return` shouldn't disallow newlines (fixes #6176) (#6217) (alberto)
* d4f5526 Fix: `vars-on-top` crashs at export declarations (fixes #6210) (#6220) (Toru Nagashima)
* 088bda9 New: `unicode-bom` rule to allow or disallow BOM (fixes #5502) (#6230) (Andrew Johnston)
* 14bfc03 Fix: `comma-dangle` wrong autofix (fixes #6233) (#6235) (Toru Nagashima)
* cdd65d7 Docs: added examples for arrow-body-style (refs #5498) (#6242) (Tieme van Veen)
* c10c07f Fix: lost code in autofixing (refs #6233) (#6234) (Toru Nagashima)
* e6d5b1f Docs: Add rule deprecation section to user guide (fixes #5845) (#6201) (Kai Cataldo)
* 777941e Upgrade: doctrine to 1.2.2 (fixes #6121) (#6231) (alberto)
* 74c458d Update: key-spacing rule whitespace fixer (fixes #6167) (#6169) (Ruurd Moelker)
* 04bd586 New: Disallow use of Object.prototype methods on objects (fixes #2693) (#6107) (Andrew Levine)
* 53754ec Update: max in `max-statements-per-line` should be >=0 (fixes #6171) (#6172) (alberto)
* 54d1201 Update: Add treatUndefinedAsUnspecified option (fixes #6026) (#6194) (Kenneth Williams)
* 18152dd Update: Add checkLoops option to no-constant-condition (fixes #5477) (#6202) (Kai Cataldo)
* 7644908 Fix: no-multiple-empty-lines BOF and EOF defaults (fixes #6179) (#6180) (Ruurd Moelker)
* 72335eb Fix: `max-statements-per-line` false positive (fixes #6173, fixes #6153) (#6192) (Toru Nagashima)
* 9fce04e Fix: `generator-star-spacing` false positive (fixes #6135) (#6168) (Toru Nagashima)

v2.10.2 - May 16, 2016

* bda5de5 Fix: Remove default parser from CLIEngine options (fixes #6182) (#6183) (alberto)
* e59e5a0 Docs: Describe options in rules under Possible Errors part 3 (#6105) (Mark Pedrotti)
* 842ab2e Build: Run phantomjs tests using karma (fixes #6128) (#6178) (alberto)

v2.10.1 - May 14, 2016

* 9397135 Fix: `valid-jsdoc` false positive at default parameters (fixes #6097) (#6170) (Toru Nagashima)
* 2166ad4 Fix: warning & error count in `CLIEngine.getErrorResults` (fixes #6155) (#6157) (alberto)
* 1e0a652 Fix: ignore empty statements in max-statements-per-line (fixes #6153) (#6156) (alberto)
* f9ca0d6 Fix: `no-extra-parens` to check for nulls (fixes #6161) (#6164) (Gyandeep Singh)
* d095ee3 Fix: Parser merge sequence in config (fixes #6158) (#6160) (Gyandeep Singh)
* f33e49f Fix: `no-return-assign` to check for null tokens (fixes #6159) (#6162) (Gyandeep Singh)

v2.10.0 - May 13, 2016

* 098cd9c Docs: Distinguish examples in rules under Stylistic Issues part 4 (#6136) (Kenneth Williams)
* 805742c Docs: Clarify JSX option usage (#6132) (Richard Collins)
* 10b0933 Fix: Optimize no-irregular-whitespace for the common case (fixes #6116) (#6117) (Andres Suarez)
* 36bec90 Docs: linkify URLs in development-environment.md (#6150) (chrisjshull)
* 29c401a Docs: Convert rules in index under Removed from list to table (#6091) (Mark Pedrotti)
* e13e696 Fix: `_` and `$` in isES5Constructor (fixes #6085) (#6094) (Kevin Locke)
* 67916b9 Fix: `no-loop-func` crashed (fixes #6130) (#6138) (Toru Nagashima)
* d311a62 Fix: Sort fixes consistently even if they overlap (fixes #6124) (#6133) (alberto)
* 6294459 Docs: Correct syntax for default ignores and `.eslintignore` example (#6118) (alberto)
* 067db14 Fix: Replace `assert.deepEqual` by `lodash.isEqual` (fixes #6111) (#6112) (alberto)
* 52fdf04 Fix: `no-multiple-empty-lines` duplicate errors at BOF (fixes #6113) (#6114) (alberto)
* e6f56da Docs: Document `--ignore-pattern` (#6120) (alberto)
* ef739cd Fix: Merge various command line configs at the same time (fixes #6104) (#6108) (Ed Lee)
* 767da6f Update: add returnAssign option to no-extra-parens (fixes #6036) (#6095) (Kai Cataldo)
* 06f6252 Build: Use split instead of slice/indexOf for commit check (fixes #6109) (#6110) (Ed Lee)
* c4fc39b Docs: Update headings of rules under Removed (refs #5774) (#6102) (Mark Pedrotti)
* 716345f Build: Match rule id at beginning of heading (refs #5774) (#6089) (Mark Pedrotti)
* 0734967 Update: Add an option to `prefer-const` (fixes #5692) (#6040) (Toru Nagashima)
* 7941d5e Update: Add autofix for `lines-around-comment` (fixes #5956) (#6062) (alberto)
* dc538aa Build: Pin proxyquire to ">=1.0.0 <1.7.5" (fixes #6096) (#6100) (alberto)
* 04563ca Docs: Describe options in rules under Possible Errors part 2 (#6063) (Mark Pedrotti)
* 5d390b2 Chore: Replace deprecated calls to context - batch 4 (fixes #6029) (#6087) (alberto)
* 6df4b23 Fix: `no-return-assign` warning nested expressions (fixes #5913) (#6041) (Toru Nagashima)
* 16fad58 Merge pull request #6088 from eslint/docs-one-var-per-line (alberto)
* 0b67170 Docs: Correct default for `one-var-declaration-per-line` (fixes #6017) (#6022) (Ed Lee)
* d40017f Fix: comma-style accounts for parens in array (fixes #6006) (#6038) (Kai Cataldo)
* 992d9cf Docs: Fix typography/teriminology in indent doc (fixes #6045) (#6044) (Rich Trott)
* 4ae39d2 Chore: Replace deprecated calls to context - batch 3 (refs #6029) (#6056) (alberto)
* 8633e4d Update: multipass should not exit prematurely (fixes #5995) (#6048) (alberto)
* 3c44c2c Update: Adds an avoidQuotes option for object-shorthand (fixes #3366) (#5870) (Chris Sauvé)
* a9a4652 Fix: throw when rule uses `fix` but `meta.fixable` not set (fixes #5970) (#6043) (Vitor Balocco)
* ad10106 Docs: Update comma-style docs (#6039) (Kai Cataldo)
* 388d6f8 Fix: `no-sequences` false negative at arrow expressions (fixes #6082) (#6083) (Toru Nagashima)
* 8e96064 Docs: Clarify rule example in README since we allow string error levels (#6061) (Kevin Partington)
* a66bf19 Fix: `lines-around-comment` multiple errors on same line (fixes #5965) (#5994) (alberto)
* a2cc54e Docs: Organize meta and describe visitor in Working with Rules (#5967) (Mark Pedrotti)
* ef8cbff Fix: object-shorthand should only lint computed methods (fixes #6015) (#6024) (Kai Cataldo)
* cd1b057 Chore: Replace deprecated calls to context - batch 2 (refs #6029) (#6049) (alberto)
* a3a6e06 Update: no-irregal-whitespace in a regular expression (fixes #5840) (#6018) (Linda_pp)
* 9b9d76c Chore: Replace deprecated calls to context - batch 1 (refs #6029) (#6034) (alberto)
* dd8bf93 Fix: blockless else in max-statements-per-line (fixes #5926) (#5993) (Glen Mailer)
* f84eb80 New: Add new rule `object-property-newline` (fixes #5667) (#5933) (Vitor Balocco)
* d5f4104 Docs: mention parsing errors in strict mode (fixes #5485) (#5991) (Mark Pedrotti)
* 249732e Docs: Move docs from eslint.github.io (fixes #5964) (#6012) (Nicholas C. Zakas)
* 4c2de6c Docs: Add example of diff clarity to comma-dangle rule docs (#6035) (Vitor Balocco)
* 3db2e89 Fix: Do not swallow exceptions in CLIEngine.getFormatter (fixes #5977) (#5978) (Gustav Nikolaj)
* eb2fb44 Fix: Always ignore defaults unless explicitly passed (fixes #5547) (#5820) (Ian VanSchooten)
* ab57e94 Docs: Add example of diff clarity to newline-per-chained-call (#5986) (Vitor Balocco)
* 88bc014 Docs: Update readme info about jshint (#6027) (alberto)
* a2c15cc Docs: put config example in code block (#6005) (Amos Wenger)
* a5011cb Docs: Fix a wrong examples' header of `prefer-arrow-callback`. (#6020) (Toru Nagashima)
* 1484ede Docs: Typo in nodejs-api (#6025) (alberto)
* ade6a9b Docs: typo: "eslint-disable-line" not "eslint disable-line" (#6019) (Will Day)
* 2f15354 Fix: Removed false positives of break and continue (fixes #5972) (#6000) (Onur Temizkan)

v2.9.0 - April 29, 2016

* a8a2cd8 Fix: Avoid autoconfig crashes from inline comments (fixes #5992) (#5999) (Ian VanSchooten)
* 23b00e0 Upgrade: npm-license to 0.3.2 (fixes #5996) (#5998) (alberto)
* 377167d Upgrade: ignore to 3.1.2 (fixes #5979) (#5988) (alberto)
* 141b778 Fix: no-control-regex literal handling fixed. (fixes #5737) (#5943) (Efe Gürkan YALAMAN)
* 577757d Fix: Clarify color option (fixes #5928) (#5974) (Grant Snodgrass)
* e7e6581 Docs: Update CLA link (#5980) (Gustav Nikolaj)
* 0be26bc Build: Add nodejs 6 to travis (fixes #5971) (#5973) (Gyandeep Singh)
* e606523 New: Rule `no-unsafe-finally` (fixes #5808) (#5932) (Onur Temizkan)
* 42d1ecc Chore: Add metadata to existing rules - Batch 7 (refs #5417) (#5969) (Vitor Balocco)
* e2ad1ec Update: object-shorthand lints computed methods (fixes #5871) (#5963) (Chris Sauvé)
* d24516a Chore: Add metadata to existing rules - Batch 6 (refs #5417) (#5966) (Vitor Balocco)
* 1e7a3ef Fix: `id-match` false positive in property values (fixes #5885) (#5960) (Mike Sherov)
* 51ddd4b Update: Use process @abstract when processing @return (fixes #5941) (#5945) (Simon Schick)
* 52a4bea Update: Add autofix for `no-whitespace-before-property` (fixes #5927) (#5951) (alberto)
* 46e058d Docs: Correct typo in configuring.md (#5957) (Nick S. Plekhanov)
* 5f8abab Chore: Add metadata to existing rules - Batch 5 (refs #5417) (#5944) (Vitor Balocco)
* 0562f77 Chore: Add missing newlines to test cases (fixes #5947) (Rich Trott)
* fc78e78 Chore: Enable quote-props rule in eslint-config-eslint (refs #5188) (#5938) (Gyandeep Singh)
* 43f6d05 Docs: Update docs to refer to column (#5937) (Sashko Stubailo)
* 586478e Update: Add autofix for `comma-dangle` (fixes #3805) (#5925) (alberto)
* a4f9c5a Docs: Distinguish examples in rules under Stylistic Issues part 3 (Kenneth Williams)
* e7c0737 Chore: Enable no-console rule in eslint-config-eslint (refs #5188) (Kevin Partington)
* 0023fe6 Build: Add “chore” to commit tags (fixes #5880) (#5929) (Mike Sherov)
* 25d626a Upgrade: espree 3.1.4 (fixes #5923, fixes #5756) (Kai Cataldo)
* a01b412 New: Add `no-useless-computed-key` rule (fixes #5402) (Burak Yigit Kaya)
* 9afb9cb Chore: Remove workaround for espree and escope bugs (fixes #5852) (alberto)
* 3ffc582 Chore: Update copyright and license info (alberto)
* 249eb40 Docs: Clarify init sets up local installation (fixes #5874) (Kai Cataldo)
* 6cd8c86 Docs: Describe options in rules under Possible Errors part 1 (Mark Pedrotti)
* f842d18 Fix: `no-this-before-super` crash on unreachable paths (fixes #5894) (Toru Nagashima)
* a02960b Docs: Fix missing delimiter in README links (Kevin Partington)
* 3a9e72c Docs: Update developer guide with new standards (Nicholas C. Zakas)
* cb78585 Update: Add `allowUnboundThis` to `prefer-arrow-callback` (fixes #4668) (Burak Yigit Kaya)
* 02be29f Chore: Remove CLA check from bot (Nicholas C. Zakas)
* 220713e Chore: Add metadata to existing rules - Batch 4 (refs #5417) (Vitor Balocco)
* df53414 Chore: Include jQuery Foundation info (Nicholas C. Zakas)
* f1b2992 Fix: `no-useless-escape` false positive in JSXAttribute (fixes #5882) (Toru Nagashima)
* 74674ad Docs: Move `sort-imports` to 'ECMAScript 6' (Kenneth Williams)
* ae69ddb Docs: Fix severity type in example (Kenneth Williams)
* 19f6fff Update: Autofixing does multiple passes (refs #5329) (Nicholas C. Zakas)
* 1e4b0ca Docs: Reduce length of paragraphs in rules index (Mark Pedrotti)
* 8cfe1eb Docs: Fix a wrong option (Zach Orlovsky)
* 8f6739f Docs: Add alberto as reviewer (alberto)
* 2ae4938 Docs: Fix message for `inline-config` option (alberto)
* 089900b Docs: Fix a wrong rule name in an example (Toru Nagashima)
* c032b41 Docs: Fix emphasis (Toru Nagashima)
* ae606f0 Docs: Update JSCS info in README (alberto)
* a9c5323 Fix: Install ESLint on init if not installed (fixes #5833) (Kai Cataldo)
* ed38358 Docs: Removed incorrect example (James M. Greene)
* af3113c Docs: Fix config comments in indent docs (Brandon Mills)
* 2b39461 Update: `commentPattern` option for `default-case` rule (fixes #5803) (Artyom Lvov)

v2.8.0 - April 15, 2016

* a8821a5 Docs: Distinguish examples in rules under Stylistic Issues part 2 (Kenneth Williams)
* 76913b6 Update: Add metadata to existing rules - Batch 3 (refs #5417) (Vitor Balocco)
* 34ad8d2 Fix: Check that module.paths exists (fixes #5791) (Nicholas C. Zakas)
* 37239b1 Docs: Add new members of the team (Ilya Volodin)
* fb3c2eb Update: allow template literals (fixes #5234) (Jonathan Haines)
* 5a4a935 Update: Add metadata to existing rules - Batch 2 (refs #5417) (Vitor Balocco)
* ea2e625 Fix: newline-before-return handles return as first token (fixes #5816) (Kevin Partington)
* f8db9c9 Update: add nestedBinaryExpressions to no-extra-parens (fixes #3065) (Ilya Volodin)
* 0045d57 Update: `allowNamedFunctions` in `prefer-arrow-callback` (fixes #5675) (alberto)
* 19da72a Update: Add metadata to existing rules - Batch 1 (refs #5417) (Vitor Balocco)
* cc14e43 Fix: `no-fallthrough` empty case with comment (fixes #5799) (alberto)
* 13c8b14 Fix: LogicalExpression checks for short circuit (fixes #5693) (Vamshi krishna)
* 73b225e Fix: Document and fix metadata (refs #5417) (Ilya Volodin)
* 882d199 Docs: Improve options description in `no-redeclare` (alberto)
* 6a71ceb Docs: Improve options description in `no-params-reassign` (alberto)
* 24b6215 Update: Include 'typeof' in rule 'no-constant-condition' (fixes #5228) (Vamshi krishna)
* a959063 Docs: Remove link to deprecated ESLintTester project (refs #3110) (Trey Thomas)
* 6fd7d82 Update: Change order in `eslint --init` env options (fixes #5742) (alberto)
* c59d909 Fix: Extra paren check around object arrow bodies (fixes #5789) (Brandon Mills)
* 6f88546 Docs: Use double quotes for better Win compatibility (fixes #5796) (alberto)
* 02743d5 Fix: catch self-assignment operators in `no-magic-number` (fixes #4400) (alberto)
* c94e74e Docs: Make rule descriptions more consistent (Kenneth Williams)
* 6028252 Docs: Distinguish examples in rules under Stylistic Issues part 1 (Mark Pedrotti)
* ccd8ca9 Fix: Added property onlyDeclaration to id-match rule (fixes #3488) (Gajus Kuizinas)
* 6703c02 Update: no-useless-escape / exact locations of errors (fixes #5751) (Onur Temizkan)
* 3d84b91 Fix: ignore trailing whitespace in template literal (fixes #5786) (Kai Cataldo)
* b0e6bc4 Update: add allowEmptyCatch option to no-empty (fixes #5800) (Kai Cataldo)
* f1f1dd7 Docs: Add @pedrottimark as a committer (Brandon Mills)
* 228f201 Update: `commentPattern` option for `no-fallthrough` rule (fixes #5757) (Artyom Lvov)
* 41db670 Docs: Clarify disable inline comments (Kai Cataldo)
* 9c9a295 Docs: Add note about shell vs node glob parameters in cli (alberto)
* 5308ff9 Docs: Add code backticks to sentence in fixable rules (Mark Pedrotti)
* 965ec06 Docs: fix the examples for space-before-function-paren. (Craig Silverstein)
* 2b202fc Update: Add ignore option to space-before-function-parens (fixes #4127) (Craig Silverstein)
* 24c12ba Fix: improve `constructor-super` errors for literals (fixes #5449) (Toru Nagashima)

v2.7.0 - April 4, 2016

* 134cb1f Revert "Update: adds nestedBinaryExpressions for no-extra-parens rule (fixes #3065)" (Ilya Volodin)
* 7e80867 Docs: Update sentence in fixable rules (Mark Pedrotti)
* 1b6d5a3 Update: adds nestedBinaryExpressions for no-extra-parens (fixes #3065) (Nick Fisher)
* 4f93c32 Docs: Clarify `array-bracket-spacing` with newlines (fixes #5768) (alberto)
* 161ddac Fix: remove `console.dir` (fixes #5770) (Toru Nagashima)
* 0c33f6a Fix: indent rule uses wrong node for class indent level (fixes #5764) (Paul O’Shannessy)

v2.6.0 - April 1, 2016

* ce2accd Fix: vars-on-top now accepts exported variables (fixes #5711) (Olmo Kramer)
* 7aacba7 Update: Deprecate option `maximum` in favor of `max` (fixes #5685) (Vitor Balocco)
* 5fe6fca Fix: no-useless-escape \B regex escape (fixes #5750) (Onur Temizkan)
* 9b73ffd Update: `destructuring` option of `prefer-const` rule (fixes #5594) (Toru Nagashima)
* 8ac9206 Docs: Typo in `sort-imports` (alberto)
* 12902c5 Fix: valid-jsdoc crash w/ Field & Array Type (fixes #5745) (fixes #5746) (Burak Yigit Kaya)
* 2c8b65a Docs: Edit examples for a few rules (Mark Pedrotti)
* d736bc2 Fix: Treat SwitchCase like a block in lines-around-comment (fixes #5718) (Scott O'Hara)
* 24a61a4 Update: make `no-useless-escape` allowing line breaks (fixes #5689) (Toru Nagashima)
* 4ecd45e Fix: Ensure proper lookup of config files (fixes #5175, fixes #5468) (Nicholas C. Zakas)
* 088e26b Fix: Update doctrine to allow hyphens in JSDoc names (fixes #5612) (Kai Cataldo)
* 692fd5d Upgrade: Old Chalk.JS deprecated method (fixes #5716) (Morris Singer)
* f59d91d Update: no-param-reassign error msgs (fixes #5705) (Isaac Levy)
* c1b16cd Fix: Object spread throws error in key-spacing rule. (fixes #5724) (Ziad El Khoury Hanna)
* 3091613 Docs: Correct explanation about properties (James Monger)
* cb0f0be Fix: Lint issue with `valid-jsdoc` rule (refs #5188) (Gyandeep Singh)
* aba1954 Build: Ignore jsdoc folder internally (fixes #5714) (alberto)
* a35f127 Fix: Lint for eslint project in regards to vars (refs #5188) (Gyandeep Singh)
* d9ab4f0 Fix: Windows scoped package configs (fixes #5644) (Nicholas C. Zakas)
* 8d0cd0d Update: Basic valid-jsdoc default parameter support (fixes #5658) (Tom Andrews)

v2.5.3 - March 28, 2016

* 8749ac5 Build: Disable bundling dependencies (fixes #5687) (Nicholas C. Zakas)

v2.5.2 - March 28, 2016

* 1cc7f8e Docs: Remove mention of minimatch for .eslintignore (Ian VanSchooten)
* 5bd69a9 Docs: Reorder FAQ in README (alberto)
* 98e6bd9 Fix: Correct default for indentation in `eslint --init` (fixes #5698) (alberto)
* 679095e Fix: make the default of `options.cwd` in runtime (fixes #5694) (Toru Nagashima)
* 4f06f2f Docs: Distinguish examples in rules under Best Practices part 2 (Mark Pedrotti)
* 013a18e Build: Fix bundling script (fixes #5680) (Nicholas C. Zakas)
* 8c5d954 Docs: Typo fix (István Donkó)
* 09659d6 Docs: Use string severity (Kenneth Williams)
* a4ae769 Docs: Manual changelog update for v2.5.1 (Nicholas C. Zakas)
* c41fab9 Fix: don't use path.extname with undefined value (fixes #5678) (Myles Borins)

v2.5.1 - March 25, 2016

* Build: No functional changes, just republished with a working package.

v2.5.0 - March 25, 2016

* 7021aa9 Fix: lines-around-comment in ESLint repo, part 2 (refs #5188) (Kevin Partington)
* 095c435 Docs: Remove ES2016 from experimental section of README (Kevin Partington)
* 646f863 Build: Bundle dependencies in package.json (fixes #5013) (Nicholas C. Zakas)
* ea06868 Docs: Clarify --ext does not apply to globs (fixes #5452) (Ian VanSchooten)
* 569c478 Build: Fix phantomjs CI problems (fixes #5666) (alberto)
* 6022426 Docs: Add link to chat room in README primary links (alberto)
* 2fbb530 Docs: Add link to "Proposing a Rule Change" in README (alberto)
* 25bf491 Upgrade: globals 9.x (fixes #5668) (Toru Nagashima)
* d6f8409 New: Rule - No useless escape (fixes #5460) (Onur Temizkan)
* 12a43f1 Docs: remove brace expansion from configuring.md (refs #5314) (Jonathan Haines)
* 92d1749 New: max-statements-per-line (fixes #5424) (Kenneth Williams)
* aaf324a Fix: missing support for json sub configs (fixes #5413) (Noam Okman)
* 48ad5fe Update: Add 'caughtErrors' to rule no-unused-vars (fixes #3837) (vamshi)
* ad90c2b Fix: incorrect config message (fixes #5653) (s0ph1e)
* a551831 Docs: Distinguish examples in rules under Node.js and CommonJS (Mark Pedrotti)
* 83cd651 Upgrade: chai to 3.5.0 (fixes #5647) (alberto)
* 32748dc Fix: `radix` rule false positive at shadowed variables (fixes #5639) (Toru Nagashima)
* 66db38d Fix: `--no-ignore` should not un-ignore default ignores (fixes #5547) (alberto)
* e3e06f3 Docs: Distinguish examples in rules under Best Practices part 4 (Mark Pedrotti)
* a9f0865 Docs: Update no-sequences rule docs for clarity (fixes #5536) (Kai Cataldo)
* bae7b30 Docs: Add michaelficarra as committer (alberto)
* e2990e7 Docs: Consistent wording in rules README (alberto)
* 49b4d2a Docs: Update team list with new members (Ilya Volodin)
* d0ae66c Update: Allow autoconfiguration for JSX code (fixes #5511) (Ian VanSchooten)
* 38a0a64 Docs: Clarify `linebreak-style` docs (fixes #5628) (alberto)
* 4b7305e Fix: Allow default ignored files to be unignored (fixes #5410) (Ian VanSchooten)
* 4b05ce6 Update: Enforce repo coding conventions via ESLint (refs #5188) (Kevin Partington)
* 051b255 Docs: Remove or rewrite references to former ecmaFeatures (Mark Pedrotti)
* 9a22625 Fix: `prefer-const` false positive at non-blocked if (fixes #5610) (Toru Nagashima)
* b1fd482 Fix: leading comments added from previous node (fixes #5531) (Kai Cataldo)
* c335650 Docs: correct the no-confusing-arrow docs (Daniel Norman)
* e94b77d Fix: Respect 'ignoreTrailingComments' in max-len rule (fixes #5563) (Vamshi Krishna)
* 9289ef8 Fix: handle personal package.json without config (fixes #5496) (Denny Christochowitz)
* 87d74b2 Fix: `prefer-const` got to not change scopes (refs #5284) (Toru Nagashima)
* 5a881e7 Docs: Fix typo in code snippet for no-unmodified-loop-condition rule (Chris Rebert)
* 03037c2 Update: Overrides for space-unary-ops (fixes #5060) (Afnan Fahim)
* 24d986a Update: replace MD5 hashing of cache files with MurmurHash (fixes #5522) (Michael Ficarra)
* f405030 Fix: Ensure allowing `await` as a property name (fixes #5564) (Toru Nagashima)
* aefc90c Fix: `no-useless-constructor` clash (fixes #5573) (Toru Nagashima)
* 9eaa20d Docs: Fix typo in CLI help message (ryym)
* a7c3e67 Docs: Invalid json in `configuring.md` (alberto)
* 4e50332 Docs: Make `prefer-template` examples consistent. (alberto)
* cfc14a9 Fix: valid-jsdoc correctly checks type union (fixes #5260) (Kai Cataldo)
* 689cb7d Fix: `quote-props` false positive on certain keys (fixes #5532) (Burak Yigit Kaya)
* 167a03a Fix: `brace-style` erroneously ignoring certain errors (fixes #5197) (Burak Yigit Kaya)
* 3133f28 Fix: object-curly-spacing doesn't know types (fixes #5537) (fixes #5538) (Burak Yigit Kaya)
* d0ca171 Docs: Separate parser and config questions in issue template (Kevin Partington)
* bc769ca Fix: Improve file path resolution (fixes #5314) (Ian VanSchooten)
* 9ca8567 Docs: Distinguish examples in rules under Best Practices part 3 (Mark Pedrotti)
* b9c69f1 Docs: Distinguish examples in rules under Variables part 2 (Mark Pedrotti)
* c289414 New: `no-duplicate-imports` rule (fixes #3478) (Simen Bekkhus)

v2.4.0 - March 11, 2016

* 97b2466 Fix: estraverse/escope to work with unknowns (fixes #5476) (Nicholas C. Zakas)
* 641b3f7 Fix: validate the type of severity level (fixes #5499) (Shinnosuke Watanabe)
* 9ee8869 Docs: no-unused-expressions - add more edge unusable and usable examples (Brett Zamir)
* 56bf864 Docs: Create parity between no-sequences examples (Brett Zamir)
* 13ef1c7 New: add `--parser-options` to CLI (fixes #5495) (Jordan Harband)
* ae1ee54 Docs: fix func-style arrow exception option (Craig Martin)
* 91852fd Docs: no-lone-blocks - show non-problematic (and problematic) label (Brett Zamir)
* b34458f Docs: Rearrange rules for better categories (and improve rule summaries) (Brett Zamir)
* 1198b26 Docs: Minor README clarifications (Brett Zamir)
* 03e6869 Fix: newline-before-return: bug with comment (fixes #5480) (mustafa)
* ad100fd Fix: overindent in VariableDeclarator parens or brackets (fixes #5492) (David Greenspan)
* 9b8e04b Docs: Replace all node references to Node.js which is the official name (Brett Zamir)
* cc1f2f0 Docs: Minor fixes in no-new-func (Brett Zamir)
* 6ab81d4 Docs: Distinguish examples in rules under Best Practices part 1 (Mark Pedrotti)
* 9c6c70c Update: add `allowParens` option to `no-confusing-arrow` (fixes #5332) (Burak Yigit Kaya)
* 979c096 Docs: Document linebreak-style as fixable. (Afnan Fahim)
* 9f18a81 Fix: Ignore destructuring assignment in `object-shorthand` (fixes #5488) (alberto)
* 5d9a798 Docs: README.md, prefer-const; change modified to reassigned (Michiel de Bruijne)
* 38eb7f1 Fix: key-spacing checks ObjectExpression is multiline (fixes #5479) (Kevin Partington)
* 9592c45 Fix: `no-unmodified-loop-condition` false positive (fixes #5445) (Toru Nagashima)

v2.3.0 - March 4, 2016

* 1b2c6e0 Update: Proposed no-magic-numbers option: ignoreJSXNumbers (fixes #5348) (Brandon Beeks)
* 63c0b7d Docs: Fix incorrect environment ref. in Rules in Plugins. (fixes #5421) (Jesse McCarthy)
* 124c447 Build: Add additional linebreak to docs (fixes #5464) (Ilya Volodin)
* 0d3831b Docs: Add RuleTester parserOptions migration steps (Kevin Partington)
* 50f4d5a Fix: extends chain (fixes #5411) (Toru Nagashima)
* 0547072 Update: Replace getLast() with lodash.last() (fixes #5456) (Jordan Eldredge)
* 8c29946 Docs: Distinguish examples in rules under Possible Errors part 1 (Mark Pedrotti)
* 5319b4a Docs: Distinguish examples in rules under Possible Errors part 2 (Mark Pedrotti)
* 1da2420 Fix: crash when SourceCode object was reused (fixes #5007) (Toru Nagashima)
* 9e9daab New: newline-before-return rule (fixes #5009) (Kai Cataldo)
* e1bbe45 Fix: Check space after anonymous generator star (fixes #5435) (alberto)
* 119e0ed Docs: Distinguish examples in rules under Variables (Mark Pedrotti)
* 905c049 Fix: `no-undef` false positive at new.target (fixes #5420) (Toru Nagashima)
* 4a67b9a Update: Add ES7 support (fixes #5401) (Brandon Mills)
* 89c757d Docs: Replace ecmaFeatures with parserOptions in working-with-rules (Kevin Partington)
* 804c08e Docs: Add parserOptions to RuleTester section of working-with-rules (Kevin Partington)
* 1982c50 Docs: Document string option for `no-unused-vars`. (alberto)
* 4f82b2b Update: Support classes in `padded-blocks` (fixes #5092) (alberto)
* ed5564f Docs: Specify results of `no-unused-var` with `args` (fixes #5334) (chinesedfan)
* de0a4ef Fix: `getFormatter` throws an error when called as static (fixes #5378) (cowchimp)
* 78f7ca9 Fix: Prevent crash from swallowing console.log (fixes #5381) (Ian VanSchooten)
* 34b648d Fix: remove tests which have invalid syntax (fixes #5405) (Toru Nagashima)
* 7de5ae4 Docs: Missing allow option in  docs (Scott O'Hara)
* cf14c71 Fix: `no-useless-constructor` rule crashes sometimes (fixes #5290) (Burak Yigit Kaya)
* 70e3a02 Update: Allow string severity in config (fixes #3626) (Nicholas C. Zakas)
* 13c7c19 Update: Exclude ES5 constructors from consistent-return (fixes #5379) (Kevin Locke)
* 784d3bf Fix: Location info in `dot-notation` rule (fixes #5397) (Gyandeep Singh)
* 6280b2d Update: Support switch statements in padded-blocks (fixes #5056) (alberto)
* 25a5b2c Fix: Allow irregular whitespace in comments (fixes #5368) (Christophe Porteneuve)
* 560c0d9 New: no-restricted-globals rule implementation (fixes #3966) (Benoît Zugmeyer)
* c5bb478 Fix: `constructor-super` false positive after a loop (fixes #5394) (Toru Nagashima)
* 6c0c4aa Docs: Add Issue template (fixes #5313) (Kai Cataldo)
* 1170e67 Fix: indent rule doesn't handle constructor instantiation (fixes #5384) (Nate Cavanaugh)
* 6bc9932 Fix: Avoid magic numbers in rule options (fixes #4182) (Brandon Beeks)
* 694e1c1 Fix: Add tests to cover default magic number tests (fixes #5385) (Brandon Beeks)
* 0b5349d Fix: .eslintignore paths should be absolute (fixes #5362) (alberto)
* 8f6c2e7 Update: Better error message for plugins (refs #5221) (Nicholas C. Zakas)
* 972d41b Update: Improve error message for rule-tester (fixes #5369) (Jeroen Engels)
* fe3f6bd Fix: `no-self-assign` false positive at shorthand (fixes #5371) (Toru Nagashima)
* 2376291 Docs: Missing space in `no-fallthrough` doc. (alberto)
* 5aedb87 Docs: Add mysticatea as reviewer (Nicholas C. Zakas)
* 1f9fd10 Update: no-invalid-regexp allows custom flags (fixes #5249) (Afnan Fahim)
* f1eab9b Fix: Support for dash and slash in `valid-jsdoc` (fixes #1598) (Gyandeep Singh)
* cd12a4b Fix:`newline-per-chained-call` should only warn on methods (fixes #5289) (Burak Yigit Kaya)
* 0d1377d Docs: Add missing `symbol` type into valid list (Plusb Preco)
* 6aa2380 Update: prefer-const; change modified to reassigned (fixes #5350) (Michiel de Bruijne)
* d1d62c6 Fix: indent check for else keyword with Stroustrup style (fixes #5218) (Gyandeep Singh)
* 7932f78 Build: Fix commit message validation (fixes #5340) (Nicholas C. Zakas)
* 1c347f5 Fix: Cleanup temp files from tests (fixes #5338) (Nick)
* 2f3e1ae Build: Change rules to warnings in perf test (fixes #5330) (Brandon Mills)
* 36f40c2 Docs: Achieve consistent order of h2 in rule pages (Mark Pedrotti)

v2.2.0 - February 19, 2016

* 45a22b5 Docs: remove esprima-fb from suggested parsers (Henry Zhu)
* a4d9cd3 Docs: Fix semi rule typo (Brandon Mills)
* 9d005c0 Docs: Correct option name in `no-implicit-coercion` rule (Neil Kistner)
* 2977248 Fix: Do not cache `.eslintrc.js` (fixes #5067) (Nick)
* 211eb8f Fix: no-multi-spaces conflicts with smart tabs (fixes #2077) (Afnan Fahim)
* 6dc9483 Fix: Crash in `constructor-super` (fixes #5319) (Burak Yigit Kaya)
* 3f48875 Docs: Fix yield star spacing examples (Dmitriy Lazarev)
* 4dab76e Docs: Update `preferType` heading to keep code format (fixes #5307) (chinesedfan)
* 7020b82 Fix: `sort-imports` warned between default and members (fixes #5305) (Toru Nagashima)
* 2f4cd1c Fix: `constructor-super` and `no-this-before-super` false (fixes #5261) (Toru Nagashima)
* 59e9c5b New: eslint-disable-next-line (fixes #5206) (Kai Cataldo)
* afb6708 Fix: `indent` rule forgot about some CallExpressions (fixes #5295) (Burak Yigit Kaya)
* d18d406 Docs: Update PR creation bot message (fixes #5268) (Nicholas C. Zakas)
* 0b1cd19 Fix: Ignore parser option if set to default parser (fixes #5241) (Kai Cataldo)

v2.1.0 - February 15, 2016

* 7981ef5 Build: Fix release script (Nicholas C. Zakas)
* c9c34ea Fix: Skip computed members in `newline-per-chained-call` (fixes #5245) (Burak Yigit Kaya)
* b32ddad Build: `npm run perf` command should check the exit code (fixes #5279) (Burak Yigit Kaya)
* 6580d1c Docs: Fix incorrect `api.verify` JSDoc for `config` param (refs #5104) (Burak Yigit Kaya)
* 1f47868 Docs: Update yield-star-spacing documentation for 2.0.0 (fixes #5272) (Burak Yigit Kaya)
* 29da8aa Fix: `newline-after-var` crash on a switch statement (fixes #5277) (Toru Nagashima)
* 86c5a20 Fix: `func-style` should ignore ExportDefaultDeclarations (fixes #5183) (Burak Yigit Kaya)
* ba287aa Fix: Consolidate try/catches to top levels (fixes #5243) (Ian VanSchooten)
* 3ef5da1 Docs: Update no-magic-numbers#ignorearrayindexes. (KazuakiM)
* 0d6850e Update: Allow var declaration at end of block (fixes #5246) (alberto)
* c1e3a73 Fix: Popular style init handles missing package.json keys (refs #5243) (Brandon Mills)
* 68c6e22 Docs: fix default value of `keyword-spacing`'s overrides option. (Toru Nagashima)
* 00fe46f Upgrade: inquirer (fixes #5265) (Bogdan Chadkin)
* ef729d7 Docs: Remove option that is not being used in max-len rule (Thanos Lefteris)
* 4a5ddd5 Docs: Fix rule config above examples for require-jsdoc (Thanos Lefteris)
* c5cbc1b Docs: Add rule config above each example in jsx-quotes (Thanos Lefteris)
* f0aceba Docs: Correct alphabetical ordering in rule list (Randy Coulman)
* 1651ffa Docs: update migrating to 2.0.0 (fixes #5232) (Toru Nagashima)
* 9078537 Fix: `indent` on variable declaration with separate array (fixes #5237) (Burak Yigit Kaya)
* f8868b2 Docs: Typo fix in consistent-this rule doc fixes #5240 (Nicolas Froidure)
* 44f6915 Fix: ESLint Bot mentions the wrong person for extra info (fixes #5229) (Burak Yigit Kaya)
* c612a8e Fix: `no-empty-function` crash (fixes #5227) (Toru Nagashima)
* ae663b6 Docs: Add links for issue documentation (Nicholas C. Zakas)
* 717bede Build: Switch to using eslint-release (fixes #5223) (Nicholas C. Zakas)
* 980e139 Fix: Combine all answers for processAnswers (fixes #5220) (Ian VanSchooten)
* 1f2a1d5 Docs: Remove inline errors from doc examples (fixes #4104) (Burak Yigit Kaya)

v2.0.0 - February 12, 2016

* cc3a66b Docs: Issue message when more info is needed (Nicholas C. Zakas)
* 2bc40fa Docs: Simplify hierarchy of headings in rule pages (Mark Pedrotti)
* 1666254 Docs: Add note about only-whitespace rule for `--fix` (fixes #4774) (Burak Yigit Kaya)
* 2fa09d2 Docs: Add `quotes` to related section of `prefer-template` (fixes #5192) (Burak Yigit Kaya)
* 7b12995 Fix: `key-spacing` not enforcing no-space in minimum mode (fixes #5008) (Burak Yigit Kaya)
* c1c4f4d Breaking: new `no-empty-function` rule (fixes #5161) (Toru Nagashima)

v2.0.0-rc.1 - February 9, 2016

* 4dad82a Update: Adding shared environment for node and browser (refs #5196) (Eli White)
* b46c893 Fix: Config file relative paths (fixes #5164, fixes #5160) (Nicholas C. Zakas)
* aa5b2ac Fix: no-whitespace-before-property fixes (fixes #5167) (Kai Cataldo)
* 4e99924 Update: Replace several dependencies with lodash (fixes #5012) (Gajus Kuizinas)
* 718dc68 Docs: Remove periods in rules' README for consistency. (alberto)
* 7a47085 Docs: Correct `arrow-spacing` overview. (alberto)
* a4cde1b Docs: Clarify global-require inside try/catch (fixes #3834) (Brandon Mills)
* fd07925 Docs: Clarify docs for api.verify (fixes #5101, fixes #5104) (Burak Yigit Kaya)
* 413247f New: Add a --print-config flag (fixes #5099) (Christopher Crouzet)
* efeef42 Update: Implement auto fix for space-in-parens (fixes #5050) (alberto)
* e07fdd4 Fix: code path analysis and labels (fixes #5171) (Toru Nagashima)
* 2417bb2 Fix: `no-unmodified-loop-condition` false positive (fixes #5166) (Toru Nagashima)
* fae1884 Fix: Allow same-line comments in padded-blocks (fixes #5055) (Brandon Mills)
* a24d8ad Fix: Improve autoconfig logging (fixes #5119) (Ian VanSchooten)
* e525923 Docs: Correct obvious inconsistencies in rules h2 elements (Mark Pedrotti)
* 9675b5e Docs: `avoid-escape` does not allow backticks (fixes #5147) (alberto)
* a03919a Fix: `no-unexpected-multiline` false positive (fixes #5148) (Feross Aboukhadijeh)
* 74360d6 Docs: Note no-empty applies to empty block statements (fixes #5105) (alberto)
* 6eeaa3f Build: Remove pending tests (fixes #5126) (Ian VanSchooten)
* 02c83df Docs: Update docs/rules/no-plusplus.md (Sheldon Griffin)
* 0c4de5c New: Added "table" formatter (fixes #4037) (Gajus Kuizinas)
* 0a59926 Update: 'implied strict mode' ecmaFeature (fixes #4832) (Nick Evans)
* 53a6eb3 Fix: Handle singular case in rule-tester error message (fixes #5141) (Bryan Smith)
* 97ac91c Build: Increment eslint-config-eslint (Nicholas C. Zakas)

v2.0.0-rc.0 - February 2, 2016

* 973c499 Fix: `sort-imports` crash (fixes #5130) (Toru Nagashima)
* e64b2c2 Breaking: remove `no-empty-label` (fixes #5042) (Toru Nagashima)
* 79ebbc9 Breaking: update `eslint:recommended` (fixes #5103) (Toru Nagashima)
* e1d7368 New: `no-extra-label` rule (fixes #5059) (Toru Nagashima)
* c83b48c Fix: find ignore file only in cwd (fixes #5087) (Nicholas C. Zakas)
* 3a24240 Docs: Fix jsdoc param names to match function param names (Thanos Lefteris)
* 1d79746 Docs: Replace ecmaFeatures setting with link to config page (Thanos Lefteris)
* e96ffd2 New: `template-curly-spacing` rule (fixes #5049) (Toru Nagashima)
* 4b02902 Update: Extended no-console rule (fixes #5095) (EricHenry)
* 757651e Docs: Remove reference to rules enabled by default (fixes #5100) (Brandon Mills)
* 0d87f5d Docs: Clarify eslint-disable comments only affect rules (fixes #5005) (Brandon Mills)
* 1e791a2 New: `no-self-assign` rule (fixes #4729) (Toru Nagashima)
* c706eb9 Fix: reduced `no-loop-func` false positive (fixes #5044) (Toru Nagashima)
* 3275e86 Update: Add extra aliases to consistent-this rule (fixes #4492) (Zachary Alexander Belford)
* a227360 Docs: Replace joyent org with nodejs (Thanos Lefteris)
* b2aedfe New: Rule to enforce newline after each call in the chain (fixes #4538) (Rajendra Patil)
* d67bfdd New: `no-unused-labels` rule (fixes #5052) (Toru Nagashima)

v2.0.0-beta.3 - January 29, 2016

* 86a3e3d Update: Remove blank lines at beginning of files (fixes #5045) (Jared Sohn)
* 4fea752 New: Autoconfiguration from source inspection (fixes #3567) (Ian VanSchooten)
* 519f39f Breaking: Remove deprecated rules (fixes #5032) (Gyandeep Singh)
* c75ee4a New: Add support for configs in plugins (fixes #3659) (Ilya Volodin)
* 361377f Fix: `prefer-const` false positive reading before writing (fixes #5074) (Toru Nagashima)
* ff2551d Build: Improve `npm run perf` command (fixes #5028) (Toru Nagashima)
* bcca69b Update: add int32Hint option to `no-bitwise` rule (fixes #4873) (Maga D. Zandaqo)
* e3f2683 Update: config extends dependency lookup (fixes #5023) (Nicholas C. Zakas)
* a327a06 Fix: Indent rule for allman brace style scenario (fixes #5064) (Gyandeep Singh)
* afdff6d Fix: `no-extra-bind` false positive (fixes #5058) (Toru Nagashima)
* c1fad4f Update: add autofix support for spaced-comment (fixes #4969, fixes #5030) (Maga D. Zandaqo)
* 889b942 Revert "Docs: Update readme for legend describing rules icons (refs #4355)" (Nicholas C. Zakas)
* b0f21a0 Fix: `keyword-spacing` false positive in template strings (fixes #5043) (Toru Nagashima)
* 53fa5d1 Fix: `prefer-const` false positive in a loop condition (fixes #5024) (Toru Nagashima)
* 385d399 Docs: Update readme for legend describing rules icons (Kai Cataldo)
* 505f1a6 Update: Allow parser to be relative to config (fixes #4985) (Nicholas C. Zakas)
* 79e8a0b New: `one-var-declaration-per-line` rule (fixes #1622) (alberto)
* 654e6e1 Update: Check extra Boolean calls in no-extra-boolean-cast (fixes #3650) (Andrew Sutton)

v2.0.0-beta.2 - January 22, 2016

* 3fa834f Docs: Fix formatter links (fixes #5006) (Gyandeep Singh)
* 54b1bc8 Docs: Fix link in strict.md (fixes #5026) (Nick Evans)
* e0c5cf7 Upgrade: Espree to 3.0.0 (fixes #5018) (Ilya Volodin)
* 69f149d Docs: language tweaks (Andres Kalle)
* 2b33c74 Update: valid-jsdoc to not require @return in constructors (fixes #4976) (Maga D. Zandaqo)
* 6ac2e01 Docs: Fix description of exported comment (Mickael Jeanroy)
* 29392f8 New: allow-multiline option on comma-dangle (fixes #4967) (Alberto Gimeno)
* 05b8cb3 Update: Module overrides all 'strict' rule options (fixes #4936) (Nick Evans)
* 8470474 New: Add metadata to few test rules (fixes #4494) (Ilya Volodin)
* ba11c1b Docs: Add Algolia as sponsor to README (Nicholas C. Zakas)
* b28a19d Breaking: Plugins envs and config removal (fixes #4782, fixes #4952) (Nicholas C. Zakas)
* a456077 Docs: newline-after-var doesn't allow invalid options. (alberto)
* 3e6a24e Breaking: Change `strict` default mode to "safe" (fixes #4961) (alberto)
* 5b96265 Breaking: Update eslint:recommended (fixes #4953) (alberto)
* 7457a4e Upgrade: glob to 6.x (fixes #4991) (Gyandeep Singh)
* d3f4bdd Build: Cleanup for code coverage (fixes #4983) (Gyandeep Singh)
* b8fbaa0 Fix: multiple message in TAP formatter (fixes #4975) (Simon Degraeve)
* 990f8da Fix: `getNodeByRangeIndex` performance issue (fixes #4989) (Toru Nagashima)
* 8ac1dac Build: Update markdownlint dependency to 0.1.0 (fixes #4988) (David Anson)
* 5cd5429 Fix: function expression doc in call expression (fixes #4964) (Tim Schaub)
* 4173baa Fix: `no-dupe-class-members` false positive (fixes #4981) (Toru Nagashima)
* 12fe803 Breaking: Supports Unicode BOM (fixes #4878) (Toru Nagashima)
* 1fc80e9 Build: Increment eslint-config-eslint (Nicholas C. Zakas)
* e0a9024 Update: Report newline between template tag and literal (fixes #4210) (Rajendra Patil)
* da3336c Update: Rules should get `sourceType` from Program node (fixes #4960) (Nick Evans)
* a2ac359 Update: Make jsx-quotes fixable (refs #4377) (Gabriele Petronella)
* ee1014d Fix: Incorrect error location for object-curly-spacing (fixes #4957) (alberto)
* b52ed17 Fix: Incorrect error location for space-in-parens (fixes #4956) (alberto)
* 9c1bafb Fix: Columns of parse errors are off by 1 (fixes #4896) (alberto)
* 5e4841e New: 'id-blacklist' rule (fixes #3358) (Keith Cirkel)
* 700b8bc Update: Add "allow" option to allow specific operators (fixes #3308) (Rajendra Patil)
* d82eeb1 Update: Add describe around rule tester blocks (fixes #4907) (Ilya Volodin)
* 2967402 Update: Add minimum value to integer values in schema (fixes #4941) (Ilya Volodin)
* 7b632f8 Upgrade: Globals to ^8.18.0 (fixes #4728) (Gyandeep Singh)
* 86e6e57 Fix: Incorrect error at EOF for no-multiple-empty-lines (fixes #4917) (alberto)
* 7f058f3 Fix: Incorrect location for padded-blocks (fixes #4913) (alberto)
* b3de8f7 Fix: Do not show ignore messages for default ignored files (fixes #4931) (Gyandeep Singh)
* b1360da Update: Support multiLine and singleLine options (fixes #4697) (Rajendra Patil)
* 82fbe09 Docs: Small semantic issue in documentation example (fixes #4937) (Marcelo Zarate)
* 13a4e30 Docs: Formatting inconsistencies (fixes #4912) (alberto)
* d487013 Update: Option to allow extra parens for cond assign (fixes #3317) (alberto)
* 0f469b4 Fix: JSDoc for function expression on object property (fixes #4900) (Tim Schaub)
* c2dee27 Update: Add module tests to no-extra-semi (fixes #4915) (Nicholas C. Zakas)
* 5a633bf Update: Add `preferType` option to `valid-jsdoc` rule (fixes #3056) (Gyandeep Singh)
* ebd01b7 Build: Fix version number on release (fixes #4921) (Nicholas C. Zakas)
* 2d626a3 Docs: Fix typo in changelog (Nicholas C. Zakas)
* c4c4139 Fix: global-require no longer warns if require is shadowed (fixes #4812) (Kevin Partington)
* bbf7f27 New: provide config.parser via `parserName` on RuleContext (fixes #3670) (Ben Mosher)

v2.0.0-beta.1 - January 11, 2016

* 6c70d84 Build: Fix prerelease script (fixes #4919) (Nicholas C. Zakas)
* d5c9435 New: 'sort-imports' rule (refs #3143) (Christian Schuller)
* a8cfd56 Fix: remove duplicate of eslint-config-eslint (fixes #4909) (Toru Nagashima)
* 19a9fbb Breaking: `space-before-blocks` ignores after keywords (fixes #1338) (Toru Nagashima)
* c275b41 Fix: no-extra-parens ExpressionStatement restricted prods (fixes #4902) (Michael Ficarra)
* b795850 Breaking: don't load ~/.eslintrc when using --config flag (fixes #4881) (alberto)
* 3906481 Build: Add AppVeyor CI (fixes #4894) (Gyandeep Singh)
* 6390862 Docs: Fix missing footnote (Yoshiya Hinosawa)
* e5e06f8 Fix: Jsdoc comment for multi-line function expressions (fixes #4889) (Gyandeep Singh)
* 7c9be60 Fix: Fix path errors in windows (fixes #4888) (Gyandeep Singh)
* a1840e7 Fix: gray text was invisible on Solarized Dark theme (fixes #4886) (Jack Leigh)
* fc9f528 Docs: Modify unnecessary flag docs in quote-props (Matija Marohnić)
* 186e8f0 Update: Ignore camelcase in object destructuring (fixes #3185) (alberto)
* 7c97201 Upgrade: doctrine version to 1.1.0 (fixes #4854) (Tim Schaub)
* ceaf324 New: Add no-new-symbol rule (fixes #4862) (alberto)
* e2f2b66 Breaking: Remove defaults from `eslint:recommended` (fixes #4809) (Ian VanSchooten)
* 0b3c01e Docs: Specify default for func-style (fixes #4834) (Ian VanSchooten)
* 008ea39 Docs: Document default for operator assignment (fixes #4835) (alberto)
* b566f56 Docs: no-new-func typo (alberto)
* 1569695 Update: Adds default 'that' for consistent-this (fixes #4833) (alberto)
* f7b28b7 Docs: clarify `requireReturn` option for valid-jsdoc rule (fixes #4859) (Tim Schaub)
* 407f329 Build: Fix prerelease script (Nicholas C. Zakas)
* 688f277 Fix: Set proper exit code for Node > 0.10 (fixes #4691) (Nicholas C. Zakas)
* 58715e9 Fix: Use single quotes in context.report messages (fixes #4845) (Joe Lencioni)
* 5b7586b Fix: do not require a @return tag for @interface (fixes #4860) (Tim Schaub)
* d43f26c Breaking: migrate from minimatch to node-ignore (fixes #2365) (Stefan Grönke)
* c07ca39 Breaking: merges keyword spacing rules (fixes #3869) (Toru Nagashima)
* 871f534 Upgrade: Optionator version to 0.8.1 (fixes #4851) (Eric Johnson)
* 82d4cd9 Update: Add atomtest env (fixes #4848) (Andres Suarez)
* 9c9beb5 Update: Add "ignore" override for operator-linebreak (fixes #4294) (Rajendra Patil)
* 9c03abc Update: Add "allowCall" option (fixes #4011) (Rajendra Patil)
* 29516f1 Docs: fix migration guide for no-arrow-condition rule (Peter Newnham)
* 2ef7549 Docs: clarify remedy to some prefer-const errors (Turadg Aleahmad)
* 1288ba4 Update: Add default limit to `complexity` (fixes #4808) (Ian VanSchooten)
* d3e8179 Fix: env is rewritten by modules (fixes #4814) (Toru Nagashima)
* fd72aba Docs: Example fix for `no-extra-parens` rule (fixes #3527) (Gyandeep Singh)
* 315f272 Fix: Change max-warnings type to Int (fixes #4660) (George Zahariev)
* 5050768 Update: Ask for `commonjs` under config init (fixes #3553) (Gyandeep Singh)
* 4665256 New: Add no-whitespace-before-property rule (fixes #1086) (Kai Cataldo)
* f500d7d Fix: allow extending @scope/eslint/file (fixes #4800) (André Cruz)
* 5ab564e New: 'ignoreArrayIndexes' option for 'no-magic-numbers' (fixes #4370) (Christian Schuller)
* 97cdb95 New: Add no-useless-constructor rule (fixes #4785) (alberto)
* b9bcbaf Fix: Bug in no-extra-bind (fixes #4806) (Andres Kalle)
* 246a6d2 Docs: Documentation fix (Andres Kalle)
* 9ea6b36 Update: Ignore case in jsdoc tags (fixes #4576) (alberto)
* acdda24 Fix: ignore argument parens in no-unexpected-multiline (fixes #4658) (alberto)
* 4931f56 Update: optionally allow bitwise operators (fixes #4742) (Swaagie)

v2.0.0-alpha-2 - December 23, 2015

* Build: Add prerelease script (Nicholas C. Zakas)
* Update: Allow to omit semi for one-line blocks (fixes #4385) (alberto)
* Fix: Handle getters and setters in key-spacing (fixes #4792) (Brandon Mills)
* Fix: ObjectRestSpread throws error in key-spacing rule (fixes #4763) (Ziad El Khoury Hanna)
* Docs: Typo in generator-star (alberto)
* Fix: Backtick behavior in quotes rule (fixes #3090) (Nicholas C. Zakas)
* Fix: Empty schemas forbid any options (fixes #4789) (Brandon Mills)
* Fix: Remove `isMarkedAsUsed` function name (fixes #4783) (Gyandeep Singh)
* Fix: support arrow functions in no-return-assign (fixes #4743) (alberto)
* Docs: Add license header to Working with Rules guide (Brandon Mills)
* Fix: RuleTester to show parsing errors (fixes #4779) (Nicholas C. Zakas)
* Docs: Escape underscores in no-path-concat (alberto)
* Update: configuration for classes in space-before-blocks (fixes #4089) (alberto)
* Docs: Typo in no-useless-concat (alberto)
* Docs: fix typos, suggests (molee1905)
* Docs: Typos in space-before-keywords and space-unary-ops (fixes #4771) (alberto)
* Upgrade: beefy to ^2.0.0, fixes installation errors (fixes #4760) (Kai Cataldo)
* Docs: Typo in no-unexpected-multiline (fixes #4756) (alberto)
* Update: option to ignore top-level max statements (fixes #4309) (alberto)
* Update: Implement auto fix for semi-spacing rule (fixes #3829) (alberto)
* Fix: small typos in code examples (Plusb Preco)
* Docs: Add section on file extensions to user-guide/configuring (adam)
* Fix: Comma first issue in `indent` (fixes #4739, fixes #3456) (Gyandeep Singh)
* Fix: no-constant-condition false positive (fixes #4737) (alberto)
* Fix: Add source property for fatal errors (fixes #3325) (Gyandeep Singh)
* New: Add a comment length option to the max-len rule (fixes #4665) (Ian)
* Docs: RuleTester doesn't require any tests (fixes #4681) (alberto)
* Fix: Remove path analysis from debug log (fixes #4631) (Ilya Volodin)
* Fix: Set null to property ruleId when fatal is true (fixes #4722) (Sébastien Règne)
* New: Visual Studio compatible formatter (fixes #4708) (rhpijnacker)
* New: Add greasemonkey environment (fixes #4715) (silverwind)
* Fix: always-multiline for comma-dangle import (fixes #4704) (Nicholas C. Zakas)
* Fix: Check 1tbs non-block else (fixes #4692) (Nicholas C. Zakas)
* Fix: Apply environment configs last (fixes #3915) (Nicholas C. Zakas)
* New: `no-unmodified-loop-condition` rule (fixes #4523) (Toru Nagashima)
* Breaking: deprecate `no-arrow-condition` rule (fixes #4417) (Luke Karrys)
* Update: Add cwd option for cli-engine (fixes #4472) (Ilya Volodin)
* New: Add no-confusing-arrow rule (refs #4417) (Luke Karrys)
* Fix: ensure `ConfigOps.merge` do a deep copy (fixes #4682) (Toru Nagashima)
* Fix: `no-invalid-this` allows this in static method (fixes #4669) (Toru Nagashima)
* Fix: Export class syntax for `require-jsdoc` rule (fixes #4667) (Gyandeep Singh)
* Update: Add "safe" mode to strict (fixes #3306) (Brandon Mills)

v2.0.0-alpha-1 - December 11, 2015

* Breaking: Correct links between variables and references (fixes #4615) (Toru Nagashima)
* Fix: Update rule tests for parser options (fixes #4673) (Nicholas C. Zakas)
* Breaking: Implement parserOptions (fixes #4641) (Nicholas C. Zakas)
* Fix: max-len rule overestimates the width of some tabs (fixes #4661) (Nick Evans)
* New: Add no-implicit-globals rule (fixes #4542) (Joshua Peek)
* Update: `no-use-before-define` checks invalid initializer (fixes #4280) (Toru Nagashima)
* Fix: Use oneValuePerFlag for --ignore-pattern option (fixes #4507) (George Zahariev)
* New: `array-callback-return` rule (fixes #1128) (Toru Nagashima)
* Upgrade: Handlebars to >= 4.0.5 for security reasons (fixes #4642) (Jacques Favreau)
* Update: Add class body support to `indent` rule (fixes #4372) (Gyandeep Singh)
* Breaking: Remove space-after-keyword newline check (fixes #4149) (Nicholas C. Zakas)
* Breaking: Treat package.json like the rest of configs (fixes #4451) (Ilya Volodin)
* Docs: writing mistake (molee1905)
* Update: Add 'method' option to no-empty (fixes #4605) (Kai Cataldo)
* Breaking: Remove autofix from eqeqeq (fixes #4578) (Ilya Volodin)
* Breaking: Remove ES6 global variables from builtins (fixes #4085) (Brandon Mills)
* Fix: Handle forbidden LineTerminators in no-extra-parens (fixes #4229) (Brandon Mills)
* Update: Option to ignore constructor Fns object-shorthand (fixes #4487) (Kai Cataldo)
* Fix: Check YieldExpression argument in no-extra-parens (fixes #4608) (Brandon Mills)
* Fix: Do not cache `package.json` (fixes #4611) (Spain)
* Build: Consume no-underscore-dangle allowAfterThis option (fixes #4599) (Kevin Partington)
* New: Add no-restricted-imports rule (fixes #3196) (Guy Ellis)
* Docs: no-extra-semi no longer refers to deprecated rule (fixes #4598) (Kevin Partington)
* Fix: `consistent-return` checks the last (refs #3530, fixes #3373) (Toru Nagashima)
* Update: add class option to `no-use-before-define` (fixes #3944) (Toru Nagashima)
* Breaking: Simplify rule schemas (fixes #3625) (Nicholas C. Zakas)
* Docs: Update docs/rules/no-plusplus.md (Xiangyun Chi)
* Breaking: added bower_components to default ignore (fixes #3550) (Julian Laval)
* Fix: `no-unreachable` with the code path (refs #3530, fixes #3939) (Toru Nagashima)
* Fix: `no-this-before-super` with the code path analysis (refs #3530) (Toru Nagashima)
* Fix: `no-fallthrough` with the code path analysis (refs #3530) (Toru Nagashima)
* Fix: `constructor-super` with the code path analysis (refs #3530) (Toru Nagashima)
* Breaking: Switch to Espree 3.0.0 (fixes #4334) (Nicholas C. Zakas)
* Breaking: Freeze context object (fixes #4495) (Nicholas C. Zakas)
* Docs: Add Code of Conduct (fixes #3095) (Nicholas C. Zakas)
* Breaking: Remove warnings of readonly from `no-undef` (fixes #4504) (Toru Nagashima)
* Update: allowAfterThis option in no-underscore-dangle (fixes #3435) (just-boris)
* Fix: Adding options unit tests for --ignore-pattern (refs #4507) (Kevin Partington)
* Breaking: Implement yield-star-spacing rule (fixes #4115) (Bryan Smith)
* New: `prefer-rest-params` rule (fixes #4108) (Toru Nagashima)
* Update: `prefer-const` begins to cover separating init (fixes #4474) (Toru Nagashima)
* Fix: `no-eval` come to catch indirect eval (fixes #4399, fixes #4441) (Toru Nagashima)
* Breaking: Default no-magic-numbers to none. (fixes #4193) (alberto)
* Breaking: Allow empty arrow body (fixes #4411) (alberto)
* New: Code Path Analysis (fixes #3530) (Toru Nagashima)

v1.10.3 - December 1, 2015

* Docs: Update strict rule docs (fixes #4583) (Nicholas C. Zakas)
* Docs: Reference .eslintrc.* in contributing docs (fixes #4532) (Kai Cataldo)
* Fix: Add for-of to `curly` rule (fixes #4571) (Kai Cataldo)
* Fix: Ignore space before function in array start (fixes #4569) (alberto)

v1.10.2 - November 27, 2015

* Upgrade: escope@3.3.0 (refs #4485) (Nicholas C. Zakas)
* Upgrade: Pinned down js-yaml to avoid breaking dep (fixes #4553) (alberto)
* Fix: lines-around-comment with multiple comments (fixes #3509) (alberto)
* Upgrade: doctrine@0.7.1 (fixes #4545) (Kevin Partington)
* Fix: Bugfix for eqeqeq autofix (fixes #4540) (Kevin Partington)
* Fix: Add for-in to `curly` rule (fixes #4436) (Kai Cataldo)
* Fix: `valid-jsdoc` unneeded require check fix (fixes #4527) (Gyandeep Singh)
* Fix: `brace-style` ASI fix for if-else condition (fixes #4520) (Gyandeep Singh)
* Build: Add branch update during release process (fixes #4491) (Gyandeep Singh)
* Build: Allow revert commits in commit messages (fixes #4452) (alberto)
* Fix: Incorrect location in no-fallthrough (fixes #4516) (alberto)
* Fix: `no-spaced-func` had been crashed (fixes #4508) (Toru Nagashima)
* Fix: Add a RestProperty test of `no-undef` (fixes #3271) (Toru Nagashima)
* Docs: Load badge from HTTPS (Brian J Brennan)
* Build: Update eslint bot messages (fixes #4497) (Nicholas C. Zakas)

v1.10.1 - November 20, 2015

* Fix: Revert freezing context object (refs #4495) (Nicholas C. Zakas)
* 1.10.0 (Nicholas C. Zakas)

v1.10.0 - November 20, 2015

* Docs: Remove dupes from changelog (Nicholas C. Zakas)
* Update: --init to create extensioned files (fixes #4476) (Nicholas C. Zakas)
* Docs: Update description of exported comment (fixes #3916) (Nicholas C. Zakas)
* Docs: Move legacy rules to stylistic (files #4111) (Nicholas C. Zakas)
* Docs: Clean up description of recommended rules (fixes #4365) (Nicholas C. Zakas)
* Docs: Fix home directory config description (fixes #4398) (Nicholas C. Zakas)
* Update: Add class support to `require-jsdoc` rule (fixes #4268) (Gyandeep Singh)
* Update: return type error in `valid-jsdoc` rule (fixes #4443) (Gyandeep Singh)
* Update: Display errors at the place where fix should go (fixes #4470) (nightwing)
* Docs: Fix typo in default `cacheLocation` value (Andrew Hutchings)
* Fix: Handle comments in block-spacing (fixes #4387) (alberto)
* Update: Accept array for `ignorePattern` (fixes #3982) (Jesse McCarthy)
* Update: replace label and break with IIFE and return (fixes #4459) (Ilya Panasenko)
* Fix: space-before-keywords false positive (fixes #4449) (alberto)
* Fix: Improves performance (refs #3530) (Toru Nagashima)
* Fix: Autofix quotes produces invalid javascript (fixes #4380) (nightwing)
* Docs: Update indent.md (Nathan Brown)
* New: Disable comment config option (fixes #3901) (Matthew Riley MacPherson)
* New: Config files with extensions (fixes #4045, fixes #4263) (Nicholas C. Zakas)
* Revert "Update: Add JSX exceptions to no-extra-parens (fixes #4229)" (Brandon Mills)
* Update: Add JSX exceptions to no-extra-parens (fixes #4229) (Brandon Mills)
* Docs: Replace link to deprecated rule with newer rule (Andrew Marshall)
* Fix: `no-extend-native` crashed at empty defineProperty (fixes #4438) (Toru Nagashima)
* Fix: Support empty if blocks in lines-around-comment (fixes #4339) (alberto)
* Fix: `curly` warns wrong location for `else` (fixes #4362) (Toru Nagashima)
* Fix: `id-length` properties never option (fixes #4347) (Toru Nagashima)
* Docs: missing close rbracket in example (@storkme)
* Revert "Update: Allow empty arrow body (fixes #4411)" (Nicholas C. Zakas)
* Fix: eqeqeq autofix avoids clashes with space-infix-ops (fixes #4423) (Kevin Partington)
* Docs: Document semi-spacing behaviour (fixes #4404) (alberto)
* Update: Allow empty arrow body (fixes #4411) (alberto)
* Fix: Handle comments in comma-spacing (fixes #4389) (alberto)
* Update: Refactor eslint.verify args (fixes #4395) (Nicholas C. Zakas)
* Fix: no-undef-init should ignore const (fixes #4284) (Nicholas C. Zakas)
* Fix: Add the missing "as-needed" docs to the radix rule (fixes #4364) (Michał Gołębiowski)
* Fix: Display singular/plural version of "line" in message (fixes #4359) (Marius Schulz)
* Update: Add Popular Style Guides (fixes #4320) (Jamund Ferguson)
* Fix: eslint.report can be called w/o node if loc provided (fixes #4220) (Kevin Partington)
* Update: no-implicit-coercion validate AssignmentExpression (fixes #4348) (Ilya Panasenko)

v1.9.0 - November 6, 2015

* Update: Make radix accept a "as-needed" option (fixes #4048) (Michał Gołębiowski)
* Fix: Update the message to include number of lines (fixes #4342) (Brian Delahunty)
* Docs: ASI causes problem whether semicolons are used or not (Thai Pangsakulyanont)
* Fix: Fixer to not overlap ranges among fix objects (fixes #4321) (Gyandeep Singh)
* Update: Add default to `max-nested-callbacks` (fixes #4297) (alberto)
* Fix: Check comments in space-in-parens (fixes #4302) (alberto)
* Update: Add quotes to error messages to improve clarity (fixes #4313) (alberto)
* Fix: tests failing due to differences in temporary paths (fixes #4324) (alberto)
* Fix: Make tests compatible with Windows (fixes #4315) (Ian VanSchooten)
* Update: Extract glob and filesystem logic from cli-engine (fixes #4305) (Ian VanSchooten)
* Build: Clarify commit-check messages (fixes #4256) (Ian VanSchooten)
* Upgrade: Upgrade various dependencies (fixes #4303) (Gyandeep Singh)
* Build: Add node 5 to travis build (fixes #4310) (Gyandeep Singh)
* Fix: ensure using correct estraverse (fixes #3951) (Toru Nagashima)
* Docs: update docs about using gitignore (Mateusz Derks)
* Update: Detect and fix wrong linebreaks (fixes #3981) (alberto)
* New: Add no-case-declarations rule (fixes #4278) (Erik Arvidsson)

v1.8.0 - October 30, 2015

* Fix: Check for node property before testing type (fixes #4298) (Ian VanSchooten)
* Docs: Specify 'double' as default for quotes (fixes #4270) (Ian VanSchooten)
* Fix: Missing errors in space-in-parens (fixes #4257, fixes #3996) (alberto)
* Docs: fixed typo (Mathieu M-Gosselin)
* Fix: `cacheLocation` handles paths in windows style. (fixes #4285) (royriojas)
* Docs: fixed typo (mpal9000)
* Update: Add support for class in `valid-jsdoc` rule (fixes #4279) (Gyandeep Singh)
* Update: cache-file accepts a directory. (fixes #4241) (royriojas)
* Update: Add `maxEOF` to no-multiple-empty-lines (fixes #4235) (Adrien Vergé)
* Update: fix option for comma-spacing (fixes #4232) (HIPP Edgar (PRESTA EXT))
* Docs: Fix use of wrong word in configuration doc (Jérémie Astori)
* Fix: Prepare config before verifying SourceCode (fixes #4230) (Ian VanSchooten)
* Update: RuleTester come to check AST was not modified (fixes #4156) (Toru Nagashima)
* Fix: wrong count for 'no-multiple-empty-lines' on last line (fixes #4228) (alberto)
* Update: Add `allow` option to `no-shadow` rule (fixes #3035) (Gyandeep Singh)
* Doc: Correct the spelling of Alberto's surname (alberto)
* Docs: Add alberto as a committer (Gyandeep Singh)
* Build: Do not stub console in testing (fixes #1328) (Gyandeep Singh)
* Fix: Check node exists before checking type (fixes #4231) (Ian VanSchooten)
* Update: Option to exclude afterthoughts from no-plusplus (fixes #4093) (Brody McKee)
* New: Add rule no-arrow-condition (fixes #3280) (Luke Karrys)
* Update: Add linebreak style option to eol-last (fixes #4148) (alberto)
* New: arrow-body-style rule (fixes #4109) (alberto)

v1.7.3 - October 21, 2015

* Fix: Support comma-first style in key-spacing (fixes #3877) (Brandon Mills)
* Fix: no-magic-numbers: variable declarations (fixes #4192) (Ilya Panasenko)
* Fix: Support ES6 shorthand in key-spacing (fixes #3678) (Brandon Mills)
* Fix: `indent` array with memberExpression (fixes #4203) (Gyandeep Singh)
* Fix: `indent` param function on sameline (fixes #4174) (Gyandeep Singh)
* Fix: no-multiple-empty-lines fails when empty line at EOF (fixes #4214) (alberto)
* Fix: `comma-dangle` false positive (fixes #4200) (Nicholas C. Zakas)
* Fix: `valid-jsdoc` prefer problem (fixes #4205) (Nicholas C. Zakas)
* Docs: Add missing single-quote (Kevin Lamping)
* Fix: correct no-multiple-empty-lines at EOF (fixes #4140) (alberto)

v1.7.2 - October 19, 2015

* Fix: comma-dangle confused by parens (fixes #4195) (Nicholas C. Zakas)
* Fix: no-mixed-spaces-and-tabs (fixes #4189, fixes #4190) (alberto)
* Fix: no-extend-native disallow using Object.properties (fixes #4180) (Nathan Woltman)
* Fix: no-magic-numbers should ignore Number.parseInt (fixes #4167) (Henry Zhu)

v1.7.1 - October 16, 2015

* Fix: id-match schema (fixes #4155) (Nicholas C. Zakas)
* Fix: no-magic-numbers should ignore parseInt (fixes #4167) (Nicholas C. Zakas)
* Fix: `indent` param function fix (fixes #4165, fixes #4164) (Gyandeep Singh)

v1.7.0 - October 16, 2015

* Fix: array-bracket-spacing for empty array (fixes #4141) (alberto)
* Fix: `indent` arrow function check fix (fixes #4142) (Gyandeep Singh)
* Update: Support .js files for config (fixes #3102) (Gyandeep Singh)
* Fix: Make eslint-config-eslint work (fixes #4145) (Nicholas C. Zakas)
* Fix: `prefer-arrow-callback` had been wrong at arguments (fixes #4095) (Toru Nagashima)
* Docs: Update various rules docs (Nicholas C. Zakas)
* New: Create eslint-config-eslint (fixes #3525) (Nicholas C. Zakas)
* Update: RuleTester allows string errors in invalid cases (fixes #4117) (Kevin Partington)
* Docs: Reference no-unexpected-multiline in semi (fixes #4114) (alberto)
* Update: added exceptions to `lines-around-comment` rule. (fixes #2965) (Mathieu M-Gosselin)
* Update: Add `matchDescription` option to `valid-jsdoc` (fixes #2449) (Gyandeep Singh)
* Fix: check for objects or arrays in array-bracket-spacing (fixes #4083) (alberto)
* Docs: Alphabetize Rules lists (Kenneth Chung)
* Fix: message templates fail when no parameters are passed (fixes #4080) (Ilya Volodin)
* Fix: `indent` multi-line function call (fixes #4073, fixes #4075) (Gyandeep Singh)
* Docs: Improve comma-dangle documentation (Gilad Peleg)
* Fix: no-mixed-tabs-and-spaces fails with some comments (fixes #4086) (alberto)
* Fix: `semi` to check for do-while loops (fixes #4090) (Gyandeep Singh)
* Build: Fix path related failures on Windows in tests (fixes #4061) (Burak Yigit Kaya)
* Fix: `no-unused-vars` had been missing some parameters (fixes #4047) (Toru Nagashima)
* Fix: no-mixed-spaces-and-tabs with comments and templates (fixes #4077) (alberto)
* Update: Add `allow` option for `no-underscore-dangle` rule (fixes #2135) (Gyandeep Singh)
* Update: `allowArrowFunctions` option for `func-style` rule (fixes #1897) (Gyandeep Singh)
* Fix: Ignore template literals in no-mixed-tabs-and-spaces (fixes #4054) (Nicholas C. Zakas)
* Build: Enable CodeClimate (fixes #4068) (Nicholas C. Zakas)
* Fix: `no-cond-assign` had needed double parens in `for` (fixes #4023) (Toru Nagashima)
* Update: Ignore end of function in newline-after-var (fixes #3682) (alberto)
* Build: Performance perf to not ignore jshint file (refs #3765) (Gyandeep Singh)
* Fix: id-match bug incorrectly errors on `NewExpression` (fixes #4042) (Burak Yigit Kaya)
* Fix: `no-trailing-spaces` autofix to handle linebreaks (fixes #4050) (Gyandeep Singh)
* Fix: renamed no-magic-number to no-magic-numbers (fixes #4053) (Vincent Lemeunier)
* New: add "consistent" option to the "curly" rule (fixes #2390) (Benoît Zugmeyer)
* Update: Option to ignore for loops in init-declarations (fixes #3641) (alberto)
* Update: Add webextensions environment (fixes #4051) (Blake Winton)
* Fix: no-cond-assign should report assignment location (fixes #4040) (alberto)
* New: no-empty-pattern rule (fixes #3668) (alberto)
* Upgrade: Upgrade globals to 8.11.0 (fixes #3599) (Burak Yigit Kaya)
* Docs: Re-tag JSX code fences (fixes #4020) (Brandon Mills)
* New: no-magic-number rule (fixes #4027) (Vincent Lemeunier)
* Docs: Remove list of users from README (fixes #3881) (Brandon Mills)
* Fix: `no-redeclare` and `no-sahadow` for builtin globals (fixes #3971) (Toru Nagashima)
* Build: Add `.eslintignore` file for the project (fixes #3765) (Gyandeep Singh)

v1.6.0 - October 2, 2015

* Fix: cache is basically not working (fixes #4008) (Richard Hansen)
* Fix: a test failure on Windows (fixes #3968) (Toru Nagashima)
* Fix: `no-invalid-this` had been missing globals in node (fixes #3961) (Toru Nagashima)
* Fix: `curly` with `multi` had false positive (fixes #3856) (Toru Nagashima)
* Build: Add load performance check inside perf function (fixes #3994) (Gyandeep Singh)
* Fix: space-before-keywords fails with super keyword (fixes #3946) (alberto)
* Fix: CLI should not fail on account of ignored files (fixes #3978) (Dominic Barnes)
* Fix: brace-style rule incorrectly flagging switch (fixes #4002) (Aparajita Fishman)
* Update: Implement auto fix for space-unary-ops rule (fixes #3976) (alberto)
* Update: Implement auto fix for computed-property-spacing (fixes #3975) (alberto)
* Update: Implement auto fix for no-multi-spaces rule (fixes #3979) (alberto)
* Fix: Report shorthand method names in complexity rule (fixes #3955) (Tijn Kersjes)
* Docs: Add note about typeof check for isNaN (fixes #3985) (Daniel Lo Nigro)
* Update: ESLint reports parsing errors with clear prefix. (fixes #3555) (Kevin Partington)
* Build: Update markdownlint dependency (fixes #3954) (David Anson)
* Update: `no-mixed-require` to have non boolean option (fixes #3922) (Gyandeep Singh)
* Fix: trailing spaces auto fix to check for line breaks (fixes #3940) (Gyandeep Singh)
* Update: Add `typeof` option to `no-undef` rule (fixes #3684) (Gyandeep Singh)
* Docs: Fix explanation and typos for accessor-pairs (alberto)
* Docs: Fix typos for camelcase (alberto)
* Docs: Fix typos for max-statements (Danny Guo)
* Update: Implement auto fix for object-curly-spacing (fixes #3857) (alberto)
* Update: Implement auto fix for array-bracket-spacing rule (fixes #3858) (alberto)
* Fix: Add schema to `global-require` rule (fixes #3923) (Gyandeep Singh)
* Update: Apply lazy loading for rules (fixes #3930) (Gyandeep Singh)
* Docs: Fix typo for arrow-spacing (Danny Guo)
* Docs: Fix typos for wrap-regex (Danny Guo)
* Docs: Fix explanation for space-before-keywords (Danny Guo)
* Docs: Fix typos for operator-linebreak (Danny Guo)
* Docs: Fix typos for callback-return (Danny Guo)
* Fix: no-trailing-spaces autofix to account for blank lines (fixes #3912) (Gyandeep Singh)
* Docs: Fix example in no-negated-condition.md (fixes #3908) (alberto)
* Update:warn message use @return when prefer.returns=return (fixes #3889) (闲耘™)
* Update: Implement auto fix for generator-star-spacing rule (fixes #3873) (alberto)
* Update: Implement auto fix for arrow-spacing rule (fixes #3860) (alberto)
* Update: Implement auto fix for block-spacing rule (fixes #3859) (alberto)
* Fix: Support allman style for switch statement (fixes #3903) (Gyandeep Singh)
* New: no-negated-condition rule (fixes #3740) (alberto)
* Docs: Fix typo in blog post template (Nicholas C. Zakas)
* Update: Add env 'nashorn' to support Java 8 Nashorn Engine (fixes #3874) (Benjamin Winterberg)
* Docs: Prepare for rule doc linting (refs #2271) (Ian VanSchooten)

v1.5.1 - September 22, 2015

* Fix: valid-jsdoc fix for param with properties (fixes #3476) (Gyandeep Singh)
* Fix: valid-jsdoc error with square braces (fixes #2270) (Gyandeep Singh)
* Upgrade: `doctrine` to 0.7.0 (fixes #3891) (Gyandeep Singh)
* Fix: `space-before-keywords` had been wrong on getters (fixes #3854) (Toru Nagashima)
* Fix: `no-dupe-args` had been wrong for nested destructure (fixes #3867) (Toru Nagashima)
* Docs: io.js is the new Node.js (thefourtheye)
* Docs: Fix method signature on working-with-rules docs (fixes #3862) (alberto)
* Docs: Add related ternary links (refs #3835) (Ian VanSchooten)
* Fix: don’t ignore config if cwd is the home dir (fixes #3846) (Mathias Schreck)
* Fix: `func-style` had been warning arrows with `this` (fixes #3819) (Toru Nagashima)
* Fix: `space-before-keywords`; allow opening curly braces (fixes #3789) (Marko Raatikka)
* Build: Fix broken .gitattributes generation (fixes #3566) (Nicholas C. Zakas)
* Build: Fix formatter docs generation (fixes #3847) (Nicholas C. Zakas)

v1.5.0 - September 18, 2015

* Fix: invalidate cache when config changes. (fixes #3770) (royriojas)
* Fix: function body indent issues (fixes #3614, fixes #3799) (Gyandeep Singh)
* Update: Add configuration option to `space-before-blocks` (fixes #3758) (Phil Vargas)
* Fix: space checking between tokens (fixes #2211) (Nicholas C. Zakas)
* Fix: env-specified ecmaFeatures had been wrong (fixes #3735) (Toru Nagashima)
* Docs: Change example wording from warnings to problems (fixes #3676) (Ian VanSchooten)
* Build: Generate formatter example docs (fixes #3560) (Ian VanSchooten)
* New: Add --debug flag to CLI (fixes #2692) (Nicholas C. Zakas)
* Docs: Update no-undef-init docs (fixes #3170) (Nicholas C. Zakas)
* Docs: Update no-unused-expressions docs (fixes #3685) (Nicholas C. Zakas)
* Docs: Clarify node types in no-multi-spaces (fixes #3781) (Nicholas C. Zakas)
* Docs: Update new-cap docs (fixes #3798) (Nicholas C. Zakas)
* Fix: `space-before-blocks` had conflicted `arrow-spacing` (fixes #3769) (Toru Nagashima)
* Fix: `comma-dangle` had not been checking imports/exports (fixes #3794) (Toru Nagashima)
* Fix: tests fail due to differences in temporary paths. (fixes #3778) (royriojas)
* Fix: Directory ignoring should work (fixes #3812) (Nicholas C. Zakas)
* Fix: Ensure **/node_modules works in ignore files (fixes #3788) (Nicholas C. Zakas)
* Update: Implement auto fix for `space-infix-ops` rule (fixes #3801) (Gyandeep Singh)
* Fix: `no-warning-comments` can't be set via config comment (fixes #3619) (Burak Yigit Kaya)
* Update: `key-spacing` should allow 1+ around colon (fixes #3363) (Burak Yigit Kaya)
* Fix: false alarm of semi-spacing with semi set to never (fixes #1983) (Chen Yicai)
* Fix: Ensure ./ works correctly with CLI (fixes #3792) (Nicholas C. Zakas)
* Docs: add more examples + tests for block-scoped-var (fixes #3791) (JT)
* Update: Implement auto fix for `indent` rule (fixes #3734) (Gyandeep Singh)
* Fix: `space-before-keywords` fails to handle some cases (fixes #3756) (Marko Raatikka)
* Docs: Add if-else example (fixes #3722) (Ian VanSchooten)
* Fix: jsx-quotes exception for attributes without value (fixes #3793) (Mathias Schreck)
* Docs: Fix closing code fence on cli docs (Ian VanSchooten)
* Update: Implement auto fix for `space-before-blocks` rule (fixes #3776) (Gyandeep Singh)
* Update: Implement auto fix for `space-after-keywords` rule (fixes #3773) (Gyandeep Singh)
* Fix: `semi-spacing` had conflicted with `block-spacing` (fixes #3721) (Toru Nagashima)
* Update: Implement auto fix for `space-before-keywords` rule (fixes #3771) (Gyandeep Singh)
* Update: auto fix for space-before-function-paren rule (fixes #3766) (alberto)
* Update: Implement auto fix for `no-extra-semi` rule (fixes #3745) (Gyandeep Singh)
* Update: Refactors the traversing logic (refs #3530) (Toru Nagashima)
* Update: Implement auto fix for `space-return-throw-case` (fixes #3732) (Gyandeep Singh)
* Update: Implement auto fix for `no-spaced-func` rule (fixes #3728) (Gyandeep Singh)
* Update: Implement auto fix for `eol-last` rule (fixes #3725) (Gyandeep Singh)
* Update: Implement auto fix for `no-trailing-spaces` rule (fixes #3723) (Gyandeep Singh)

v1.4.3 - September 15, 2015

* Fix: Directory ignoring should work (fixes #3812) (Nicholas C. Zakas)
* Fix: jsx-quotes exception for attributes without value (fixes #3793) (Mathias Schreck)

v1.4.2 - September 15, 2015

* Fix: Ensure **/node_modules works in ignore files (fixes #3788) (Nicholas C. Zakas)
* Fix: Ensure ./ works correctly with CLI (fixes #3792) (Nicholas C. Zakas)

v1.4.1 - September 11, 2015

* Fix: CLIEngine default cache parameter name (fixes #3755) (Daniel G. Taylor)
* Fix: Glob pattern from .eslintignore not applied (fixes #3750) (Burak Yigit Kaya)
* Fix: Skip JSDoc from NewExpression (fixes #3744) (Nicholas C. Zakas)
* Docs: Shorten and simplify autocomment for new issues (Nicholas C. Zakas)

v1.4.0 - September 11, 2015

* Docs: Add new formatters to API docs (Ian VanSchooten)
* New: Implement autofixing (fixes #3134) (Nicholas C. Zakas)
* Fix: Remove temporary `"allow-null"` (fixes #3705) (Toru Nagashima)
* Fix: `no-unused-vars` had been crashed at `/*global $foo*/` (fixes #3714) (Toru Nagashima)
* Build: check-commit now checks commit message length. (fixes #3706) (Kevin Partington)
* Fix: make getScope acquire innermost scope (fixes #3700) (voideanvalue)
* Docs: Fix spelling mistake (domharrington)
* Fix: Allow whitespace in rule message parameters. (fixes #3690) (Kevin Partington)
* Fix: Eqeqeq rule with no option does not warn on 'a == null' (fixes #3699) (fediev)
* Fix: `no-unused-expressions` with `allowShortCircuit` false positive if left has no effect (fixes #3675) (Toru Nagashima)
* Update: Add Node 4 to travis builds (fixes #3697) (Ian VanSchooten)
* Fix: Not check for punctuator if on same line as last var (fixes #3694) (Gyandeep Singh)
* Docs: Make `quotes` docs clearer (fixes #3646) (Nicholas C. Zakas)
* Build: Increase mocha timeout (fixes #3692) (Nicholas C. Zakas)
* Fix: `no-extra-bind` to flag all arrow funcs (fixes #3672) (Nicholas C. Zakas)
* Docs: Update README with release and sponsor info (Nicholas C. Zakas)
* Fix: `object-curly-spacing` had been crashing on an empty object pattern (fixes #3658) (Toru Nagashima)
* Fix: `no-extra-parens` false positive at IIFE with member accessing (fixes #3653) (Toru Nagashima)
* Fix: `comma-dangle` with `"always"`/`"always-multiline"` false positive after a rest element (fixes #3627) (Toru Nagashima)
* New: `jsx-quotes` rule (fixes #2011) (Mathias Schreck)
* Docs: Add linting for second half of rule docs (refs #2271) (Ian VanSchooten)
* Fix: `no-unused-vars` had not shown correct locations for `/*global` (fixes #3617) (Toru Nagashima)
* Fix: `space-after-keywords` not working for `catch` (fixes #3654) (Burak Yigit Kaya)
* Fix: Incorrectly warning about ignored files (fixes #3649) (Burak Yigit Kaya)
* Fix: Indent rule VariableDeclarator doesn't apply to arrow functions (fixes #3661) (Burak Yigit Kaya)
* Upgrade: Consuming handlebars@^4.0.0 (fixes #3632) (Kevin Partington)
* Docs: Fixing typos in plugin processor section. (fixes #3648) (Kevin Partington)
* Fix: Invalid env keys would cause an unhandled exception.(fixes #3265) (Ray Booysen)
* Docs: Fixing broken link in documentation (Ilya Volodin)
* Update: Check for default assignment in no-unneeded-ternary (fixes #3232) (cjihrig)
* Fix: `consistent-as-needed` mode with `keyword: true` (fixes #3636) (Alex Guerrero)
* New: Implement cache in order to only operate on changed files since previous run. (fixes #2998) (Roy Riojas)
* Update: Grouping related CLI options. (fixes #3612) (Kevin Partington)
* Update: Using @override does not require @param or @returns (fixes #3629) (Whitney Young)
* Docs: Use eslint-env in no-undef (fixes #3616) (Ian VanSchooten)
* New: `require-jsdoc` rule (fixes #1842) (Gyandeep Singh)
* New: Support glob path on command line (fixes #3402) (Burak Yigit Kaya)
* Update: Short circuit and ternary support in no-unused-expressions (fixes #2733) (David Warkentin)
* Docs: Replace to npmjs.com (Ryuichi Okumura)
* Fix: `indent` should only indent chain calls if the first call is single line (fixes #3591) (Burak Yigit Kaya)
* Fix: `quote-props` should not crash for object rest spread syntax (fixes #3595) (Joakim Carlstein)
* Update: Use `globals` module for the `commonjs` globals (fixes #3606) (Sindre Sorhus)
* New: `no-restricted-syntax` rule to forbid certain syntax (fixes #2422) (Burak Yigit Kaya)
* Fix: `no-useless-concat` false positive at numbers (fixes #3575, fixes #3589) (Toru Nagashima)
* New: Add --max-warnings flag to CLI (fixes #2769) (Kevin Partington)
* New: Add `parser` as an option (fixes #3127) (Gyandeep Singh)
* New: `space-before-keywords` rule (fixes #1631) (Marko Raatikka)
* Update: Allowing inline comments to disable eslint rules (fixes #3472) (Whitney Young)
* Docs: Including for(;;) as valid case in no-constant-condition (Kevin Partington)
* Update: Add quotes around the label in `no-redeclare` error messages (fixes #3583) (Ian VanSchooten)
* Docs: correct contributing URL (Dieter Luypaert)
* Fix: line number for duplicate object keys error (fixes #3573) (Elliot Lynde)
* New: global-require rule (fixes #2318) (Jamund Ferguson)

v1.3.1 - August 29, 2015

* Fix: `indent` to not crash on empty files (fixes #3570) (Gyandeep Singh)
* Fix: Remove unused config file (fixes #2227) (Gyandeep Singh)

v1.3.0 - August 28, 2015

* Build: Autogenerate release blog post (fixes #3562) (Nicholas C. Zakas)
* New: `no-useless-concat` rule (fixes #3506) (Henry Zhu)
* Update: Add `keywords` flag to `consistent-as-needed` mode in `quote-props` (fixes #3532) (Burak Yigit Kaya)
* Update: adds `numbers` option  to quote-props (fixes #2914) (Jose Roberto Vidal)
* Fix: `quote-props` rule should ignore computed and shorthand properties (fixes #3557) (fixes #3544) (Burak Yigit Kaya)
* Docs: Add config comments for rule examples 'accessor-pairs' to 'no-extra-semi' (refs #2271) (Ian VanSchooten)
* Update: Return to accept `undefined` type (fixes #3382) (Gyandeep Singh)
* New: Added HTML formatter (fixes #3505) (Julian Laval)
* Fix: check space after yield keyword in space-unary-ops (fixes #2707) (Mathias Schreck)
* Docs: (curly) Fix broken code in example (Kent C. Dodds)
* Update: Quote var name in `no-unused-vars` error messages (refs #3526) (Burak Yigit Kaya)
* Update: Move methods to SourceCode (fixes #3516) (Nicholas C. Zakas)
* Fix: Don't try too hard to find fault in `no-implicit-coercion` (refs #3402) (Burak Yigit Kaya)
* Fix: Detect ternary operator in operator-linebreak rule (fixes #3274) (Burak Yigit Kaya)
* Docs: Clearer plugin rule configuration (fixes #2022) (Nicholas C. Zakas)
* Update: Add quotes around the label in  `no-empty-label` error reports (fixes #3526) (Burak Yigit Kaya)
* Docs: Turn off Liquid in example (Nicholas C. Zakas)
* Docs: Mention CommonJS along with Node.js (fixes #3388) (Nicholas C. Zakas)
* Docs: Make it clear which rules are recommended (fixes #3398) (Nicholas C. Zakas)
* Docs: Add links to JSON Schema resources (fixes #3411) (Nicholas C. Zakas)
* Docs: Add more info to migration guide (fixes #3439) (Nicholas C. Zakas)
* Fix: ASI indentation issue (fixes #3514) (Burak Yigit Kaya)
* Fix: Make `no-implicit-coercion` smarter about numerical expressions (fixes #3510) (Burak Yigit Kaya)
* Fix: `prefer-template` had not been handling TemplateLiteral as literal node (fixes #3507) (Toru Nagashima)
* Update: `newline-after-var` Allow comment + blank after var (fixes #2852) (Ian VanSchooten)
* Update: Add `unnecessary` option to `quote-props` (fixes #3381) (Burak Yigit Kaya)
* Fix: `indent` shouldn't check the last line unless it is a punctuator (fixes #3498) (Burak Yigit Kaya)
* Fix: `indent` rule does not indent when doing multi-line chain calls (fixes #3279) (Burak Yigit Kaya)
* Fix: sort-vars rule fails when memo is undefined (fixes #3474) (Burak Yigit Kaya)
* Fix: `brace-style` doesn't report some closing brace errors (fixes #3486) (Burak Yigit Kaya)
* Update: separate options for block and line comments in `spaced-comment` rule (fixes #2897) (Burak Yigit Kaya)
* Fix: `indent` does not check FunctionDeclaration nodes properly (fixes #3173) (Burak Yigit Kaya)
* Update: Added "properties" option to `id-length` rule to ignore property names. (fixes #3450) (Mathieu M-Gosselin)
* Update: add new ignore pattern options to no-unused-vars (fixes #2321) (Mathias Schreck)
* New: Protractor environment (fixes #3457) (James Whitney)
* Docs: Added section to shareable config (Gregory Waxman)
* Update: Allow pre-parsed code (fixes #1025, fixes #948) (Nicholas C. Zakas)

v1.2.1 - August 20, 2015

* Fix: "key-spacing" crashes eslint on object literal shorthand properties  (fixes #3463) (Burak Yigit Kaya)
* Fix: ignore leading space check for `null` elements in comma-spacing (fixes #3392) (Mathias Schreck)
* Fix: `prefer-arrow-callback` false positive at recursive functions (fixes #3454) (Toru Nagashima)
* Fix: one-var rule doesn’t have default options (fixes #3449) (Burak Yigit Kaya)
* Fix: Refactor `no-duplicate-case` to be simpler and more efficient (fixes #3440) (Burak Yigit Kaya)
* Docs: Fix trailing spaces in README (Nicholas C. Zakas)
* Docs: Update gyandeeps and add byk (Nicholas C. Zakas)
* Docs: Update plugins documentation for 1.0.0 (Nicholas C. Zakas)
* Docs: `object-curly-spacing` doc is inaccurate about exceptions (Burak Yigit Kaya)
* Fix: `object-curly-spacing` shows the incorrect column for opening brace (fixes #3438) (Burak Yigit Kaya)

v1.2.0 - August 18, 2015

* Update: add support for semicolon in comma-first setup in indent rule  (fixes #3423) (Burak Yigit Kaya)
* Docs: better JSDoc for indent rule (Burak Yigit Kaya)
* Docs: Document the second argument of `CLIEngine.executeOnText()` (Sindre Sorhus)
* New: `no-dupe-class-members` rule (fixes #3294) (Toru Nagashima)
* Fix: exclude `AssignmentExpression` and `Property` nodes from extra indentation on first line (fixes #3391) (Burak Yigit Kaya)
* Update: Separate indent options for var, let and const (fixes #3339) (Burak Yigit Kaya)
* Fix: Add AssignmentPattern to space-infix-ops (fixes #3380) (Burak Yigit Kaya)
* Docs: Fix typo: exception label (tienslebien)
* Update: Clean up tests for CLI config support (refs #2543) (Gyandeep Singh)
* New: `block-spacing` rule (fixes #3303) (Toru Nagashima)
* Docs: Update docs for no-iterator (fixes #3405) (Nicholas C. Zakas)
* Upgrade: bump `espree` dependency to `2.2.4` (fixes #3403) (Burak Yigit Kaya)
* Fix: false positive on switch 'no duplicate case', (fixes #3408) (Cristian Carlesso)
* Fix: `valid-jsdoc` test does not recognize aliases for `@param` (fixes #3399) (Burak Yigit Kaya)
* New: enable `-c` flag to accept a shareable config (fixes #2543) (Shinnosuke Watanabe)
* Fix: Apply plugin given in CLI (fixes #3383) (Ian VanSchooten)
* New: Add commonjs environment (fixes #3377) (Nicholas C. Zakas)
* Docs: Update no-unused-var docs (Nicholas C. Zakas)
* Fix: trailing commas in object-curly-spacing for import/export (fixes #3324) (Henry Zhu)
* Update: Make `baseConfig` to behave as other config options (fixes #3371) (Gyandeep Singh)
* Docs: Add "Compatibility" section to linebreak-style (Vitor Balocco)
* New: `prefer-arrow-callback` rule (fixes #3140) (Toru Nagashima)
* Docs: Clarify what an unused var is (fixes #2342) (Nicholas C. Zakas)
* Docs: Mention double-byte character limitation in max-len (fixes #2370) (Nicholas C. Zakas)
* Fix: object curly spacing incorrectly warning for import with default and multiple named specifiers (fixes #3370) (Luke Karrys)
* Fix: Indent rule errors with array of objects (fixes #3329) (Burak Yigit Kaya)
* Update: Make it clear that `space-infix-ops` support `const` (fixes #3299) (Burak Yigit Kaya)
* New: `prefer-template` rule (fixes #3014) (Toru Nagashima)
* Docs: Clarify `no-process-env` docs (fixes #3318) (Nicholas C. Zakas)
* Docs: Fix arrow name typo (fixes #3309) (Nicholas C. Zakas)
* Update: Improve error message for `indent` rule violation (fixes #3340) (Burak Yigit Kaya)
* Fix: radix rule does not apply for Number.parseInt (ES6) (fixes #3364) (Burak Yigit Kaya)
* Fix: `key-spacing.align` doesn't pay attention to non-whitespace before key (fixes #3267) (Burak Yigit Kaya)
* Fix: arrow-parens & destructuring/default params (fixes #3353) (Jamund Ferguson)
* Update: Add support for Allman to brace-style rule, brackets on newline (fixes #3347) (Burak Yigit Kaya)
* Fix: Regression no-catch-shadow (1.1.0) (fixes #3322) (Burak Yigit Kaya)
* Docs: remove note outdated in 1.0.0 (Denis Sokolov)
* Build: automatically convert line endings in release script (fixes #2642) (Burak Yigit Kaya)
* Update: allow disabling new-cap on object methods (fixes #3172) (Burak Yigit Kaya)
* Update: Improve checkstyle format (fixes #3183) (Burak Yigit Kaya)
* Fix: Indent rule errors if an array literal starts a new statement (fixes #3328) (Burak Yigit Kaya)
* Update: Improve validation error messages (fixes #3193) (Burak Yigit Kaya)
* Docs: fix syntax error in space-before-function-paren (Fabrício Matté)
* Fix: `indent` rule to check for last line correctly (fixes #3327) (Gyandeep Singh)
* Fix: Inconsistent off-by-one errors with column numbers (fixes #3231) (Burak Yigit Kaya)
* Fix: Keyword "else" must not be followed by a newline (fixes #3226) (Burak Yigit Kaya)
* Fix: `id-length` does not work for most of the new ES6 patterns (fixes #3286) (Burak Yigit Kaya)
* Fix: Spaced Comment Exceptions Not Working (fixes #3276) (Jamund Ferguson)

v1.1.0 - August 7, 2015

* Update: Added as-needed option to arrow-parens (fixes #3277) (Jamund Ferguson)
* Fix: curly-spacing missing import case (fixes #3302) (Jamund Ferguson)
* Fix: `eslint-env` in comments had not been setting `ecmaFeatures` (fixes #2134) (Toru Nagashima)
* Fix: `es6` env had been missing `spread` and `newTarget` (fixes #3281) (Toru Nagashima)
* Fix: Report no-spaced-func on last token before paren (fixes #3289) (Benjamin Woodruff)
* Fix: Check for null elements in indent rule (fixes #3272) (Gyandeep Singh)
* Docs: Use backticks for option heading (Gyandeep Singh)
* Fix: `no-invalid-this` had been missing jsdoc comment (fixes #3287) (Toru Nagashima)
* Fix: `indent` rule for multi-line objects and arrays (fixes #3236) (Gyandeep Singh)
* Update: add new `multi-or-nest` option for the `curly` rule (fixes #1806) (Ivan Nikulin)
* Fix: `no-cond-assign` had been missing simplest pattern (fixes #3249) (Toru Nagashima)
* Fix: id-length rule doesn't catch violations in arrow function parameters (fixes #3275) (Burak Yigit Kaya)
* New: Added grep-style formatter (fixes #2991) (Nobody Really)
* Update: Split out generic AST methods into utility (fixes #962) (Gyandeep Singh)
* Fix: `accessor-pairs` false positive (fixes #3262) (Toru Nagashima)
* Fix: `context.getScope()` returns correct scope in blockBindings (fixes #3254) (Toru Nagashima)
* Update: Expose `getErrorResults` as a static method on `CLIEngine` (fixes #3242) (Gyandeep Singh)
* Update: Expose `getFormatter` as a static method on `CLIEngine` (fixes #3239) (Gyandeep Singh)
* Docs: use correct encoding for id-match.md (fixes #3246) (Matthieu Larcher)
* Docs: place id-match rule at correct place in README.md (fixes #3245) (Matthieu Larcher)
* Docs: Update no-proto.md (Joe Zimmerman)
* Docs: Fix typo in object-shorthand docs (Gunnar Lium)
* Upgrade: inquirer dependency (fixes #3241) (Gyandeep Singh)
* Fix: `indent` rule for objects and nested one line blocks (fixes #3238, fixes #3237) (Gyandeep Singh)
* Docs: Fix wrong options in examples of key-spacing (keik)
* Docs: Adds missing "not" to semi.md (Marius Schulz)
* Docs: Update no-multi-spaces.md (Kenneth Powers)
* Fix: `indent` to not error on same line nodes (fixes #3228) (Gyandeep Singh)
* New: Jest environment (fixes #3212) (Darshak Parikh)

v1.0.0 - July 31, 2015

* Update: merge `no-reserved-keys` into `quote-props` (fixes #1539) (Jose Roberto Vidal)
* Fix: `indent` error message (fixes #3220) (Gyandeep Singh)
* Update: Add embertest env (fixes #3205) (ismay)
* Docs: Correct documentation errors for `id-length` rule. (Jess Telford)
* Breaking: `indent` rule to have node specific options (fixes #3210) (Gyandeep Singh)
* Fix: space-after-keyword shouldn't allow newlines (fixes #3198) (Brandon Mills)
* New: Add JSON formatter (fixes #3036) (Burak Yigit Kaya)
* Breaking: Switch to RuleTester (fixes #3186) (Nicholas C. Zakas)
* Breaking: remove duplicate warnings of `no-undef` from `block-scoped-var` (fixes #3201) (Toru Nagashima)
* Fix: `init-declarations` ignores in for-in/of (fixes #3202) (Toru Nagashima)
* Fix: `quotes` with `"backtick"` ignores ModuleSpecifier and LiteralPropertyName (fixes #3181) (Toru Nagashima)
* Fix: space-in-parens in Template Strings (fixes #3182) (Ian VanSchooten)
* Fix: Check for concatenation in no-throw-literal (fixes #3099, fixes #3101) (Ian VanSchooten)
* Build: Remove `eslint-tester` from devDependencies (fixes #3189) (Gyandeep Singh)
* Fix: Use new ESLintTester (fixes #3187) (Nicholas C. Zakas)
* Update: `new-cap` supports fullnames (fixes #2584) (Toru Nagashima)
* Fix: Non object rule options merge (fixes #3179) (Gyandeep Singh)
* New: add id-match rule (fixes #2829) (Matthieu Larcher)
* Fix: Rule options merge (fixes #3175) (Gyandeep Singh)
* Fix: `spaced-comment` allows a mix of markers and exceptions (fixes #2895) (Toru Nagashima)
* Fix: `block-scoped-var` issues (fixes #2253, fixes #2747, fixes #2967) (Toru Nagashima)
* New: Add id-length rule (fixes #2784) (Burak Yigit Kaya)
* Update: New parameters for quote-props rule (fixes #1283, fixes #1658) (Tomasz Olędzki)

v1.0.0-rc-3 - July 24, 2015

* Fix: Make Chai and Mocha as a dependency (fixes #3156) (Gyandeep Singh)
* Fix: traverse `ExperimentalSpread/RestProperty.argument` (fixes #3157) (Toru Nagashima)
* Fix: Check shareable config package prefix correctly (fixes #3146) (Gyandeep Singh)
* Update: move redeclaration checking for builtins (fixes #3070) (Toru Nagashima)
* Fix: `quotes` with `"backtick"` allows directive prologues (fixes #3132) (Toru Nagashima)
* Fix: `ESLintTester` path in exposed API (fixes #3149) (Gyandeep Singh)
* Docs: Remove AppVeyor badge (Gyandeep Singh)
* Fix: Check no-new-func on CallExpressions (fixes #3145) (Benjamin Woodruff)

v1.0.0-rc-2 - July 23, 2015

* Docs: Mention eslint-tester in migration guide (Nicholas C. Zakas)
* Docs: Mention variables defined in a global comment (fixes #3137) (William Becker)
* Docs: add documentation about custom-formatters. (fixes #1260) (royriojas)
* Fix: Multi-line variable declarations indent  (fixes #3139) (Gyandeep Singh)
* Fix: handles blocks in no-use-before-define (fixes #2960) (Jose Roberto Vidal)
* Update: `props` option of `no-param-reassign` (fixes #1600) (Toru Nagashima)
* New: Support shared configs named `@scope/eslint-config`, with shortcuts of `@scope` and `@scope/` (fixes #3123) (Jordan Harband)
* New: Add ignorePattern, ignoreComments, and ignoreUrls options to max-len (fixes #2934, fixes #2221, fixes #1661) (Benjamin Woodruff)
* Build: Increase Windows Mocha timeout (fixes #3133) (Ian VanSchooten)
* Docs: incorrect syntax in the example for rule «one-var» (Alexander Burtsev)
* Build: Check commit message format at end of tests (fixes #3058) (Ian VanSchooten)
* Update: Move eslint-tester into repo (fixes #3110) (Nicholas C. Zakas)
* Fix: Not load configs outside config with `root: true`  (fixes #3109) (Gyandeep Singh)
* Docs: Add config information to README (fixes #3074) (Nicholas C. Zakas)
* Docs: Add mysticatea as committer (Nicholas C. Zakas)
* Docs: Grammar fixes in rule descriptions (refs #3038) (Greg Cochard)
* Fix: Update sort-vars to ignore Array and ObjectPattern (fixes #2954) (Harry Ho)
* Fix: block-scoped-var rule incorrectly flagging break/continue with label (fixes #3082) (Aparajita Fishman)
* Fix: spaces trigger wrong in `no-useless-call` and `prefer-spread` (fixes #3054) (Toru Nagashima)
* Fix: `arrow-spacing` allow multi-spaces and line-endings (fixes #3079) (Toru Nagashima)
* Fix: add missing loop scopes to one-var (fixes #3073) (Jose Roberto Vidal)
* New: the `no-invalid-this` rule (fixes #2815) (Toru Nagashima)
* Fix: allow empty loop body in no-extra-semi (fixes #3075) (Mathias Schreck)
* Update: Add qunit to environments (fixes #2870) (Nicholas C. Zakas)
* Fix: `space-before-blocks` to consider classes (fixes #3062) (Gyandeep Singh)
* Fix: Include phantomjs globals (fixes #3064) (Linus Unnebäck)
* Fix: no-else-return handles multiple else-if blocks (fixes #3015) (Jose Roberto Vidal)
* Fix: `no-*-assgin` rules support destructuring (fixes #3029) (Toru Nagashima)
* New: the `no-implicit-coercion` rule (fixes #1621) (Toru Nagashima)
* Fix: Make no-implied-eval match more types of strings (fixes #2898) (Benjamin Woodruff)
* Docs: Clarify that bot message is automatic (Ian VanSchooten)
* Fix: Skip rest properties in no-dupe-keys (fixes 3042) (Nicholas C. Zakas)
* Docs: New issue template (fixes #3048) (Nicholas C. Zakas)
* Fix: strict rule supports classes (fixes #2977) (Toru Nagashima)
* New: the `prefer-reflect` rule (fixes #2939) (Keith Cirkel)
* Docs: make grammar consistent in rules index (Greg Cochard)
* Docs: Fix unmatched paren in rule description (Greg Cochard)
* Docs: Small typo fix in no-useless-call documentation (Paul O’Shannessy)
* Build: readd phantomjs dependency with locked down version (fixes #3026) (Mathias Schreck)
* Docs: Add IanVS as committer (Nicholas C. Zakas)
* docs: additional computed-property-spacing documentation (fixes #2941) (Jamund Ferguson)
* Docs: Add let and const examples for newline-after-var (fixes #3020) (James Whitney)
* Build: Remove unnecessary phantomjs devDependency (fixes #3021) (Gyandeep Singh)
* Update: added shared builtins list (fixes #2972) (Jose Roberto Vidal)

v1.0.0-rc-1 - July 15, 2015

* Upgrade: Espree to 2.2.0 (fixes #3011) (Nicholas C. Zakas)
* Docs: fix a typo (bartmichu)
* Fix: indent rule should recognize single line statements with ASI (fixes #3001, fixes #3000) (Mathias Schreck)
* Update: Handle CRLF line endings in spaced-comment rule - 2 (fixes #3005) (Burak Yigit Kaya)
* Fix: Indent rule error on empty block body (fixes #2999) (Gyandeep Singh)
* New: the `no-class-assign` rule (fixes #2718) (Toru Nagashima)
* New: the `no-const-assign` rule (fixes #2719) (Toru Nagashima)
* Docs: Add 1.0.0 migration guide (fixes #2994) (Nicholas C. Zakas)
* Docs: Update changelog for 0.24.1 (fixes #2976) (Nicholas C. Zakas)
* Breaking: Remove deprecated rules (fixes #1898) (Ian VanSchooten)
* Fix: multi-line + fat arrow indent (fixes #2239) (Gyandeep Singh)
* Breaking: Create eslint:recommended and add to --init (fixes #2713) (Greg Cochard)
* Fix: Indent rule (fixes #1797, fixes #1799, fixes #2248, fixes #2343, fixes #2278, fixes #1800) (Gyandeep Singh)
* New: `context.getDeclaredVariables(node)` (fixes #2801) (Toru Nagashima)
* New: the `no-useless-call` rule (fixes #1925) (Toru Nagashima)
* New: the `prefer-spread` rule (fixes #2946) (Toru Nagashima)
* Fix: `valid-jsdoc` counts `return` for arrow expressions (fixes #2952) (Toru Nagashima)
* New: Add exported comment option (fixes #1200) (Jamund Ferguson)
* Breaking: Default to --reset behavior (fixes #2100) (Brandon Mills)
* New: Add arrow-parens and arrow-spacing rule (fixes #2628) (Jxck)
* Fix: Shallow cloning issues in eslint config (fixes #2961) (Gyandeep Singh)
* Add: Warn on missing rule definition or deprecation (fixes #1549) (Ian VanSchooten)
* Update: adding some tests for no-redeclare to test named functions (fixes #2953) (Dominic Barnes)
* New: Add support for root: true in config files (fixes #2736) (Ian VanSchooten)
* Fix: workaround for leading and trailing comments in padded-block (fixes #2336 and fixes #2788) (Mathias Schreck)
* Fix: object-shorthand computed props (fixes #2937) (Jamund Ferguson)
* Fix: Remove invalid check inside `getJSDocComment` function (fixes #2938) (Gyandeep Singh)
* Docs: Clarify when not to use space-before-blocks (Ian VanSchooten)
* Update: `no-loop-func` allows block-scoped variables (fixes #2517) (Toru Nagashima)
* Docs: remove mistaken "off by default" (Jan Schär)
* Build: Add appveyor CI system (fixes #2923) (Gyandeep Singh)
* Docs: Fix typo in the shareable configs doc (Siddharth Kannan)
* Fix: max-len to report correct column number (fixes #2926) (Mathias Schreck)
* Fix: add destructuring support to comma-dangle rule (fixes #2911) (Mathias Schreck)
* Docs: clarification in no-unused-vars (Jan Schär)
* Fix: `no-redeclare` checks module scopes (fixes #2903) (Toru Nagashima)
* Docs: missing quotes in JSON (Jan Schär)
* Breaking: Switch to 1-based columns (fixes #2284) (Nicholas C. Zakas)
* Docs: array-bracket-spacing examples used space-in-brackets (Brandon Mills)
* Docs: Add spaced-line-comment deprecation notice (Brandon Mills)
* Docs: Add space-in-brackets deprecation notice (Brandon Mills)
* Fix: Include execScript in no-implied-eval rule (fixes #2873) (Frederik Braun)
* Fix: Support class syntax for line-around-comment rule (fixes #2894) (Gyandeep Singh)
* Fix: lines-around-comment was crashing in some cases due to a missing check (fixes #2892) (Mathieu M-Gosselin)
* New: Add init-declarations rule (fixes #2606) (cjihrig)
* Docs: Fix typo in array-bracket-spacing rule (zallek)
* Fix: Added missing export syntax support to the block-scoped-var rule. (fixes #2887) (Mathieu M-Gosselin)
* Build: gensite target supports rule removal (refs #1898) (Brandon Mills)
* Update: Handle CRLF line endings in spaced-comment rule (fixes #2884) (David Anson)
* Update: Attach parent in getNodeByRangeIndex (fixes #2863) (Brandon Mills)
* Docs: Fix typo (Bryan Smith)
* New: Add serviceworker environment (fixes #2557) (Gyandeep Singh)
* Fix: Yoda should ignore comparisons where both sides are constants (fixes #2867) (cjihrig)
* Update: Loosens regex rules around intentional fall through comments (Fixes #2811) (greg5green)
* Update: Add missing schema to rules (fixes #2858) (Ilya Volodin)
* New: `require-yield` rule (fixes #2822) (Toru Nagashima)
* New: add callback-return rule (fixes #994) (Jamund Ferguson)

v0.24.1 - July 10, 2015

* Docs: Clarify when not to use space-before-blocks (Ian VanSchooten)
* Docs: remove mistaken "off by default" (Jan Schär)
* Docs: remove mistaken "off by default" (Jan Schär)
* Docs: Fix typo in the shareable configs doc (Siddharth Kannan)
* Docs: clarification in no-unused-vars (Jan Schär)
* Docs: missing quotes in JSON (Jan Schär)
* Fix: Revert 1-based column changes in tests for patch (refs #2284) (Nicholas C. Zakas)
* Fix: Shallow cloning issues in eslint config (fixes #2961) (Gyandeep Singh)
* Fix: object-shorthand computed props (fixes #2937) (Jamund Ferguson)
* Fix: Remove invalid check inside `getJSDocComment` function (fixes #2938) (Gyandeep Singh)
* Fix: max-len to report correct column number (fixes #2926) (Mathias Schreck)
* Fix: add destructuring support to comma-dangle rule (fixes #2911) (Mathias Schreck)
* Fix: `no-redeclare` checks module scopes (fixes #2903) (Toru Nagashima)
* Fix: Include execScript in no-implied-eval rule (fixes #2873) (Frederik Braun)
* Fix: Support class syntax for line-around-comment rule (fixes #2894) (Gyandeep Singh)
* Fix: lines-around-comment was crashing in some cases due to a missing check (fixes #2892) (Mathieu M-Gosselin)
* Fix: Added missing export syntax support to the block-scoped-var rule. (fixes #2887) (Mathieu M-Gosselin)
* Fix: Yoda should ignore comparisons where both sides are constants (fixes #2867) (cjihrig)
* Docs: array-bracket-spacing examples used space-in-brackets (Brandon Mills)
* Docs: Add spaced-line-comment deprecation notice (Brandon Mills)
* Docs: Add space-in-brackets deprecation notice (Brandon Mills)

v0.24.0 - June 26, 2015

* Upgrade: eslint-tester to 0.8.1 (Nicholas C. Zakas)
* Fix: no-dupe-args sparse array crash (fixes #2848) (Chris Walker)
* Fix: space-after-keywords should ignore extra parens (fixes #2847) (Mathias Schreck)
* New: add no-unexpected-multiline rule (fixes #746) (Glen Mailer)
* Update: refactor handle-callback-err to improve performance (fixes #2841) (Mathias Schreck)
* Fix: Add --init to the CLI options (fixes #2817) (Gyandeep Singh)
* Update: Add `except-parens` option to `no-return-assign` rule (fixes #2809) (Toru Nagashima)
* Fix: handle-callback-err missing arrow functions (fixes #2823) (Jamund Ferguson)
* Fix: `no-extra-semi` in class bodies (fixes #2794) (Toru Nagashima)
* Fix: Check type to be file when looking for config files (fixes #2790) (Gyandeep Singh)
* Fix: valid-jsdoc to work for object getters (fixes #2407) (Gyandeep Singh)
* Update: Add an option as an object to `generator-star-spacing` rule (fixes #2787) (Toru Nagashima)
* Build: Update markdownlint dependency (David Anson)
* Fix: context report message to handle more scenarios (fixes #2746) (Gyandeep Singh)
* Update: Ignore JsDoc comments by default for `spaced-comment` (fixes #2766) (Gyandeep Singh)
* Fix: one-var 'never' option for mixed initialization (Fixes #2786) (Ian VanSchooten)
* Docs: Fix a minor typo in a prefer-const example (jviide)
* Fix: comma-dangle always-multiline: no comma right before the last brace (fixes #2091) (Benoît Zugmeyer)
* Fix: Allow blocked comments with markers and new-line (fixes #2777) (Gyandeep Singh)
* Docs: small fix in quote-props examples (Jose Roberto Vidal)
* Fix: object-shorthand rule should not warn for NFEs (fixes #2748) (Michael Ficarra)
* Fix: arraysInObjects for object-curly-spacing (fixes #2752) (Jamund Ferguson)
* Docs: Clarify --rule description (fixes #2773) (Nicholas C. Zakas)
* Fix: object literals in arrow function bodies (fixes #2702) (Jose Roberto Vidal)
* New: `constructor-super` rule (fixes #2720) (Toru Nagashima)
* New: `no-this-before-super` rule (fixes #2721) (Toru Nagashima)
* Fix: space-unary-ops flags expressions starting w/ keyword (fixes #2764) (Michael Ficarra)
* Update: Add block options to `lines-around-comment` rule (fixes #2667) (Gyandeep Singh)
* New: array-bracket-spacing (fixes #2226) (Jamund Ferguson)
* Fix: No-shadow rule duplicating error messages (fixes #2706) (Aliaksei Shytkin)

v0.23.0 - June 14, 2015

* Build: Comment out auto publishing of release notes (refs #2640) (Ilya Volodin)
* Fix: "extends" within package.json (fixes #2754) (Gyandeep Singh)
* Upgrade: globals@8.0.0 (fixes #2759) (silverwind)
* Docs: eol-last docs fix (fixes #2755) (Gyandeep Singh)
* Docs: btmills is a reviewer (Nicholas C. Zakas)
* Build: Revert lock io.js to v2.1.0 (refs #2745) (Brandon Mills)
* New: computed-property-spacing (refs #2226) (Jamund Ferguson)
* Build: Pin Sinon version (fixes #2742) (Ilya Volodin)
* Fix: `prefer-const` treats `for-in`/`for-of` with the same way (Fixes #2739) (Toru Nagashima)
* Docs: Add links to team members profile (Gyandeep Singh)
* Docs: add team and ES7 info to readme (Nicholas C. Zakas)
* Fix: don't try to strip "line:" prefix from parser errors with no such prefix (fixes #2698) (Tim Cuthbertson)
* Fix: never ignore config comment options (fixes #2725) (Brandon Mills)
* Update: Add clarification to spaced-comment (refs #2588) (Greg Cochard)
* Update: Add markers to spaced-comment (fixes #2588) (Greg Cochard)
* Fix: no-trailing-spaces now handles skipBlankLines (fixes #2575) (Greg Cochard)
* Docs: Mark global-strict on by default (fixes #2629) (Ilya Volodin)
* New: Allow extends to be an array (fixes #2699) (Justin Morris)
* New: globals@7.1.0 (fixes #2682) (silverwind)
* New: `prefer-const` rule (fixes #2333) (Toru Nagashima)
* Fix: remove hard-coded list of unary keywords in space-unary-ops rule (fixes #2696) (Tim Cuthbertson)
* Breaking: Automatically validate rule options (fixes #2595) (Brandon Mills)
* Update: no-lone-blocks does not report block-level scopes (fixes  #2119) (Jose Roberto Vidal)
* Update: yoda onlyEquality option (fixes #2638) (Denis Sokolov)
* Docs: update comment to align with source code it's referencing (Michael Ficarra)
* Fix: Misconfigured default option for lines-around-comment rule (fixes #2677) (Gyandeep Singh)
* Fix: `no-shadow` allows shadowing in the TDZ (fixes #2568) (Toru Nagashima)
* New: spaced-comment rule (fixes #1088) (Gyandeep Singh)
* Fix: Check unused vars in exported functions (fixes #2678) (Gyandeep Singh)
* Build: Stringify payload of release notes (fixes #2640) (Greg Cochard)
* Fix: Allowing u flag in regex to properly lint no-empty-character-class (fixes #2679) (Dominic Barnes)
* Docs: deprecate no-wrap-func (fixes #2644) (Jose Roberto Vidal)
* Docs: Fixing grammar: then -> than (E)
* Fix: trailing commas in object-curly-spacing (fixes #2647) (Jamund Ferguson)
* Docs: be consistent about deprecation status (Matthew Dapena-Tretter)
* Docs: Fix mistakes in object-curly-spacing docs (Matthew Dapena-Tretter)
* New: run processors when calling executeOnText (fixes #2331) (Mordy Tikotzky)
* Update: move executeOnText() tests to the correct describe block (fixes #2648) (Mordy Tikotzky)
* Update: add tests to assert that the preprocessor is running (fixes #2651) (Mordy Tikotzky)
* Build: Lock io.js to v2.1.0 (fixes #2653) (Ilya Volodin)

v0.22.1 - May 30, 2015

* Build: Remove release notes auto-publish (refs #2640) (Ilya Volodin)

v0.22.0 - May 30, 2015

* Upgrade: escope 3.1.0 (fixes #2310, #2405) (Toru Nagashima)
* Fix: “consistent-this” incorrectly flagging destructuring of `this` (fixes #2633) (David Aurelio)
* Upgrade: eslint-tester to 0.7.0 (Ilya Volodin)
* Update: allow shadowed references in no-alert (fixes #1105) (Mathias Schreck)
* Fix: no-multiple-empty-lines and template strings (fixes #2605) (Jamund Ferguson)
* New: object-curly-spacing (fixes #2225) (Jamund Ferguson)
* Docs: minor fix for one-var rule (Jamund Ferguson)
* Fix: Shared config being clobbered by other config (fixes #2592) (Dominic Barnes)
* Update: adds "functions" option to no-extra-parens (fixes #2477) (Jose Roberto Vidal)
* Docs: Fix json formatting for lines-around-comments rule (Gyandeep Singh)
* Fix: Improve around function/class names of `no-shadow` (fixes #2556, #2552) (Toru Nagashima)
* Fix: Improve code coverage (fixes #2590) (Ilya Volodin)
* Fix: Allow scoped configs to have sub-configs (fixes #2594) (Greg Cochard)
* Build: Add auto-update of release tag on github (fixes #2566) (Greg Cochard)
* New: lines-around-comment (fixes #1344) (Jamund Ferguson)
* Build: Unblock build by increasing code coverage (Ilya Volodin)
* New: accessor-pairs rule to object initializations (fixes #1638) (Gyandeep Singh)
* Fix: counting of variables statements in one-var (fixes #2570) (Mathias Schreck)
* Build: Add sudo:false for Travis (fixes #2582) (Ilya Volodin)
* New: Add rule schemas (refs #2179) (Brandon Mills)
* Docs: Fix typo in shareable-configs example (fixes #2571) (Ted Piotrowski)
* Build: Relax markdownlint rules by disabling style-only items (David Anson)
* Fix: Object shorthand rule incorrectly flagging getters/setters (fixes #2563) (Brad Dougherty)
* New: Add config validator (refs #2179) (Brandon Mills)
* New: Add worker environment (fixes #2442) (Ilya Volodin)
* New no-empty-character class (fixes #2508) (Jamund Ferguson)
* New: Adds --ignore-pattern option. (fixes #1742) (Patrick McElhaney)

v0.21.2 - May 18, 2015

* 0.21.2 (Nicholas C. Zakas)
* Fix: one-var exception for ForStatement.init (fixes #2505) (Brandon Mills)
* Fix: Don't throw spurious shadow errors for classes (fixes #2545) (Jimmy Jia)
* Fix: valid-jsdoc rule to support exported functions (fixes #2522) (Gyandeep Singh)
* Fix: Allow scoped packages in configuration extends (fixes #2544) (Eric Isakson)
* Docs: Add chatroom to FAQ (Nicholas C. Zakas)
* Docs: Move Gitter badge (Nicholas C. Zakas)

v0.21.1 - May 15, 2015

* 0.21.1 (Nicholas C. Zakas)
* Fix: loc obj in report fn expects column (fixes #2481) (Varun Verma)
* Build: Make sure that all md files end with empty line (fixes #2520) (Ilya Volodin)
* Added Gitter badge (The Gitter Badger)
* Fix: forced no-shadow to check all scopes (fixes #2294) (Jose Roberto Vidal)
* Fix: --init indent setting (fixes #2493) (Nicholas C. Zakas)
* Docs: Mention bundling multiple shareable configs (Nicholas C. Zakas)
* Fix: Not to override the required extended config object directly (fixes #2487) (Gyandeep Singh)
* Build: Update markdownlint dependency (David Anson)
* Docs: added recursive function example to no-unused-vars (Jose Roberto Vidal)
* Docs: Fix typo (then -> than) (Vladimir Agafonkin)
* Revert "Fix: sanitise Jekyll interpolation during site generation (fixes #2297)" (Nicholas C. Zakas)
* Fix: dot-location should use correct dot token (fixes #2504) (Mathias Schreck)
* Fix: Stop linebreak-style from crashing (fixes #2490) (James Whitney)
* Fix: rule no-duplicate-case problem with CallExpressions. (fixes #2499) (Matthias Osswald)
* Fix: Enable full support for eslint-env comments (refs #2134) (Ilya Volodin)
* Build: Speed up site generation (fixes #2475) (Ilya Volodin)
* Docs: Fixing trailing spaces (Fixes #2478) (Ilya Volodin)
* Docs: Update README FAQs (Nicholas C. Zakas)
* Fix: Allow comment before comma for comma-spacing rule (fixes #2408) (Gyandeep Singh)

v0.21.0 - May 9, 2015

* 0.21.0 (Nicholas C. Zakas)
* New: Shareable configs (fixes #2415) (Nicholas C. Zakas)
* Fix: Edge cases for no-wrap-func (fixes #2466) (Nicholas C. Zakas)
* Docs: Update ecmaFeatures description (Nicholas C. Zakas)
* New: Add dot-location rule. (fixes #1884) (Greg Cochard)
* New: Add addPlugin method to CLI-engine (Fixes #1971) (Ilya Volodin)
* Breaking: Do not check unset declaration types (Fixes #2448) (Ilya Volodin)
* Fix: no-redeclare switch scoping (fixes #2337) (Nicholas C. Zakas)
* Fix: Check extra scope in no-use-before-define (fixes #2372) (Nicholas C. Zakas)
* Fix: Ensure baseConfig isn't changed (fixes #2380) (Nicholas C. Zakas)
* Fix: Don't warn for member expression functions (fixes #2402) (Nicholas C. Zakas)
* New: Adds skipBlankLines option to the no-trailing-spaces rule (fixes #2303) (Andrew Vaughan)
* Fix: Adding exception for last line (Refs #2423) (Greg Cochard)
* Fix: crash on 0 max (fixes #2423) (gcochard)
* Fix object-shorthand arrow functions (fixes #2414) (Jamund Ferguson)
* Fix: Improves detection of self-referential functions (fixes #2363) (Jose Roberto Vidal)
* Update: key-spacing groups must be consecutive lines (fixes #1728) (Brandon Mills)
* Docs: grammar fix in no-sync (Tony Lukasavage)
* Docs: Update configuring.md to fix incorrect link. (Ans)
* New: Check --stdin-filename by ignore settings (fixes #2432) (Aliaksei Shytkin)
* Fix: `no-loop-func` rule allows functions at init part (fixes #2427) (Toru Nagashima)
* New: Add init command (fixes #2302) (Ilya Volodin)
* Fix: no-irregular-whitespace should work with irregular line breaks (fixes #2316) (Mathias Schreck)
* Fix: generator-star-spacing with class methods (fixes #2351) (Brandon Mills)
* New: no-unneeded-ternary rule to disallow boolean literals in conditional expressions (fixes #2391) (Gyandeep Singh)
* Docs: Add `restParams` to `ecmaFeatures` options list (refs: #2346) (Bogdan Savluk)
* Fix: space-in-brackets Cannot read property 'range' (fixes #2392) (Gyandeep Singh)
* Docs: Sort the rules (Lukas Böcker)
* Add: Exception option for `no-extend-native` and `no-native-reassign` (fixes #2355) (Gyandeep Singh)
* Fix: space-in-brackets import declaration  (fixes #2378) (Gyandeep Singh)
* Update: Add uninitialized and initialized options (fixes #2206) (Ian VanSchooten)
* Fix: brace-style to not warn about curly mix ifStatements (fixes #1739) (Gyandeep Singh)
* Fix: npm run profile script should use espree (fixes #2150) (Mathias Schreck)
* New: Add support for extending configurations (fixes #1637) (Espen Hovlandsdal)
* Fix: Include string literal keys in object-shorthand (Fixes #2374) (Jamund Ferguson)
* Docs: Specify language for all code fences, enable corresponding markdownlint rule. (David Anson)
* New: linebreak-style rule (fixes #1255) (Erik Müller)
* Update: Add "none" option to operator-linebreak rule (fixes #2295) (Casey Visco)
* Fix: sanitise Jekyll interpolation during site generation (fixes #2297) (Michael Ficarra)

v0.20.0 - April 24, 2015

* 0.20.0 (Nicholas C. Zakas)
* Fix: support arrow functions in no-extra-parens (fixes #2367) (Michael Ficarra)
* Fix: Column position in space-infix-ops rule (fixes #2354) (Gyandeep Singh)
* Fix: allow plugins to be namespaced (fixes #2360) (Seth Pollack)
* Update: one-var: enable let & const (fixes #2301) (Joey Baker)
* Docs: Add meteor to avaiable environments list (bartmichu)
* Update: Use `Object.assign()` polyfill for all object merging (fixes #2348) (Sindre Sorhus)
* Docs: Update markdownlint dependency, resolve/suppress new issues. (David Anson)
* Fix: newline-after-var declare and export (fixes #2325) (Gyandeep Singh)
* Docs: Some typos and grammar. (AlexKVal)
* Fix: newline-after-var to ignore declare in for specifiers (fixes #2317) (Gyandeep Singh)
* New: add --stdin-filename option (fixes #1950) (Mordy Tikotzky)
* Fix: Load .eslintrc in $HOME only if no other .eslintrc is found (fixes #2279) (Jasper Woudenberg)
* Fix: Add `v8` module to no-mixed-requires rule (fixes #2320) (Gyandeep Singh)
* Fix: key-spacing with single properties (fixes #2311) (Brandon Mills)
* Docs: `no-invalid-regexp`: add `ecmaFeatures` flags for `u`/`y` (Jordan Harband)
* New: object-shorthand rule (refs: #1617) (Jamund Ferguson)
* Update: backticks support for quotes rule (fixes #2153) (borislavjivkov)
* Fix: space-in-brackets to work with modules (fixes #2216) (Nicholas C. Zakas)

v0.19.0 - April 11, 2015

* 0.19.0 (Nicholas C. Zakas)
* Upgrade: Espree to 2.0.1 (Nicholas C. Zakas)
* Docs: Update one-var documentation (fixes #2210) (Nicholas C. Zakas)
* Update: Add test for no-undef (fixes #2214) (Nicholas C. Zakas)
* Fix: Report better location for padded-blocks error (fixes #2224) (Nicholas C. Zakas)
* Fix: Don't check concise methods in quote-props (fixes #2251) (Nicholas C. Zakas)
* Fix: Consider tabs for space-in-parens rule (fixes #2191) (Josh Quintana)
* Fix: block-scoped-var to work with classes (fixes #2280) (Nicholas C. Zakas)
* Docs: Remove trailing spaces, enable corresponding markdownlint rule. (David Anson)
* Fix: padded-blocks with ASI (fixes #2273) (Brandon Mills)
* Fix: Handle comment lines in newline-after-var (fixed #2237) (Casey Visco)
* Docs: Standardize on '*' for unordered lists, enable corresponding markdownlint rule. (David Anson)
* Fix: no-undef and no-underscore-dangle to use double quotes (fixes #2258) (Gyandeep Singh)
* Docs: Improve grammar and style in comma-dangle.md (Nate Eagleson)
* Docs: Improve grammar and style in padded-blocks.md (Nate Eagleson)
* Docs: Update URL in no-wrap-func.md to resolve 404 (Nate Eagleson)
* Docs: Fix typo in command-line-interface.md (Nate Eagleson)
* Docs: Fix typo in working-with-rules.md (Nate Eagleson)
* Docs: Remove hard tabs from *.md, enable corresponding markdownlint rule. (David Anson)
* Fix: Function id missing in parent scope when using ecmaFeature `modules` for rule block-scoped-var (fixes #2242) (Michael Ferris)
* Fix: Ignore single lines for vertical alignment (fixes #2018) (Ian VanSchooten)
* Fix: Allow inline comments in newline-after-var rule (fixes #2229) (Casey Visco)
* Upgrade: Espree 2.0.0 and escope 3.0.0 (fixes #2234, fixes #2201, fixes (Nicholas C. Zakas)
* Docs: Update --no-ignore warning (Brandon Mills)
* Build: Remove jshint files (fixes #2222) (Jeff Tan)
* Docs: no-empty fix comment change (refs #2188) (Gyandeep Singh)
* Fix: duplicate semi and no-extra-semi errors (fixes #2207) (Brandon Mills)
* Docs: Update processors description (Nicholas C. Zakas)
* Fix: semi error on export declaration (fixes #2194) (Brandon Mills)
* New: operator-linebreak rule (fixes #1405) (Benoît Zugmeyer)
* Docs: Fixing broken links in documentation (Ilya Volodin)
* Upgrade: Espree to 0.12.3 (fixes #2195) (Gyandeep Singh)
* Fix: camelcase rule with {properties: never} shouldn't check assignment (fixes #2189) (Gyandeep Singh)
* New: Allow modifying base config (fixes #2143) (Meo)
* New: no-continue rule (fixes #1945) (borislavjivkov)
* Fix: `no-empty` rule should allow any comments (fixes #2188) (Gyandeep Singh)
* Docs: Fix spell in camelcase doc (fixes #2190) (Gyandeep Singh)
* Fix: Require semicolon after import/export statements (fixes #2174) (Gyandeep Singh)
* Build: Add linting of Markdown files to "npm test" script (fixes #2182) (David Anson)
* Build: Fixing site generation (Ilya Volodin)
* Build: Fix gensite task to work even if files are missing (Nicholas C. Zakas)

v0.18.0 - March 28, 2015

* 0.18.0 (Nicholas C. Zakas)
* Fix: Mark variables as used in module scope (fixes #2137) (Nicholas C. Zakas)
* Fix: arrow functions need wrapping (fixes #2113) (Nicholas C. Zakas)
* Fix: Don't crash on empty array pattern item (fixes #2111) (Nicholas C. Zakas)
* Fix: Don't error on destructured params (fixes #2051) (Nicholas C. Zakas)
* Docs: Fixing broken links (Ilya Volodin)
* Fix: no-constant-condition should not flag += (fixes #2155) (Nicholas C. Zakas)
* Fix: Ensure piped in code will trigger correct errors (fixes #2154) (Nicholas C. Zakas)
* Fix: block-scoped-var to handle imports (fixes #2087) (Nicholas C. Zakas)
* Fix: no-dupe-args to work with destructuring (fixes #2148) (Nicholas C. Zakas)
* Fix: key-spacing crash on computed properties (fixes #2120) (Brandon Mills)
* Fix: indent crash on caseless switch (fixes #2144) (Brandon Mills)
* Fix: Don't warn about destructured catch params (fixes #2125) (Nicholas C. Zakas)
* Update: Omit setter param from no-unused-vars (fixes #2133) (Nicholas C. Zakas)
* Docs: Cleaning dead links (Ilya Volodin)
* Docs: Moving documentation out of the repository and modifying build scripts (Ilya Volodin)
* Docs: Update link to Documentation (Kate Lizogubova)
* Docs: Adding back deprecated space-unary-word-ops documentation (Ilya Volodin)
* Fix: Unused recursive functions should be flagged (issue2095) (Nicholas C. Zakas)
* Breaking: Remove JSX support from no-undef (fixes #2093) (Nicholas C. Zakas)
* Fix: markVariableAsUsed() should work in Node.js env (fixes #2089) (Nicholas C. Zakas)
* New: Add "always" and "never" options to "one-var" rule. (fixes #1619) (Danny Fritz)
* New: newline-after-var rule (fixes #2057) (Gopal Venkatesan)
* Fix: func-names with ES6 classes (fixes #2103) (Marsup)
* Fix: Add "Error" to the "new-cap" rule exceptions (fixes #2098) (Mickaël Tricot)
* Fix: vars-on-top conflict with ES6 import (fixes #2099) (Gyandeep Singh)
* Docs: Fixed JSON syntax (Sajin)
* New: space-before-function-paren rule (fixes #2028) (Brandon Mills)
* Breaking: rule no-empty also checking for empty catch blocks. (fixes #1841) (Dieter Oberkofler)
* Update: rule camelcase to allow snake_case in object literals. (fixes #1919) (Dieter Oberkofler)
* New: Added option int32Hint for space-infix-ops (fixes #1295) (Kirill Efimov)
* New: no-param-reassign rule (fixes #1599) (Nat Burns)

v0.17.1 - March 17, 2015

* 0.17.1 (Nicholas C. Zakas)
* Fix: no-func-assign should not fail on import declarations (fixes #2060) (Igor Zalutsky)
* Fix: block-scoped-var to work with destructuring (fixes #2059) (Nicholas C. Zakas)
* Fix: no-redeclare should check Node.js scope (fixes #2064) (Nicholas C. Zakas)
* Fix: space-before-function-parentheses generator methods (fixes #2082) (Brandon Mills)
* Fix: Method name resolution in complexity rule (fixes #2049) (Nicholas C. Zakas)
* Fix: no-unused-vars crash from escope workaround (fixes #2042) (Brandon Mills)
* Fix: restrict dot-notation keywords to actual ES3 keywords (fixes #2075) (Michael Ficarra)
* Fix: block-scoped-var to work with classes (fixes #2048) (Nicholas C. Zakas)
* Docs: Update no-new documentation (fixes #2044) (Nicholas C. Zakas)
* Fix: yoda range exceptions with this (fixes #2063) (Brandon Mills)
* Docs: Fix documentation on configuring eslint with comments (Miguel Ping)
* Fix: rule no-duplicate-case problem with MemberExpressions. (fixes #2038) (Dieter Oberkofler)
* Fix: Exempt \0 from no-octal-escape (fixes #1923) (Michael Ficarra)

v0.17.0 - March 14, 2015

* 0.17.0 (Nicholas C. Zakas)
* Fix: module import specifiers should be defined (refs #1978) (Nicholas C. Zakas)
* Fix: Ignore super in no-undef (refs #1968) (Nicholas C. Zakas)
* Upgrade: Espree to v0.12.0 (refs #1968) (Nicholas C. Zakas)
* Fix: destructured arguments should work in block-scoped-var (fixes #1996) (Nicholas C. Zakas)
* Fix: Line breaking with just carriage return (fixes #2005) (Nicholas C. Zakas)
* Fix: location of new-cap error messages (fixes #2025) (Mathias Schreck)
* Breaking: Stop checking JSX variable use, expose API instead (fixes #1911) (Glen Mailer)
* Fix: Check spacing of class methods (fixes #1989) (Nicholas C. Zakas)
* New: no-duplicate-case rule to disallow a duplicate case label (fixes #2015) (Dieter Oberkofler)
* Clarify issue requirement for doc pull requests (Ian)
* Add quotes around object key (Ian)
* Fix: Add comma-dangle allow-multiline (fixes #1984) (Keith Cirkel)
* Fix: Don't explode on default export function (fixes #1985) (Nicholas C. Zakas)
* Update: Add AST node exceptions to comma-style. (fixes #1932) (Evan Simmons)
* Docs: Add spread operator to available language options (Nicholas C. Zakas)
* New: generator-star-spacing rule (fixes #1680, fixes #1949) (Brandon Mills)

v0.16.2 - March 10, 2015

* 0.16.2 (Nicholas C. Zakas)
* Fix: Ensure globalReturn isn't on when node:false (fixes #1995) (Nicholas C. Zakas)
* Downgrade: escope pegged to 2.0.6 (refs #2001) (Nicholas C. Zakas)
* Upgrade: escope to 2.0.7 (fixes #1978) (Nicholas C. Zakas)
* Docs: Update descriptive text for --no-ignore option. (David Anson)
* Upgrade: estraverse to latest for ESTree support (fixes #1986) (Nicholas C. Zakas)
* Fix: Global block-scope-var check should work (fixes #1980) (Nicholas C. Zakas)
* Fix: Don't warn about parens around yield (fixes #1981) (Nicholas C. Zakas)

v0.16.1 - March 8, 2015

* 0.16.1 (Nicholas C. Zakas)
* Fix: Node.js scoping in block-scoped-var (fixes #1969) (Nicholas C. Zakas)
* Update: Enable ES6 scoping for more options (Nicholas C. Zakas)
* Fix: Ensure all export nodes are traversable (fixes #1965) (Nicholas C. Zakas)
* Fix: Ensure class names are marked as used (fixes #1967) (Nicholas C. Zakas)
* Fix: remove typo that caused a crash (fixes #1963) (Fabricio C Zuardi)
* Docs: Added missing "are" (Sean Wilkinson)

v0.16.0 - March 7, 2015

* 0.16.0 (Nicholas C. Zakas)
* Fix: Pass correct sourceType to escope (fixes #1959) (Nicholas C. Zakas)
* Fix: Scoping for Node.js (fixes #892) (Nicholas C. Zakas)
* Fix: strict rule should honor module code (fixes #1956) (Nicholas C. Zakas)
* New: Add es6 environment (fixes #1864, fixes #1944) (Nicholas C. Zakas)
* Docs: Update ecmaFeatures list (fixes #1942) (Nicholas C. Zakas)
* Fix: Make no-unused-vars ignore exports (fixes #1903) (Nicholas C. Zakas)
* Upgrade: Espree to v1.11.0 (Nicholas C. Zakas)
* Fix: Comment configuration of rule doesn't work (fixes #1792) (Jary)
* Fix: Rest args should work in no-undef and block-scoped-var (fixes #1543) (Nicholas C. Zakas)
* Breaking: change no-comma-dangle to comma-dangle (fixes #1350) (Mathias Schreck)
* Update: space-before-function-parentheses to support generators (fixes #1929) (Brandon Mills)
* New: Adding support for "// eslint-disable-line rule" style comments (Billy Matthews)
* Fix: Use unversioned sinon file in browser test (fixes #1947) (Nicholas C. Zakas)
* Docs: Add mention of compatible parsers (Nicholas C. Zakas)
* Fix: Better error when given null as rule config (fixes #1760) (Glen Mailer)
* Update: no-empty to check TryStatement.handler (fixes #1930) (Brandon Mills)
* Fix: space-before-function-parentheses and object methods (fixes #1920) (Brandon Mills)
* New: no-dupe-args rule (fixes #1880) (Jamund Ferguson)
* Fix: comma-spacing should ignore JSX text (fixes #1916) (Brandon Mills)
* Breaking: made eol-last less strict (fixes #1460) (Glen Mailer)
* New: generator-star middle option (fixes #1808) (Jamund Ferguson)
* Upgrade: Espree to 1.10.0 for classes support (Nicholas C. Zakas)
* Docs: no-plusplus.md - auto semicolon insertion (Miroslav Obradović)
* Docs: Use union types in TokenStore JSDoc (refs #1878) (Brandon Mills)
* Fix: block-scoped-var to work with destructuring (fixes #1863) (Nicholas C. Zakas)
* Docs: Update docs for token-related methods (fixes #1878) (Nicholas C. Zakas)
* Update: Remove preferGlobal from package.json (fixes #1877) (Nicholas C. Zakas)
* Fix: allow block bindings in no-inner-declarations (fixes #1893) (Roberto Vidal)
* Fix: getScope and no-use-before-define for arrow functions (fixes #1895) (Brandon Mills)
* Fix: Make no-inner-declarations look for arrow functions (fixes #1892) (Brandon Mills)
* Breaking: Change no-space-before-semi to semi-spacing and add "after" option (fixes #1671) (Mathias Schreck)
* Update: Add support for custom preprocessors (fixes #1817) (Ilya Volodin)

v0.15.1 - February 26, 2015

* 0.15.1 (Nicholas C. Zakas)
* Build: Fix release task (Nicholas C. Zakas)
* Fix: check all semicolons in no-space-before-semi (fixes #1885) (Mathias Schreck)
* Fix: Refactor comma-spacing (fixes #1587, fixes #1845) (Roberto Vidal)
* Fix: Allow globalReturn in consistent-return (fixes #1868) (Brandon Mills)
* Fix: semi rule should check throw statements (fixes #1873) (Mathias Schreck)
* Docs: Added HolidayCheck AG as user (0xPIT)
* Upgrade: `chalk` to 1.0.0 (Sindre Sorhus)
* Docs: Add CustomInk to the list of companies (Derek Lindahl)
* Docs: Alphabetize project & company usage list (Derek Lindahl)
* Docs: fix typo (Henry Zhu)
* Docs: Fix typo (Brenard Cubacub)

v0.15.0 - February 21, 2015

* 0.15.0 (Nicholas C. Zakas)
* Upgrade: Espree to 1.9.1 (fixes #1816, fixes #1805) (Nicholas C. Zakas)
* Fix: make rules work with for-of statements (fixes #1859) (Mathias Schreck)
* Fix: Enable globalReturn for Node.js environment (fixes #1158) (Nicholas C. Zakas)
* Fix: Location of extra paren message (fixes #1814) (Nicholas C. Zakas)
* Fix: Remove unnecessary file exists check (fixes #1831) (Nicholas C. Zakas)
* Fix: Don't count else-if in max-depth (fixes #1835) (Nicholas C. Zakas)
* Fix: Don't flag for-of statement (fixes #1852) (Nicholas C. Zakas)
* Build: Test using io.js as well (Nicholas C. Zakas)
* Change customformat value to path (suisho)
* Docs: Add a missing word in the Contributing doc (Ben Linskey)
* Docs: Fix typo in wrap-iife rule doc title (Ben Linskey)
* Docs: Update pages to fix rendering of lists (David Anson)
* Fix: new-cap should allow defining exceptions (fixes #1424) (Brian Di Palma)
* Update: Add requireReturnDescription for valid-jsdoc (fixes #1833) (Brian Di Palma)
* New: rule no-throw-literal added (fixes #1791) (Dieter Oberkofler)
* New: multi-line option for the curly rule (fixes #1812) (Hugo Wood)
* Docs: fix typo in configuring docs (mendenhallmagic)
* Update: Backslashes in path (fixes #1818) (Jan Schär)
* Docs: Update pages to fix rendering of lists and fenced code blocks (David Anson)
* Docs: add webpack loader to the docs/integrations page (Maxime Thirouin)
* Breaking: space-before-function-parentheses replaces space-after-function-name and checkFunctionKeyword (fixes #1618) (Mathias Schreck)

v0.14.1 - February 8, 2015

* 0.14.1 (Nicholas C. Zakas)
* Fix: Exit code should be 1 for any number of errors (fixes #1795) (Nicholas C. Zakas)
* Fix: Check indentation of first line (fixes #1796) (Nicholas C. Zakas)
* Fix: strict rules shouldn't throw on arrow functions (fixes #1789) (Nicholas C. Zakas)

v0.14.0 - February 7, 2015

* 0.14.0 (Nicholas C. Zakas)
* Update: Fix indentation of comment (Nicholas C. Zakas)
* Fix: comma-spacing for template literals (fixes #1736) (Nicholas C. Zakas)
* Build: Add Node.js 0.12 testing (Nicholas C. Zakas)
* Breaking: Remove node from results (fixes #957) (Nicholas C. Zakas)
* Breaking: Exit code is now error count (Nicholas C. Zakas)
* Docs: Correct getFormatter() documentation (refs #1723) (Nicholas C. Zakas)
* Update: Make rules work with arrow functions (fixes #1508, fixes #1509, fixes #1493) (Nicholas C. Zakas)
* Fix: Ensure template string references count (fixes #1542) (Nicholas C. Zakas)
* Fix: no-undef to work with arrow functions (fixes #1604) (Nicholas C. Zakas)
* Upgrade: Espree to version 1.8.0 (Nicholas C. Zakas)
* Fix: Don't throw error for arguments (fixes #1759) (Nicholas C. Zakas)
* Fix: Don't warn on computed nonliteral properties (fixes #1762) (Nicholas C. Zakas)
* New: Allow parser to be configured (fixes #1624) (Nicholas C. Zakas)
* Docs: Added double quotes for JSON keys for comma-spacing and key-spacing rule (Dmitry Polovka)
* New: Rule indent (fixes #1022) (Dmitriy Shekhovtsov)
* Revert "New: Rule indent (fixes #1022)" (Nicholas C. Zakas)
* Update: fix eslint indentations (fixes #1770) (Dmitriy Shekhovtsov)
* Fix: Scoping issues for no-unused-vars (fixes #1741) (Nicholas C. Zakas)
* Docs: Added `eslint-enable` inline (Ivan Fraixedes)
* New: Add predefined Meteor globals (fixes #1763) (Johan Brook)
* New: Rule indent (fixes #1022) (Dmitriy Shekhovtsov)
* Update: Check all assignments for consistent-this (fixes #1513) (Timothy Jones)
* Fix: Support exceptions in no-multi-spaces (fixes #1755) (Brandon Mills)
* Docs: Forgotten parentheses in code snippet (Ivan Fraixedes)
* Update: CLIEngine results include warning and error count (fixes #1732) (gyandeeps)
* Fix: Scoping issues for no-unused-vars (fixes #1733) (Nicholas C. Zakas)
* Update: Add getNodeByRangeIndex method (refs #1755) (Brandon Mills)
* Update: Replace getTokenByRange(Index->Start) (refs #1721) (Brandon Mills)
* Update: Fast-path for empty input (fixes #546) (Nicholas C. Zakas)
* Fix: Allow single line else-if (fixes #1739) (Nicholas C. Zakas)
* Fix: Don't crash when $HOME isn't set (fixes #1465) (Nicholas C. Zakas)
* Fix: Make no-multi-spaces work for every case (fixes #1603, fixes #1659) (Nicholas C. Zakas)
* Breaking: Show error and warning counts in stylish summary (fixes #1746) (Brandon Mills)
* Docs: fixed typo in no-lone-blocks docs (Vitor Balocco)
* Docs: fixed typo in consistent-return docs (Vitor Balocco)
* Breaking: remove implied eval check from no-eval (fixes #1202) (Mathias Schreck)
* Update: Improve CLIEngine.getFormatter() (refs #1723) (Nicholas C. Zakas)
* Docs: Add Backbone plugin link (Ilya Volodin)
* Docs: use npm's keyword route (Tom Vincent)
* Build: Update sitegen script (Closes #1725) (Ilya Volodin)

v0.13.0 - January 24, 2015

* 0.13.0 (Nicholas C. Zakas)
* Update: The rule spaced-line-comment now also allows tabs and not only spaces as whitespace. (fixes #1713) (Dieter Oberkofler)
* Docs: add Jasmine rules and eslintplugin npm links (Tom Vincent)
* Fix: Make no-redeclare work with let (fixes #917) (Nicholas C. Zakas)
* Update: Add CLIEngine.getFormatter() (fixes #1653) (Nicholas C. Zakas)
* Breaking: Update escope (fixes #1642) (Nicholas C. Zakas)
* Update: Switch to using estraverse-fb (fixes #1712) (Nicholas C. Zakas)
* Docs: Update README FAQ (Nicholas C. Zakas)
* Update: no-warning-comments matches on whole word only (fixes #1709) (Nick Fisher)
* Build: Add JSDoc generation (fixes #1363) (Nicholas C. Zakas)
* Docs: Add more info about context (fixes #1330) (Nicholas C. Zakas)
* Upgrade: Espree to 1.7.1 (fixes #1706) (Nicholas C. Zakas)
* Docs: Make CLA notice more prominent (Nicholas C. Zakas)
* Update: Added globals for: phantom,jquery, prototypejs, shelljs (fixes #1704) (Dmitriy Shekhovtsov)
* Docs: Fixed example for the space-return-throw-case rule (mpal9000)
* Fix: Except object literal methods from func-names (fixes #1699) (Brandon Mills)
* Update: use global strict mode everywhere (fixes #1691) (Brandon Mills)
* Update: Add allowPattern option for dot-notation rule (fixes #1679) (Tim Schaub)
* Fix: Missing undeclared variables in JSX (fixes #1676) (Yannick Croissant)
* Fix: no-unused-expressions rule incorrectly flagging  yield (fixes #1672) (Rémi Gérard-Marchant)
* Update: Combine strict mode rules (fixes #1246) (Brandon Mills)
* Fix: disregards leading './' in ignore pattern or file name (fixes #1685) (Chris Montrois)
* Upgrade: globals module to latest (fixes #1670) (Nicholas C. Zakas)
* Fix: generator-star should allow params (fixes #1677) (Brandon Mills)
* Fix: no-unused-vars for JSX (fixes #1673 and fixes #1534) (Yannick Croissant)
* Docs: Add angularjs-eslint link into the integration doc (Emmanuel DEMEY)

v0.12.0 - January 17, 2015

* 0.12.0 (Nicholas C. Zakas)
* Fix: Track JSX global variable correctly (fixes #1534) (Nicholas C. Zakas)
* Fix: Property regex flag checking (fixes #1537) (Nicholas C. Zakas)
* Docs: Add angularjs-eslint link into the integration doc (Emmanuel DEMEY)
* Update: Expose ecmaFeatures on context (fixes #1648) (Nicholas C. Zakas)
* Docs: Added Fitbit to the list of companies (Igor Zalutsky)
* New: gen-star rule (refs #1617) (Jamund Ferguson)
* New: no-var rule (refs #1617) (Jamund Ferguson)
* Fix: Support JSX spread operator (fixes #1634) (Nicholas C. Zakas)
* Docs: Document ecmaFeatures (Nicholas C. Zakas)
* Upgrade: several dependencies (fixes #1377) (Nicholas C. Zakas)
* Fix: Broken JSX test (Nicholas C. Zakas)
* Fix: no-bitwise reports on bitwise assignment expressions (fixes #1643) (Mathias Schreck)
* Fix: Find JSXIdentifier refs in no-unused-vars (fixes #1534) (Nicholas C. Zakas)
* Update: Add a couple JSX tests (Nicholas C. Zakas)
* Fix: quotes rule ignores JSX literals (fixes #1477) (Nicholas C. Zakas)
* Fix: Don't warn on JSX literals with newlines (fixes #1533) (Nicholas C. Zakas)
* Update: Fully enable JSX support (fixes #1640) (Nicholas C. Zakas)
* Breaking: Allow parser feature flips (fixes #1602) (Nicholas C. Zakas)
* Fix: Allow comments in key-spacing groups (fixes #1632) (Brandon Mills)
* Fix: block-scoped-var reports labels (fixes #1630) (Michael Ficarra)
* Docs: add newline to no-process-env (fixes #1627) (Tom Vincent)
* Fix: Update optionator, --no in help (fixes #1134) (George Zahariev)
* Fix: Allow individual newlines in space-in-brackets (fixes #1614) (Brandon Mills)
* Docs: Correct alignment in example project tree (Tim Schaub)
* Docs: Remove references to Esprima (Nicholas C. Zakas)
* Docs: Remove illegal code fence (Nicholas C. Zakas)

v0.11.0 - December 30, 2014

* 0.11.0 (Nicholas C. Zakas)
* Fix: Adding regexp literal exception (fixes #1589) (Greg Cochard)
* Fix: padded-blocks incorrectly complained on comments (fixes #1416) (Mathias Schreck)
* Fix: column location of key-spacing with additional tokens (fixes #1458) (Mathias Schreck)
* Build: tag correct commit (refs #1606) (Mathias Schreck)
* Upgrade: Updat Espree to 1.3.1 (Nicholas C. Zakas)
* Fix: add es3 config option to dot-notation rule (fixes #1484) (Michael Ficarra)
* Fix: valid-jsdoc should recognize @class (fixes #1585) (Nicholas C. Zakas)
* Update: Switch to use Espree (fixes #1595) (Nicholas C. Zakas)
* Fix: brace-style stroustrup should report on cuddled elseif (fixes #1583) (Ian Christian Myers)
* New: Configuration via package.json (fixes #698) (Michael Mclaughlin)
* Update: Set environments w/ globals (fixes #1577) (Elan Shanker)
* Fix: yoda treats negative numbers as literals (fixes #1571) (Brandon Mills)
* Fix: function arguments now count towards no-shadow check (fixes #1584) (Glen Mailer)
* Fix: check if next statement is on newline when warning against extra semicolons. (fixes #1580) (Evan You)
* Update: add yoda exception for range tests (fixes #1561) (Brandon Mills)
* New: space-after-function-name (fixes #1340) (Roberto Vidal)

v0.10.2 - December 12, 2014

* 0.10.2 (Nicholas C. Zakas)
* Fix: detect for...in in no-loop-func (fixes #1573) (Greg Cochard)
* Update: simplify comma-spacing logic (fixes #1562) (Brandon Mills)
* Fix: operator-assignment addition is non-commutative (fixes#1556) (Brandon Mills)
* 0.10.1 (Nicholas C. Zakas)
* Update: Add new-cap exception configurations. (Fixes #1487)  - `newCapsAllowed`  - `nonNewCapsAllowed` (Jordan Harband)

v0.10.1 - December 6, 2014

* 0.10.1 (Nicholas C. Zakas)
* Docs: Fix v0.10.0 changelog (Nicholas C. Zakas)
* Build: Ensure changelog works with large semver versions (Nicholas C. Zakas)
* Fix: comma-spacing and comma-style to work with array literals (fixes #1492) (Nicholas C. Zakas)
* Update: better operator regex in use-isnan rule (fixes #1551) (Michael Ficarra)
* Fix: wrong op index in no-multi-spaces (fixes #1547) (Brandon Mills)
* Fix: Restrict use-isnan violations to comparison operators. (Fixes #1535) (Jordan Harband)
* Fix: comma-spacing has false positives when parenthesis are used (fixes #1457) (Jamund Ferguson)
* Docs: alphabetize the "Stylistic Issues" section (Jeff Williams)
* Build: make the "gensite" target work when DOCS_DIR does not exist (fixes #1530) (Jeff Williams)
* Docs: badges should only refer to master branch (Mathias Schreck)
* Fix: prevent crash on empty blocks in no-else-return (fixes #1527) (Mathias Schreck)
* Build: Fix md to html conversion regex (fixes #1525) (Brandon Mills)
* 0.10.0 (Nicholas C. Zakas)

v0.10.0 - November 27, 2014

* 0.10.0 (Nicholas C. Zakas)
* Fix: Add Object and Function as exceptions in new-cap (refs #1487) (Nicholas C. Zakas)
* Breaking: Allow extensionless files to be passed on CLI (fixes #1131) (Nicholas C. Zakas)
* Fix: typo: iffe to iife, none to non (Michael Ficarra)
* Update: refactor tokens API (refs #1212) (Brandon Mills)
* New: Allow other file extensions (fixes #801) (Nicholas C. Zakas)
* Update: Add Event to browser globals (fixes #1474) (Nicholas C. Zakas)
* Fix: check function call arguments in comma-spacing (fixes #1515) (Mathias Schreck)
* Update: Add no-cond-assign option to disallow nested assignments in conditionals (fixes #1444) (Jeff Williams)
* Fix: crash in no-multi-spaces on empty array elements (fixes #1418) (Brandon Mills)
* Fix: Don't explode on directory traversal (fixes #1452) (Nicholas C. Zakas)
* Fix: no-fallthrough should work when semis are missing (fixes #1447) (Nicholas C. Zakas)
* Fix: JSDoc parsing by updating doctrine (fixes #1442) (Nicholas C. Zakas)
* Update: restore the "runs" global present in Jasmine 1.3 (fixes #1498) (Michał Gołębiowski)
* Fix: ignore undefined identifiers in typeof (fixes #1482) (Mathias Schreck)
* Fix: Ignoring empty comments. (fixes #1488) (Greg Cochard)
* New: Add space-unary-ops rules (#1346) (Marcin Kumorek)
* Update: Remove shebang workaround in spaced-line-comment (fixes #1433) (Michael Ficarra)
* Docs: change 'and' to 'an' in docs/rules/valid-jsdoc.md (fixes #1441) (Michael Ficarra)
* Update: Add `beforeAll` and `afterAll` to the Jasmine globals (fixes #1478) (Gyandeep Singh)
* Update: Add exception options to space-in-parens (fixes #1368) (David Clark)
* Build: Add check for license issues (fixes #782) (Brandon Mills)
* Docs: update badges (Yoshua Wuyts)
* Docs: Update pages to fix rendering of lists and fenced code blocks (David Anson)
* Fix: env rules merging for command line config (fixes #1271) (Roberto Vidal)
* Fix: Collect variables declare in switch-case.(fixes #1453) (chris)
* Fix: remove extra capture group (Nate-Wilkins)
* Update: allow distinct alignment groups in key-spacing (fixes #1439) (Brandon Mills)
* Fix: message for numeric property names in quote-props (fixes #1459) (Brandon Mills)
* Docs: Remove assumption about the rule config (Alexander Schmidt)
* New: Add ability to time individual rules (fixes #1437) (Brandon Mills)
* Fix: single quotes (Nate-Wilkins)
* Docs: Fix broken code fences in key-spacing docs (Brandon Mills)
* Docs: Explain .eslintignore features (fixes #1094) (Brandon Mills)
* Breaking: ignore node_modules by default (fixes #1163) (Brandon Mills)
* Fix: Adds clamping to getSource beforeCount (fixes #1427) (Greg Gianforcaro)
* New: add no-inline-comment rule (fixes #1366) (Greg Cochard)
* Fix: '.md' to '.html' with anchors (fixes #1415) (Nate-Wilkins)
* Build: Filter and sort versions in gensite (fixes #1430) (Brandon Mills)
* Build: Escape period in regex (fixes #1428) (Brandon Mills)
* Revert "Fix: '.md' to '.html' with anchors (fixes #1415)" (Nicholas C. Zakas)
* 0.9.2 (Nicholas C. Zakas)
* New: Add operator-assignment rule (fixes #1420) (Brandon Mills)

v0.9.2 - November 1, 2014

* 0.9.2 (Nicholas C. Zakas)
* Fix: '.md' to '.html' with anchors (fixes #1415) (Nate-Wilkins)
* Fix: Allow line breaks in key-spacing rule (fixes #1407) (Brandon Mills)
* Build: add coveralls integration (fixes #1411) (Mathias Schreck)
* Fix: add severity flag for ignored file warning (fixes #1401) (Mathias Schreck)
* Fix: Keep sinon at ~1.10.3 (fixes #1406) (Brandon Mills)
* Fix: ! negates .eslintignore patterns (fixes #1093) (Brandon Mills)
* Fix: let fs.stat throw if a file does not exist (fixes #1296) (Mathias Schreck)
* Fix: check switch statements in space-before-blocks (fixes #1397) (Mathias Schreck)
* Docs: fix rule name in example configuration (Mathias Schreck)
* Fix: disable colors during test run (fixes #1395) (Mathias Schreck)
* New: add isPathIgnored method to CLIEngine (fixes #1392) (Mathias Schreck)
* Docs: changing eslint to ESLint and add missing backtick (Mathias Schreck)
* Docs: Documents the functionality to load a custom formatter from a file (Adam Baldwin)
* 0.9.1 (Nicholas C. Zakas)
* Update: Option type for mixed tabs and spaces (fixes #1374) (Max Nordlund)
* Fix: Nested occurrences of no-else-return now show multiple reports (fixes #1369) (Jordan Hawker)

v0.9.1 - October 25, 2014

* 0.9.1 (Nicholas C. Zakas)
* Docs: fix link on governance model (azu)
* Fix: plugins without rulesConfig causes crash (fixes #1388) (Mathias Schreck)
* 0.9.0 (Nicholas C. Zakas)

v0.9.0 - October 24, 2014

* 0.9.0 (Nicholas C. Zakas)
* New: Allow reading from STDIN (fixes #368) (Nicholas C. Zakas)
* New: add --quiet option (fixes #905) (Mathias Schreck)
* Update: Add support for plugin default configuration (fixes #1358) (Ilya Volodin)
* Fix: Make sure shebang comment node is removed (fixes #1352) (Nicholas C. Zakas)
* New: Adding in rule for irregular whitespace checking. (fixes #1024) (Jonathan Kingston)
* Fix: space-in-parens should not throw for multiline statements (fixes #1351) (Jary)
* Docs: Explain global vs. local plugins (fixes #1238) (Nicholas C. Zakas)
* Docs: Add docs on Node.js API (fixes #1247) (Nicholas C. Zakas)
* Docs: Add recommended keywords for plugins (fixes #1248) (Nicholas C. Zakas)
* Update: Add CLIEngine#getConfigForFile (fixes #1309) (Nicholas C. Zakas)
* Update: turn on comma-style for project (fixes #1316) (Nicholas C. Zakas)
* Fix: Ensure messages are sorted by line (fixes #1343) (Nicholas C. Zakas)
* Update: Added arraysInObjects and objectsInObjects options to space-in-brackets rule (fixes #1265, fixes #1302) (vegetableman)
* Breaking: Removed comma spacing check from space-infix-ops (fixes #1361) (vegetableman)
* Fix: addressed linting errors (Nicholas C. Zakas)
* Docs: Add Contributor Model (fixes #1341) (Nicholas C. Zakas)
* Docs: Add reference to CLA (Nicholas C. Zakas)
* Build: add version numbers to docs (fixes #1170) (Mathias Schreck)
* Fix: no-fallthrough incorrectly flagged falls through annotations (fixes #1353) (Mathias Schreck)
* Build: separate site publishing form generation (fixes #1356) (Mathias Schreck)
* New: Add key-spacing rule (fixes #1280) (Brandon Mills)
* New: add spaced-line-comment rule (fixes #1345) (Greg Cochard)
* Docs: added more Related Rules sections (fixes #1347) (Delapouite)
* Fix: resolve linting issue in (fixes #1339) (Nicholas C. Zakas)
* New: add space-before-blocks rule (fixes #1277) (Mathias Schreck)
* Docs: Remove moot integration plugins (Sindre Sorhus)
* New: add rule for multiple empty lines (fixes #1254) (Greg Cochard)
* Fix: no-shadow rule should consider function expressions (fixes #1322) (Mathias Schreck)
* Update: remove globals present only in Jasmine plugins (fixes #1326) (Michał Gołębiowski)
* New: added no-multi-spaces rule (fixes #630) (vegetableman)
* New: Added comma-spacing rule (Fixes #628, Fixes #1319) (vegetableman)
* New: add rule for padded blocks (fixes #1278) (Mathias Schreck)
* Docs: fix eqeqeq isNullCheck comment (Denis Sokolov)
* Fix: no-comma-dangle violation in unit test and Makefile.js/lint not checking return codes (fixes #1306) (David Anson)
* Fix: allow comma-last with object properties having line breaks (fixes #1314) (vegetableman)
* New: Added comma-style rule (fixes #1282) (vegetableman)
* Update: add space after function keyword check (fixes #1276) (Mathias Schreck)
* Update: Add missing environments and fix sorting/grouping of rules (fixes #1307, fixes #1308) (David Anson)
* Docs: Fix sorting of rules within each section (David Anson)
* Docs: Correct a few misspelled words (David Anson)
* Docs: Update multiple pages to fix rendering of fenced code blocks (David Anson)
* New: Added no-process-env rule (fixes #657) (vegetableman)
* Fix: add rule ensuring #1258 is fixed by recent rewrite (fixes #1258) (Michael Ficarra)
* Update: split propertyName from singleValue in space-in-brackets (fixes #1253) (Michael Ficarra)
* Update: add "as-needed" option to quote-props rule (fixes #1279) (Michael Ficarra)
* Docs: fixed broken link and changed warning level to error level (vegetableman)
* Docs: Added "the native web" to the list of companies that use ESLint. (Golo Roden)
* Docs: Add BountySource badge to README (Nicholas C. Zakas)
* 0.8.2 (Nicholas C. Zakas)

v0.8.2 - September 20, 2014

* 0.8.2 (Nicholas C. Zakas)
* Docs: Updated contribution guidelines to add accepted/bounty issues descriptions (Nicholas C. Zakas)
* Docs: Update README with links and FAQs (Nicholas C. Zakas)
* Docs: add finally to space-after-keywords documentation (Mathias Schreck)
* New: add ignoreCase option to sort-vars (fixes #1272) (Mathias Schreck)
* Docs: fix typo (Barry Handelman)
* Docs: Fix broken Markdown on configuration page (Nicholas C. Zakas)
* Docs: Fix reference to wrong rule name (Harry Wolff)
* Upgrade: Most dev dependencies (Nicholas C. Zakas)
* Upgrade: shelljs to 0.3.0 (Nicholas C. Zakas)
* Upgrade: doctrine to 0.5.2 (Nicholas C. Zakas)
* Upgrade: esprima to 1.2.2 (Nicholas C. Zakas)
* Upgrade: eslint-tester to latest (Nicholas C. Zakas)
* Fix: Load .eslintrc in directory with $HOME as an ancestor (fixes #1266) (Beau Gunderson)
* Fix: load .eslintrc from HOME (fixes #1262) (Beau Gunderson)
* New: Add sharable rule settings (fixes #1233) (Ilya Volodin)
* Upgrade: upgrade outdated dependencies (fixes #1251) (Mathias Schreck)
* Docs: fix typo in no-ex-assign documentation (Michael Ficarra)
* Docs: add intellij plugin to integrations (ido)
* Docs: Changing NPM to npm (Peter deHaan)
* Fix: strict should check function expressions (fixes #1244) (Brandon Mills)
* Docs: fix vars-on-top documentation (fixes #1234) (Mathias Schreck)
* 0.8.1 (Nicholas C. Zakas)
* Docs: Fixed a typo in brace-style.md (Anton Antonov)

v0.8.1 - September 9, 2014

* 0.8.1 (Nicholas C. Zakas)
* Fix: Ensure exit code is 1 when there's a syntax error (fixes #1239) (Nicholas C. Zakas)
* Docs: fix up vars-on-top documentation (fixes #1234) (Michael Ficarra)
* Fix: vars-on-top directive support (fixes #1235) (Michael Ficarra)
* Fix: Avoid mutating node.range in max-len (fixes #1224) (Brandon Mills)
* Docs: Typo, add missing quotation mark (Ádám Lippai)
* Update: space-in-brackets to allow exceptions (fixes #1142) (Brandyn Bennett)
* 0.8.0 (Nicholas C. Zakas)

v0.8.0 - September 5, 2014

* 0.8.0 (Nicholas C. Zakas)
* Perf-related revert "Fix: Speed up tokens API (refs #1212)" (Nicholas C. Zakas)
* Fix: no-fallthrough: continue affects control flow, too (fixes #1220) (Michael Ficarra)
* Fix: rewrite no-unused-vars rule (refs #1212) (Michael Ficarra)
* Fix: Error when there's a \r in .eslintrc (#1172) (Gyandeep Singh)
* Added rule disallowing reserved words being used as keys (fixes #1144) (Emil Bay)
* Fix: rewrite no-spaced-func rule (refs #1212) (Michael Ficarra)
* Fix: Speed up getScope() (refs #1212) (Brandon Mills)
* Fix: no-extra-strict behavior for named function expressions (fixes #1209) (Mathias Schreck)
* Add Date.UTC to allowed capitalized functions (David Brockman Smoliansky)
* New:  Adding 'vars-on-top' rule (fixes #1148) (Gyandeep Singh)
* Fix: Speed up tokens API (refs #1212) (Brandon Mills)
* Docs: document plugin usage (fixes #1117) (Mathias Schreck)
* New: accept plugins from cli (fixes #1113) (Mathias Schreck)
* Docs: fix some typos. (Mathias Schreck)
* New: Load plugins from configs (fixes #1115). (Mathias Schreck)
* Fix: no-unused-expressions better directive detection (fixes #1195) (Michael Ficarra)
* Fix: no-unused-expressions directive support (fixes #1185) (Michael Ficarra)
* Update: Add 'allowSingleLine' option to brace-style (fixes #1089) (John Gozde)
* Docs: Spell checking and one extra closing curly in code example (Juga Paazmaya)
* Fix: mergeConfigs ensures the plugins property exists (fixes #1191). (Mathias Schreck)
* Update: Declare ES6 collections (Map, Set, WeakMap, WeakSet) as built-in globals (fixes #1189) (Michał Gołębiowski)
* New: Adding 'plugin' CLI option (fixes #1112) (Greg)
* Fix: Correct a typo in the error message in tests (Michał Gołębiowski)
* New: Add no-extra-bind rule to flag unnecessary bind calls (fixes #982) (Bence Dányi)
* Fix: Useless bind call in cli-engine (fixes #1181) (Bence Dányi)
* Docs: Updates `amd` description (fixes #1175) (James Whitney)
* New: Adds support for the `jasmine` env (fixes #1176) (James Whitney)
* Fix: for-in support to no-empty-label rule (fixes #1161) (Marc Harter)
* docs: Update link (Mathias Bynens)
* Fix: crash when loading empty eslintrc file (fixes #1164) (Michael Ficarra)
* Fix: no-unused-var should respect compound assignments (fixes #1166) (Michael Ficarra)
* Update: ES3 `ReservedWord`s (fixes #1151) Adds ES3 `ReservedWord`s to the list of keywords in the `dot-notation` rule (fixes #1151) (Emil Bay)
* Update: Update comment parser to read rule slashes (fixes #1116) (Jary)
* New: add no-void rule (fixes #1017). (Mike Sidorov)
* New: Add rules.import() (fixes #1114) (Mathias Schreck)
* New: Make mergeConfigs() merge plugin entries (fixes #1111) (Mathias Schreck)
* Breaking: Change no-global-strict to global-strict and add "always" option (fixes #989) (Brandon Mills)
* Fix: no-unreachable should check top-level statements (fixes #1138) (Brandon Mills)
* Fix: Speed up no-unreachable (fixes #1135) (Brandon Mills)
* New: advanced handle-callback-err configuration (fixes #1124) (Mathias Schreck)
* New: Expose CLIEngine (fixes #1083) (Gyandeep Singh)
* Docs: Add link to new Atom linter (fixes #1125) (Gil Pedersen)
* Fix: space-after-keywords checks finally of TryStatement (fixes #1122) (Michael Ficarra)
* Fix: space-after-keywords checks while of DoWhileStatement (fixes #1120) (Michael Ficarra)
* Fix: space-after-keywords w/ "never" should allow else-if (fixes #1118) (Michael Ficarra)
* Fix: dot-notation rule flags non-keyword reserved words (fixes #1102) (Michael Ficarra)
* Update: Use xml-escape instead of inline helper (Ref #848) (jrajav)
* Update: Added comments support to .eslintignore (fixes #1084) (Vitaly Puzrin)
* Update: enabled 'no-trailing-spaces' rule by default (fixes #1051) (Vitaly Puzrin)
* Breaking: Ignore children of all patterns by adding "/**" (Fixes #1069) (jrajav)
* Fix: skip dot files and ignored dirs on traverse (fixes #1077, related to #814) (Vitaly Puzrin)
* Docs: Added Gruntjs plugin on integrations page (Gyandeep Singh)
* Fix: don't break node offsets if hasbang present (fixes #1078) (Vitaly Puzrin)
* Build: Exclude readme/index from rules Resources generation (Fixes #1072) (jrajav)
* Docs: Change eol-last examples to `<pre>` (Fixes #1068) (jrajav)
* 0.7.4 (Nicholas C. Zakas)
* New: space-in-parens rule (Closes #627) (jrajav)

v0.7.4 - July 10, 2014

* 0.7.4 (Nicholas C. Zakas)
* Docs: Fix 'lintinging' typo and ref links (Tom Vincent)
* Fix: Transform envs option to object in Config (Fixes #1064) (jrajav)
* 0.7.3 (Nicholas C. Zakas)

v0.7.3 - July 9, 2014

* 0.7.3 (Nicholas C. Zakas)
* Update: Address code review comment for strict rule (refs #1011) (Nicholas C. Zakas)
* Docs: Update copyright policy (Nicholas C. Zakas)
* Docs: Update documentation for max-len to include description of second option (fixes #1006) (Nicholas C. Zakas)
* Fix: Avoid double warnings for strict rule (fixes #1011) (Nicholas C. Zakas)
* Fix: Check envs for true/false (Fixes #1059) (jrajav)
* 0.7.2 (Nicholas C. Zakas)

v0.7.2 - July 8, 2014

* 0.7.2 (Nicholas C. Zakas)
* Fix: no-mixed-spaces-and-tabs incorrectly flagging multiline comments (fixes #1055) (Nicholas C. Zakas)
* Fix: new-cap error that throws on non-string member (fixes #1056) (Nicholas C. Zakas)
* Fix: Always make globals an object (Fixes #1049) (jrajav)
* 0.7.1 (Nicholas C. Zakas)

v0.7.1 - July 7, 2014

* 0.7.1 (Nicholas C. Zakas)
* Docs: Add Related Rules sections (Fixes #990) (jrajav)
* Fix: Check output file isn't dir, fix tests (Fixes #1034) (jrajav)
* Docs: Updated documentation for several rules (Nicholas C. Zakas)
* Docs: Updated contributor guide and dev env setup guide (Nicholas C. Zakas)
* Breaking: Implement configuration hierarchy (fixes #963) (Nicholas C. Zakas)
* Update: greatly simplify eqeqeq's operator finding logic (fixes #1037) (Michael Ficarra)
* New: Add getSourceLines() to core and rule context (fixed #1005) (Jary)
* Build + Docs: Adding generated resource links to rule docs (Fixes #1021) (jrajav)
* Fix: Ignore unused params for args: 'none' (Fixes #1026) (jrajav)
* Fix: Point eqeqeq error at operator (Fixes #1029) (jrajav)
* New: report output to a file (fixes #1027) (Gyandeep Singh)
* Breaking: CLIEngine abstraction for CLI operations; formatters no longer are passed configs (fixes #935) (Nicholas C. Zakas)
* Fix: Allow stdout to drain before exiting (fixes #317) (Nicholas C. Zakas)
* New: add no-undefined rule (fixes #1020) (Michael Ficarra)
* New: Added no-mixed-spaces-and-tabs rule (fixes #1003) (Jary)
* New: Added no-trailing-spaces rule (fixes #995) (Vitaly Puzrin)
* Update: Factor ignores out of Config (fixes #958) (jrajav)
* Fix: rewrite eol-last rule (fixes #1007) (fixes #1008) (Michael Ficarra)
* Fix: add additional IIFE exception in no-extra-parens (fixes #1004) (Michael Ficarra)
* Docs: Removed reference to brace-style Stroustrup default (fixes #1000) (Caleb Troughton)
* New: Added eol-last rule (Fixes #996) (Vitaly Puzrin)
* Fix: Put rule severity in messages (Fixes #984); deprecates passing full config to Formatters (jrajav)
* Fix: no-unused-vars to check only file globals (fixes #975) (Aliaksei Shytkin)
* Build: Makefile - Check for rule ids in docs titles (Fixes #969) (Delapouite)
* Docs: guard-for-in - added missing id in title (Fixes #969) (Delapouite)
* Breaking: Change 'no-yoda' rule to 'yoda' and add "always" option (Fixes #959) (jrajav)
* Fix: Fixes no-unused-vars to check /*globals*/ (Fixes #955) (jrajav)
* Update: no-eval to also warn on setTimeout and setInterval (fixes #721) (Nicholas C. Zakas)
* Remove: experimental match() method (Nicholas C. Zakas)
* Update: space-in-brackets now always allows empty object and array literals to have no spaces (fixes #797) (Nicholas C. Zakas)
* New: Allow the cli parameter "color" and "no-color" (fixes #954) (Tom Gallacher)
* Fix: valid-jsdoc no more warning for multi-level params (Fixes #925) (Delapouite)
* Update: Search parent directories for .eslintignore (Fixes #933) (jrajav)
* Fix: Correct order of arguments passed to assert.equal (fixes #945) (Michał Gołębiowski)
* Update: Write the summary in stylish formatter in yellow if no errors (fixes #906); test coloring of messages (Michał Gołębiowski)
* Fix: Corrects configs merging into base config (Fixes #838) (jrajav)
* Fix: Adding check if char is non-alphabetic to new-cap (Fixes #940) (jrajav)
* Docs: Update about page description (fixes #936) (Nicholas C. Zakas)
* Docs: Add '/', forgotten in first commit (Fixes #931) (jrajav)
* Update: Rule `new-cap` checks capitalized functions (fixes #904) (Aliaksei Shytkin)
* Docs: Mention allowed semicolons in "never" mode for 'semi' rule (fixes #931) (jrajav)
* Docs: Mention Yeoman generator in dev setup (fixes #914) (Nicholas C. Zakas)
* Build: Remove flaky perf test from Travis (Nicholas C. Zakas)
* Breaking: Refactor .eslintignore functionality (refs #928, fixes #901, fixes #837, fixes #853) (Nicholas C. Zakas)
* 0.6.2 (Nicholas C. Zakas)
* Breaking: Remove JSON support for .eslintignore (fixes #883) (icebox)

v0.6.2 - May 23, 2014

* 0.6.2 (Nicholas C. Zakas)
* Fix: Adding per-environment rule configs to docs and doc validation (Fixes #918) (jrajav)
* Docs: Updated contribution guidelines (Nicholas C. Zakas)
* Docs: Update description of eqeqeq to mention special cases (fixes #924) (Nicholas C. Zakas)
* Fix: block-scoped-var CatchClause handling (fixes #922) (Michael Ficarra)
* Fix: block-scoped-var respects decls in for and for-in (fixes #919) (Michael Ficarra)
* Update: Implement eqeqeq option "allow-null" (fixes #910) (Michał Gołębiowski)
* Fix: new-cap should allow non-alpha characters (fixes #897) (Michael Ficarra)
* Update: Refactor ESLintTester to fix dependency hell (fixes #602) (Nicholas C. Zakas)
* Fix: Merge configs with ancestors (Fixes #820) (jrajav)
* Fix: no-fallthrough should respect block statements in case statements (fixes #893) (Nicholas C. Zakas)
* Docs: Fix layout issue in configuration docs (fixes #889) (Nicholas C. Zakas)
* Build: Enable default-case rule (fixes #881) (icebox)
* Build: Enable space-after-keywords (fixes #884) (icebox)
* Fix api double emit on comment nodes (fixes #876) (Aliaksei Shytkin)
* 0.6.1 (Nicholas C. Zakas)

v0.6.1 - May 17, 2014

* 0.6.1 (Nicholas C. Zakas)
* Upgrade: Optionator to 0.4.0 (fixes #885) (Nicholas C. Zakas)
* 0.6.0 (Nicholas C. Zakas)

v0.6.0 - May 17, 2014

* 0.6.0 (Nicholas C. Zakas)
* Fix: Remove -r alias for --rule (fixes #882) (Nicholas C. Zakas)
* Docs: Update dev setup, contributing, default-case descriptions (Nicholas C. Zakas)
* Update: valid-jsdoc now allows you to optionally turn off parameter description checks (fixes #822) (Nicholas C. Zakas)
* Breaking: brace-style now disallows block statements where curlies are on the same line (fixes #758) (Nicholas C. Zakas)
* Add linting Makefile.js (fixes #870) (icebox)
* add rule flag, closes #692 (George Zahariev)
* Add check between rules doc and index (fixes #865) (icebox)
* Add Build Next mention in integrations README. (icebox)
* document new IIFE exception for no-extra parens added as part of #655 (Michael Ficarra)
* (fixes #622) Add rule ID on documentation pages (Delapouite)
* fixes #655: add IIFE exception to no-extra-parens (Michael Ficarra)
* add new rule "no-new-require" (Wil Moore III)
* exit with non-zero status when tests fail (fixes #858) (Márton Salomváry)
* removed unicode zero width space character from messages (fixes #857) (Márton Salomváry)
* Change: --rulesdir now can be specified multiple times (fixes #830) (Nicholas C. Zakas)
* Update: Node 0.8 no longer supported (fixes #734) (Nicholas C. Zakas)
* Update: Add typed arrays into builtin environment globals (fixes #846) (Nicholas C. Zakas)
* Fix: Add prototype methods to global scope (fixes #700) (Nicholas C. Zakas)
* Rule: no-restricted-modules (fixes #791) (Christian)
* Upgrade: Esprima to 1.2 (fixes #842) (Nicholas C. Zakas)
* Docs: reporting level 2 is an error (fixes #843) (Brandon Mills)
* Upgrade: Esprima to 1.2, switch to using Esprima comment attachment (fixes #730) (Nicholas C. Zakas)
* Fix: Semi rule incorrectly flagging extra semicolon (fixes #840) (Nicholas C. Zakas)
* Build: Update Travis to only test Node 0.10 (refs #734) (Nicholas C. Zakas)
* Add "nofunc" option (fixes #829) (Conrad Zimmerman)
* Rule: no-inner-declarations (fixes #587) (Brandon Mills)
* Rule 'block-scoped-var': correct scope for functions, arguments (fixes #832) (Aliaksei Shytkin)
* Rule: default-case (fixes #787) (Aliaksei Shytkin)
* Ignored files are excluded unless --force is passed on the CLI (Nick Fisher)
* Fixes a typo and a broken link in the documentation (Nick Fisher)
* Replaces .some() with .indexOf() where appropriate (Nick Fisher)
* Fix correct config merge for array values (fixes #819) (Aliaksei Shytkin)
* Remove warning about ESLint being in Alpha (Nick Fisher)
* Adds `space-after-keywords` rule (fixes #807) (Nick Fisher)
* Rule: no-lonely-if (fixes #790) (Brandon Mills)
* Add ignore comments in file (fixes #305) (Aliaksei Shytkin)
* 0.5.1 (Nicholas C. Zakas)
* Change: no-unused-vars default to 'all' (fixes #760) (Nicholas C. Zakas)

v0.5.1 - April 17, 2014

* 0.5.1 (Nicholas C. Zakas)
* Fix general config not to be modified by comment config in files (fixes #806) (Aliaksei Shytkin)
* SVG badges (Ryuichi Okumura)
* fixes #804: clean up implementation of #803 (which fixed #781) (Michael Ficarra)
* Build: Fix perf test to take median of three runs (fixes #781) (Nicholas C. Zakas)
* Fix: --reset will now properly ignore default rules in environments.json (fixes #800) (Nicholas C. Zakas)
* Docs: Updated contributor guidelines (Nicholas C. Zakas)
* Added Mocha global variables for TDD style. Fixes #793. (Golo Roden)
* Rule: no-sequences (fixes #561) (Brandon Mills)
* Change .eslintignore to plain text (fixes #761) (Brandon Mills)
* Change 'no-spaced-func' message (fixes #762) (Aliaksei Shytkin)
* Rule 'block-scoped-var' works correct when object inits (fixes #783) (Aliaksei Shytkin)
* Build: Always build docs site on top of origin/master (Nicholas C. Zakas)
* 0.5.0 (Nicholas C. Zakas)

v0.5.0 - April 10, 2014

* 0.5.0 (Nicholas C. Zakas)
* Build: Bump perf limit so Travis won't fail every time (fixes #780) (Nicholas C. Zakas)
* Add tests to cover 100% of eslint.js (Aliaksei Shytkin)
* Fix: Make sure no-path-concat doesn't flag non-concat operations (fixes #776) (Nicholas C. Zakas)
* Rule 'no-unused-var' in functional expression with identifier (fixes #775) (Aliaksei Shytkin)
* Rule: valid-typeof (Ian Christian Myers)
* Add global cli flag (ref #692) (Brandon Mills)
* update to latest Optionator (George Zahariev)
* Add options for rule 'no-unused-vars' to check all arguments in functions (fixes #728) (Aliaksei Shytkin)
* Fix: Cleanup package.json (Nicholas C. Zakas)
* New: Experimental support for CSS Auron (fixes #765) (Nicholas C. Zakas)
* Lint tests on build (fixes #764) (Aliaksei Shytkin)
* Rule block-scoped-var works correct with object properties (fixes #755) (Aliaksei Shytkin)
* Breaking: implement eslint-env and remove jshint/jslint environment comment support (fixes #759) (Aliaksei Shytkin)
* readme: npm i -> npm install (Linus Unnebäck)
* Add env flag to cli options summary (fixes #752) (Brandon Mills)
* Fix: Give the perf test a better calculated budget (fixes #749) (Nicholas C. Zakas)
* give the `env` flag type `[String]`, improve code (fixes #748) (George Zahariev)
* fixes #735: add new, more efficient getTokens interfaces (Michael Ficarra)
* Add --env cli flag (ref #692) (Brandon Mills)
* Fixes #740 - Make sure callbacks exist before marking them as 'handled'. (mstuart)
* fixes #743: wrap-regex rule warns on regex used in dynamic member access (Michael Ficarra)
* replace tab indents with 4 spaces in lib/rules/handle-callback-err.js (Michael Ficarra)
* Adding homepage and bugs links to package.json (Peter deHaan)
* JSDoc for rules (Anton Rudeshko)
* 0.4.5 (Nicholas C. Zakas)

v0.4.5 - March 29, 2014

* 0.4.5 (Nicholas C. Zakas)
* Build: Add perf check into Travis build to better monitor performance regressions (fixes #732) (Nicholas C. Zakas)
* Fix: Make sure semi reports correct location of missing semicolon (fixes #726) (Nicholas C. Zakas)
* Add --no-eslintrc cli flag (ref #717) (Brandon Mills)
* Fix #716 crash with reset flag (Brandon Mills)
* Fixed JSON formatting and highlighting (Anton Rudeshko (Tesla))
* fixes #723: block-scoped-var throws on unnamed function expression (Michael Ficarra)
* Fix: Make stroustrup brace-style closing message make sense (fixes #719) (Nicholas C. Zakas)
* no-comma-dangle reports correct line number (Andrey Popp)
* Upgrade: Esprima to 1.1.1 and EScope to 1.0.1 (fixes #718) (Nicholas C. Zakas)
* Add reset cli flag (refs #692) (Brandon Mills)
* Relax eqeqeq null check (fixes #669) (Brandon Mills)
* 0.4.4 (Nicholas C. Zakas)
* New Rule: handle-callback-err (fixes #567) (Jamund Ferguson)

v0.4.4 - March 25, 2014

* 0.4.4 (Nicholas C. Zakas)
* Fix no-used-vars to report FunctionExpression params (fixes #697). (Andrey Popp)
* fixes #711: eslint reports wrong line number for files with shebang (Michael Ficarra)
* Fix for no-unused-vars and MemberExpression (Andrey Popp)
* added no-warning-comments rule (Alexander Schmidt)
* fixes #699: brace-style does not check function expressions (Michael Ficarra)
* rewrite block-scoped-var (Michael Ficarra)
* recommend using hasOwnProperty from Object.prototype in guard-for-in docs (Michael Ficarra)
* change conf/environments.json spacing to be simpler and more consistent (Michael Ficarra)
* Update API to use context.getFilename() instead of .filename. (Loren Segal)
* Small changes, JSDoc is clarified (Aliaksei Shytkin)
* Move FileFinder to separate file (Aliaksei Shytkin)
* Cache if file is not found (Aliaksei Shytkin)
* Use cache on config files seach (Aliaksei Shytkin)
* Added .eslintignore to load from parents folders (fixes #681) (Aliaksei Shytkin)
* fix 'node-modules' typo in docs (Fred K. Schott)
* Upgrade to the latest version of doctrine. (Brian Di Palma)
* Document optional filename and default it to `input`. (Loren Segal)
* Fix: Compatibility for Node 0.8 (Nicholas C. Zakas)
* Update: Makefile.js now uses shelljs-nodecli (Nicholas C. Zakas)
* #681 apply all .eslintignore exclusions (Aliaksei Shytkin)
* Add RuleContext.filename property (for eslint/eslint#468). (Loren Segal)
* 0.4.3 (Nicholas C. Zakas)

v0.4.3 - March 18, 2014

* 0.4.3 (Nicholas C. Zakas)
* fixes #682: rewrite no-constant-condition rule (Michael Ficarra)
* Fixes #673 allow configuration of @return errors via requireReturn - (fixes #673) (Brian Di Palma)
* Tweaking inline code formatting for "if, while, dowhile" (Peter deHaan)
* Fixes #677 getJSDocComment() should not search beyond FunctionExpression or FunctionDeclaration parent nodes. (Brian Di Palma)
* Relaxed enforcement of camelcase rule (Ian Christian Myers)
* Fixing issue #675. Incorrect triggering of no-else-return rule. (Brian Di Palma)
* Added style option for wrap-iife (Mathias Schreck)
* Fix: Issues with named function expressions in no-unused-vars and no-shadow (fixes #662) (Nicholas C. Zakas)
* Update: camelcase rule now doesn't flag function calls (fixes #656) (Nicholas C. Zakas)
* Updating documentation description for: no-space-before-semi rule, changing rules to exempt strings with semicolons and test for that condition. Fixes #629. (Jonathan Kingston)
* Adding in rule no-space-before-semi to prevent spaces before semicolons. fixes #629 (Jonathan Kingston)
* show NPM version (Paul Verest)
* adapt code formatting (Mathias Schreck)
* Added a TextMate 2 integration to the docs (Nate Silva)
* 0.4.2 (Nicholas C. Zakas)

v0.4.2 - March 3, 2014

* 0.4.2 (Nicholas C. Zakas)
* fixes #651: disable no-catch-shadow rule in node environment (Michael Ficarra)
* Fixed context.report message parsing (Ian Christian Myers)
* fixe #648: wrap-iife rule should actually check that IIFEs are wrapped (Michael Ficarra)
* Added "stroustrup" option for brace-style (Ian Christian Myers)
* 0.4.1 (Nicholas C. Zakas)

v0.4.1 - February 27, 2014

* 0.4.1 (Nicholas C. Zakas)
* Created space-in-brackets rule (Ian Christian Myers)
* Update: Allow valid-jsdoc to specify replacement tags (fixes #637) (Nicholas C. Zakas)
* Fix: Ensure getJSDocComment() works for all function declarations (fixes #638) (Nicholas C. Zakas)
* Added broccoli-eslint to integration docs (Christian)
* fixes #634: getters/setters shouldn't trigger no-dupe-keys (Michael Ficarra)
* Update: semi to also enforce not using semicolons (fixes #618) (Nicholas C. Zakas)
* New Rule: no-constant-condition  - removed SwitchStatement discriminant check  - removed AssignmentExpression with right Identifier  - fixed copy paste error  - added DoWhileStatement, ForStatement based on discussion: https://github.com/eslint/eslint/pull/624 (fixes #621) (Christian)
* New Rule: no-constant-condition (fixes #621) (Christian)
* Adding mimosa-eslint to Build System list (dbashford)
* Fix: Make sure semi flags return statements without a semicolon (fixes #616) (Nicholas C. Zakas)
* Fix: stylish formatter blue text -> white text (fixes #607) (Nicholas C. Zakas)
* Fix: radix rule should warn (not throw error) when parseInt() is called without arguments (fixes #611) (Nicholas C. Zakas)
* Update README.md (Dmitry)
* Adding JSDoc comments for TAP format helper functions (Jonathan Kingston)
* Updating documentation to include TAP format option (Jonathan Kingston)
* Fixing validation issues to TAP formatter (Jonathan Kingston)
* Adding TAP formatter and basic tests (Jonathan Kingston)
* Docs: Updated integrations page (Nicholas C. Zakas)
* 0.4.0 (Nicholas C. Zakas)

v0.4.0 - February 12, 2014

* 0.4.0 (Nicholas C. Zakas)
* Change: Switch :after to :exit (fixes #605) (Nicholas C. Zakas)
* Fix: Make sure no-unused-vars doesn't get confused by nested functions (fixes #584) (Nicholas C. Zakas)
* Update: .eslintrc to check more things (Nicholas C. Zakas)
* Fix: Make sure JSDoc parser accepts JSDoc3-style optional parameters (Nicholas C. Zakas)
* Docs: Update documentation with linking instructions for ESLintTester (Nicholas C. Zakas)
* New Rule: valid-jsdoc (fixes #536) (Nicholas C. Zakas)
* #595 improved func-names documentation (Kyle Nunery)
* #595 added more func-names tests (Kyle Nunery)
* #595 fix rule message and add more tests (Kyle Nunery)
* use optionator for option parsing, not optimist (George Zahariev)
* Include instructions for working with ESLintTester (Nicholas C. Zakas)
* #595 remove needless 'function Foo() {}' in tests (Kyle Nunery)
* #595 fix whitespace (Kyle Nunery)
* #595 fix markdown for js code blocks (Kyle Nunery)
* Adding information about Yeomen generator (Ilya Volodin)
* #595 add docs for rule func-names (Kyle Nunery)
* #595 add func-names rule (Kyle Nunery)
* migrate variables array to map (Brandon Mills)
* Perf: Move try-catch out of verify() function to allow V8 optimization (refs #574) (Nicholas C. Zakas)
* Docs: Added instructions for running npm run profile (Nicholas C. Zakas)
* refactor variable name lookup into a separate function (Brandon Mills)
* optimize findVariable() in no-unused-vars (Brandon Mills)
* move to tests/bench (Chris Dickinson)
* add `npm run profile`. (Chris Dickinson)
* #586 refactor based on https://github.com/eslint/eslint/pull/590#discussion_r9476367 (Christian)
* #586 added no-unreachable jsdoc, documentation note on hoisting case (Christian)
* #586 add hoisting check to no-unreachable (Christian)
* readme: Remove stray asterisk (Timo Tijhof)
* #580 Remove eslint.getAllComments(), related docs, related tests (Christian)
* Added test for bug fix #582. Test Passes (Shmueli Englard)
* Added curly braces to if statment (Shmueli Englard)
* Added new test for fix to #582 (fixes 582) (Shmueli Englard)
* Bug #582: Added check if node.value isn't a string just exit (Shmueli Englard)
* Update Rule: implement curly options for single-statement bodies (fixes #511) (Nicholas C. Zakas)
* New Rule: no-extra-boolean-cast (fixes #557) (Brandon Mills)
* New Rule: no-sparse-arrays (fixes #499) (Nicholas C. Zakas)
* Fix: no-spaced-func is now an error (Nicholas C. Zakas)
* New Rule: no-process-exit (fixes #568) (Nicholas C. Zakas)
* New Rule: no-labels (fixes #550) (Nicholas C. Zakas)
* New Rule: no-lone-blocks (fixes #512) (Brandon Mills)
* Added Emacs/Flycheck integration (Nikolai Prokoschenko)
* Build: Add perf test (Nicholas C. Zakas)
* Fix: no-cond-assign shouldn't throw error when there's a for loop with an empty conditional (fixes #53) (Nicholas C. Zakas)
* Docs: Add docs for no-regex-spaces and all doc errors now break build (closes #562) (Nicholas C. Zakas)
* Rename: regex-spaces to no-regex-spaces (Nicholas C. Zakas)
* Docs: Add docs for no-underscore-dangle (refs #562) (Nicholas C. Zakas)
* Docs: Add docs for no-undef-init (refs #562) (Nicholas C. Zakas)
* Docs: Add docs for no-return-assign (refs #562) (Nicholas C. Zakas)
* Fix: Misspelling in no-return-assign message (Nicholas C. Zakas)
* Docs: Add docs for no-new-wrappers (refs #562) (Nicholas C. Zakas)
* Docs: Add docs for no-new-object (refs #562) (Nicholas C. Zakas)
* Docs: Add docs for no-implied-eval (refs #562) (Nicholas C. Zakas)
* Docs: Updated documentation for developing rules (Nicholas C. Zakas)
* Testing: Move ESLintTester to be external dependency (fixes #480) (Nicholas C. Zakas)
* Docs: Add list of known integrations (Nicholas C. Zakas)
* Fix #570 (dmp42)
* document no-array-constructor rule (Michael Ficarra)
* fixes #500: no-array-constructor should not flag 1-argument construction (Michael Ficarra)
* fixes #501: no-array-constructor recognises CallExpression form (Michael Ficarra)
* rename no-new-array rule to no-array-constructor; ref #501 (Michael Ficarra)
* Fix: Make radix rule warn on invalid second parameter (fixes #563) (Nicholas C. Zakas)
* Docs: Added no-floating-decimal docs (refs #562) (Nicholas C. Zakas)
* New Rule: no-path-concat (fixes #540) (Nicholas C. Zakas)
* Docs: Add some missing rule docs (refs #562) (Nicholas C. Zakas)
* Fix: CLI should not output anything when there are no warnings (fixes #558) (Nicholas C. Zakas)
* New Rule: no-yoda (fixes #504) (Nicholas C. Zakas)
* New Rule: consistent-return (fixes #481) (Nicholas C. Zakas)
* Rewrite configuration documentation to include information about globals (fixes #555) (Nicholas C. Zakas)
* Allow YAML configuration files (fixes #491) (Nicholas C. Zakas)
* 0.3.0 (Nicholas C. Zakas)

v0.3.0 - January 20, 2014

* 0.3.0 (Nicholas C. Zakas)
* Config: Allow comments in JSON configuration files (fixes #492) (Nicholas C. Zakas)
* Bug: max-len fix to report correct line number (fixes #552) (Nicholas C. Zakas)
* Build: Use browserify to create browser-ready ESLint (fixes #119) (Nicholas C. Zakas)
* Docs: Ensure all rules have entry on top-level rules index page (Nicholas C. Zakas)
* Docs: Add docs for no-fallthrough rule (Nicholas C. Zakas)
* Update README.md (Peter deHaan)
* Update README.md (Peter deHaan)
* Update package.json (Peter deHaan)
* Docs: Added documentation for semi rule (Nicholas C. Zakas)
* Build: Reset branch coverage target (Nicholas C. Zakas)
* Update build system to generate eslint.org during release (Nicholas C. Zakas)
* Updated setup doc (Nicholas C. Zakas)
* Fix #525 & #528 (Mangled Deutz)
* Improve no-negated-in-lhs description (David Bruant)
* Fixing typo (David Bruant)
* Update no-new.md (Tamas Fodor)
* Update no-extra-semi.md (Tamas Fodor)
* Fixing broken links in documentation (Ilya Volodin)
* Update about page (Nicholas C. Zakas)
* Site generation build step and documentation updates to support it (fixes #478) (Nicholas C. Zakas)
* Change message for brace-style rule (fixes #490) (Nicholas C. Zakas)
* Add question about ES6 support to FAQ (fixes #530) (Nicholas C. Zakas)
* Set unlimited number of listeners for event emitter (fixes #524) (Nicholas C. Zakas)
* Add support for comment events (fixes #531) Add :after events for comments (Nicholas C. Zakas)
* Add :after events for comments (Nicholas C. Zakas)
* Allow config files to have any name (fixes #486). (Aparajita Fishman)
* List available formatters (fixes #533). (Aparajita Fishman)
* Add support for comment events (fixes #531) (Nicholas C. Zakas)
* Add Stylish formatter and make it default. Fixes #517 (Sindre Sorhus)
* Fix missing code exit (Mangled Deutz)
* Added unit test for calling Config.getConfig with no arguments. (Aparajita Fishman)
* Typo (Mangled Deutz)
* Fixed docs typo (Nicholas C. Zakas)
* Mark functions as used when any method is called on them (Nicholas C. Zakas)
* Fixed: Config.getConfig is called either with a file path or with no args (fixes #520) (Aparajita Fishman)
* Fix minor bug in no-empty rule (Nicholas C. Zakas)
* add more info for failure messages (Nicholas C. Zakas)
* Add ruleId to all formatters output (fixes #472) (Nicholas C. Zakas)
* Remove unused code (Nicholas C. Zakas)
* Correctly handle case with both finally and catch in no-empty (Nicholas C. Zakas)
* Update documentation for no-unused-vars (Nicholas C. Zakas)
* Ensure that bound function expressions are reported as being used (fixes #510) (Nicholas C. Zakas)
* Allow empty catch/finally blocks (fixes #514) and update documentation (fixes #513) (Nicholas C. Zakas)
* Updated contribution guidelines (Nicholas C. Zakas)
* Add default setting for no-cond-assign (Nicholas C. Zakas)
* Add build step to check rule consistency (Nicholas C. Zakas)
* update docs: explicit cli args are exempt from eslintignore exclusions (Michael Ficarra)
* fixes #505: no-cond-assign should ignore doubly parenthesised tests (Michael Ficarra)
* Renamed unnecessary-strict to no-extra-strict (Nicholas C. Zakas)
* Fixed missing documentation links (Nicholas C. Zakas)
* Add build task to check for missing docs and tests for rules (Nicholas C. Zakas)
* Slight reorganization of rule groups (Nicholas C. Zakas)
* Added one-var and sorted some rules (Nicholas C. Zakas)
* Updated Travis badge for new location (Nicholas C. Zakas)
* fixes #494: allow shebangs in processed JS files (Michael Ficarra)
* fixes #496: lint ignored files when explicitly specified via the CLI (Michael Ficarra)
* More tests (Ilya Volodin)
* Upgrade Istanbul (Ilya Volodin)
* fixes #495: holey arrays cause no-comma-dangle rule to throw (Michael Ficarra)
* Documentation and minor changes (Ilya Volodin)
* Adding missing package registration (Ilya Volodin)
* Adding support for .eslintignore and .jshintignore (Closes #484) (Ilya Volodin)
* fixes #482: brace-style bug with multiline conditions (Michael Ficarra)
* Switching Travis to use ESLint (Closes #462) (Ilya Volodin)
* 0.2.0 (Nicholas C. Zakas)

v0.2.0 - January 1, 2014

* 0.2.0 (Nicholas C. Zakas)
* Bump code coverage checks (Nicholas C. Zakas)
* Take care of unreachable code in case statement (Nicholas C. Zakas)
* Updated rule messaging and added extra tests (Nicholas C. Zakas)
* Fixing eslint errors and unittests (Ilya Volodin)
* Rule: max-nested-callbacks (Ian Christian Myers)
* Fix fall-through rule with nested switch statements (fixes #430) (Nicholas C. Zakas)
* Fixed trailing comma (Nicholas C. Zakas)
* Added more tests for func-style (Nicholas C. Zakas)
* Fixed documentation for func-style (Nicholas C. Zakas)
* Fixed linting error (Nicholas C. Zakas)
* Rule to enforce function style (fixes #460) (Nicholas C. Zakas)
* Rule is off by default. Updated documentation (Ilya Volodin)
* Rule: sort variables. Closes #457 (Ilya Volodin)
* Update architecture.md (Nicholas C. Zakas)
* Change quotes option to avoid-escapes and update docs (fixes #199) (Brandon Payton)
* Add allow-avoiding-escaped-quotes option to quotes rule (fixes #199) (Brandon Payton)
* Update no-empty-class.md (Nicholas C. Zakas)
* Updated titles on all rule documentation (fixes #348) (Nicholas C. Zakas)
* Fixing eslint errors in codebase (Ilya Volodin)
* fixes #464: space-infix-ops checks for VariableDeclarator init spacing (Michael Ficarra)
* Add options to no-unused-vars. Fixes #367 (Ilya Volodin)
* rename escape function to xmlEscape in checkstyle formatter (Michael Ficarra)
* The semi rule now reports correct line number (Ian Christian Myers)
* context.report now takes optional location (Ian Christian Myers)
* fixes #454: escape values for XML in checkstyle formatter (Michael Ficarra)
* Add color to Mocha test reporting (Ian Christian Myers)
* Rule no-nested-ternary (Ian Christian Myers)
* Fixing no-unused-var and no-redeclare (Ilya Volodin)
* fixes #449: no-mixed-requires throws TypeError when grouping is enabled (Michael Ficarra)
* Fixed reported line number for trailing comma error (Ian Christian Myers)
* Update doc title for quote (Matthew DuVall)
* fixes #446: join paths without additional delimiters (Michael Ficarra)
* docs: add documentation for quotes rule (Matthew DuVall)
* minor style changes to lib/rules/space-infix-ops.js as requested in #444 (Michael Ficarra)
* remove "function invalid(){ return D }" from some tests (Michael Ficarra)
* fixes #429: require spaces around infix operators; enabled by default (Michael Ficarra)
* simplify fix for #442 (Michael Ficarra)
* Fix broken test, ensure tests get run before a release is pushed (Nicholas C. Zakas)
* 0.1.4 (Nicholas C. Zakas)

v0.1.4 - December 5, 2013

* 0.1.4 (Nicholas C. Zakas)
* Add release scripts to package.json (Nicholas C. Zakas)
* Fixed release error in Makefile (Nicholas C. Zakas)
* Fix JSHint warnings (Nicholas C. Zakas)
* Make sure 'default' isn't flagged by no-space-returns-throw rule (fixes #442) (Nicholas C. Zakas)
* Fixing documentation (Ilya Volodin)
* Fixing disabling rules with invalid comments Closes #435 (Ilya Volodin)
* improve assertion on wrong number of errors (Christoph Neuroth)
* fixes #431: no-unused-expressions should not flag statement level void (Michael Ficarra)
* fixes #437: fragile no-extend-native rule (Michael Ficarra)
* change space-* rule documentation headers to be more descriptive (Michael Ficarra)
* Moved to tabs, added comments, a few more tests (Jamund Ferguson)
* split GH-332 rule into space-unary-word-ops and space-return-throw-case (Michael Ficarra)
* fixes #346: validate strings passed to the RegExp constructor (Michael Ficarra)
* change some documentation extensions from js to md (Michael Ficarra)
* fixes #332: unary word operators must be followed by whitespace (Michael Ficarra)
* Add some docs (Jamund Ferguson)
* DRYing cli tests and improving code coverage (Ilya Volodin)
* fixes #371: add no-shadow-restricted-names rule (Michael Ficarra)
* Added Support for Object.defineProperty() checking (Jamund Ferguson)
* fixes #333: add rule to disallow gratuitously parenthesised expressions (Michael Ficarra)
* improve rule test coverage (Michael Ficarra)
* No Extend Native (Jamund Ferguson)
* change getTokens 2nd/3rd arguments to count tokens, not characters (Michael Ficarra)
* fixes #416: no-fallthrough flagging last case + reporting wrong line num (Michael Ficarra)
* fixes #415: fix unnecessary-strict rule false positives (Michael Ficarra)
* Add missing dependency (Nicholas C. Zakas)
* Update docs related to running unit tests (Nicholas C. Zakas)
* Add JSHint as missing dependency (Nicholas C. Zakas)
* Switch to using ShellJS makefile (fixes #418) (Nicholas C. Zakas)
* Updated documentation to reflect test changes (refs #417) (Nicholas C. Zakas)
* Change to eslintTester.addRuleTest (fixes #417) (Nicholas C. Zakas)
* Fix false positives for no-script-url (fixes #400) (Nicholas C. Zakas)
* Fix lint warning (Nicholas C. Zakas)
* Fixing ESLint warnings, introducing Makefile.js (not yet wired in) (Nicholas C. Zakas)
* fixes #384: include builtin module list to avoid repl dependency (Michael Ficarra)
* 0.1.3 (Nicholas C. Zakas)

v0.1.3 - November 25, 2013

* 0.1.3 (Nicholas C. Zakas)
* Updated changelog (Nicholas C. Zakas)
* Vows is gone. Mocha is now default (Ilya Volodin)
* fixes #412: remove last remaining false positives in no-spaced-func (Michael Ficarra)
* fixes #407: no-spaced-func rule flagging non-argument-list spaced parens (Michael Ficarra)
* Add no-extra-semi to configuration (fixes #386) (Nicholas C. Zakas)
* Converting formatter tests and core (Ilya Volodin)
* Don't output anything when there are no errors in compact formatter (fixes #408) (Nicholas C. Zakas)
* Removing Node 0.11 test - it fails all the time (Nicholas C. Zakas)
* Completing conversion of rule's tests to mocha (Ilya Volodin)
* added mocha conversion tests for strict, quote-props and one-var; enhanced one of the invalid one-var tests that was expecting two messages (Michael Paulukonis)


v0.1.2 - November 23, 2013

* 0.1.2 (Nicholas C. Zakas)
* added mocha tests for radix and quotes; fixed some of the internals on quotes from vows annotations (Michael Paulukonis)
* added tests for regex-spaces, strict, unnecessary-strict; fixed some types in overview/author notes in other tests. (Michael Paulukonis)
* Converting unittests to mocha (Ilya Volodin)
* mocha conversions of tests for 'use-isnan' and 'wrap-iife' (Michael Paulukonis)
* added mocha tests semi.js and wrap-regex.js (Michael Paulukonis)
* Converting more tests to mocha (Ilya Volodin)
* Update CONTRIBUTING.md (Nicholas C. Zakas)
* Cleaning up eslintTester (Ilya Volodin)
* DRYing unittests and converting them to mocha (Ilya Volodin)
* Reformatted Gruntfile (Nicholas C. Zakas)
* Add tests to config load order: base, env, user. (icebox)
* Fixing indent in gruntfile (Ilya Volodin)
* Removing jake, adding Grunt, Travis now runs grunt (Ilya Volodin)
* Add rules per environments to config. (icebox)
* Add globals property to the environments. (icebox)
* Fix error about IIFE if the function is in a new (Marsup)
* Fix a broken link in the docs (Brian J Brennan)
* Add test coverage for additional cases, fix open paren at beginning of expr (Matthew DuVall)
* Fixing no-undef for eval use case (Ilya Volodin)
* fixes #372: disallow negated left operand in `in` operator (Michael Ficarra)
* Fixing no-self-compare rule to check for operator (Ilya Volodin)
* bug: open parens in args causes no-spaced-func to trigger (Matthew DuVall)
* fixes #369: restrict UnaryExpressions to delete in no-unused-expressions (Michael Ficarra)
* Make sure delete operator isn't flagged as unused expression (fixes #364) (Nicholas C. Zakas)
* Don't flag ++ or -- as unused expressions (fixes #366) (Nicholas C. Zakas)
* Ensure that 'use strict' isn't flagged as an unused expression (fixes #361) (Nicholas C. Zakas)
* Increase test coverage for strict-related rules (refs #361) (Nicholas C. Zakas)
* Up code coverage numbers (Nicholas C. Zakas)
* Fixes error in new-cap rule when 'new' is used without a constructor (fixes #360) (Nicholas C. Zakas)
* added files array in package json (Christian)
* removed unused jshint dependency (Christian)
* Add test coverage for new Foo constructor usage (Matt DuVall)
* Pull code coverage up by removing unused method (Matt DuVall)
* recognise CallExpression variant of RegExp ctor in no-control-regex rule (Michael Ficarra)
* Merge smart-eqeqeq into eqeqeq (Matt DuVall)
* Catch additional cases for a.b, new F, iife (Matt DuVall)
* 0.2.0-dev (Nicholas C. Zakas)
* Version 0.1.0 (Nicholas C. Zakas)
* rule: no-spaced-func disallow spaces between function identifier and application (Matt DuVall)

v0.1.1 - November 09, 2013

* Ensure mergeConfigs() doesn't thrown an error when keys are missing in base config (fixes #358) (Nicholas C. Zakas)

v0.1.0 - November 03, 2013

* Version 0.1.0 (Nicholas C. Zakas)
* Updated Readme for v0.1.0 (Nicholas C. Zakas)
* Bump code coverage verification to 95% across the board (Nicholas C. Zakas)
* Fixed broken links (Nicholas C. Zakas)
* Added information about runtime rules (Nicholas C. Zakas)
* Added documentation about configuration files (Nicholas C. Zakas)
* Added description of -v option (Nicholas C. Zakas)
* Updated architecture documentation (Nicholas C. Zakas)
* Fix bug in no-control-regex (fixes #347) (Nicholas C. Zakas)
* Fix link to architecture doc in readme (azu)
* Rule: No control characters in regular expressions (fixes #338) (Nicholas C. Zakas)
* Add escaping \= test (Matt DuVall)
* Add docs for rule (Matt DuVall)
* rule: no-div-regex for catching ambiguous division operators in regexes (Matt DuVall)
* Change context-var to block-scoped-var (Matt DuVall)
* Implement config.globals (Oleg Grenrus)
* Add 'config-declared global' test (Oleg Grenrus)
* Adding ability to separate rules with comma (Ilya Volodin)
* Added rule for missing 'use strict' (fixes #321) (Nicholas C. Zakas)
* Fixing unittests and finishing code (Ilya Volodin)
* Disabling/enabling rules through comments (Ilya Volodin)
* Rename rule to context-var and add documentation (Matt DuVall)
* Added link to no-global-strict doc in readme (Nicholas C. Zakas)
* Add try-catch scoping with tests (Matt DuVall)
* Fix linting error (Matt DuVall)
* Store FunctionDeclarations in scope as they can be used as literals (Matt DuVall)
* Fix to use getTokens and add test for MemberExpression usage (Matt DuVall)
* rule: block-scope-var to check for variables declared in block-scope (Matt DuVall)
* no-unused-expressions rule: add test and doc mention for `a && b()` (Michael Ficarra)
* rule: wrap-regex for parens around regular expression literals (Matt DuVall)
* fixes #308: implement no-unused-expressions rule; ref. jshint rule W030 (Michael Ficarra)
* Updated change log script to filter out merge messages (Nicholas C. Zakas)
* Updated changelog (Nicholas C. Zakas)
* 0.1.0-dev (Nicholas C. Zakas)

v0.0.9 - October 5, 2013

* Version 0.0.9 release (Nicholas C. Zakas)
* Added rule for no global strict mode (fixes #322) (Nicholas C. Zakas)
* Change default on to be errors instead of warnings (fixes #326) (Nicholas C. Zakas)
* Fixed bug where JSHint was using the wrong file in lint task (Nicholas C. Zakas)
* Updated docs for no-unused vars rule. (Andrew de Andrade)
* Removed console.log in tests. (Andrew de Andrade)
* Added link to roadmap and JSHint feature parity list. (Andrew de Andrade)
* Fixed warning when unused var declared as param in FunctionExpression/Declaration can be ignored because later param is used (Andrew de Andrade)
* Rename test for smartereqeqeq.js to smarter-eqeqeq.js (Andrew de Andrade)
* Keep test filename inline with rule name (Andrew de Andrade)
* Added further instructions for multiline test cases. (Andrew de Andrade)
* Protecting private method (Seth McLaughlin)
* Updating look up algorithm for local config files (Seth McLaughlin)
* Fixing ESLint errors (Ilya Volodin)
* Implemented local default config file (Seth McLaughlin)
* Upgrading escope version and fixing related bugs (Ilya Volodin)
* Fixing assignment during initialization issue (Ilya Volodin)
* add plain-English regexp description to no-empty-class rule (Michael Ficarra)
* fixes #289: no-empty-class flags regexps with... flags (Michael Ficarra)
* Rule: no-catch-shadow (Ian Christian Myers)
* Update no-empty for compatibility with esprima@1.0.4 (fixes #290) (Mark Macdonald)
* Fixing bug with _ in MemberExpression (Ilya Volodin)
* Rule: no-func-assign (Ian Christian Myers)
* Fix false warning from no-undef rule (fixes #283) (Mark Macdonald)
* Adding eslint to jake (Ilya Volodin)
* Rule no redeclare (Ilya Volodin)
* Fixing no use before define issues (Ilya Volodin)
* Rule: no-octal-escape (Ian Christian Myers)
* Fix for `no-proto` and `no-iterator` false positive (Ian Christian Myers)
* Rule: no-iterator (Ian Christian Myers)
* Fixing type in guard-for-in documentation (Ilya Volodin)
* Rule No use before define (Ilya Volodin)
* Added documentation for the `no-new` rule (Ian Christian Myers)
* Added documentation for the `no-eval` rule (Ian Christian Myers)
* Added documentation for the `no-caller` rule (Ian Christian Myers)
* Added documentation for the `no-bitwise` rule (Ian Christian Myers)
* simplify no-empty-class rule (Michael Ficarra)
* Fix `no-empty-class` false negatives (Ian Christian Myers)
* Added documentation for the `no-alert` rule (Ian Christian Myers)
* Added documentation for the `new-parens` rule (Ian Christian Myers)
* Added documentation for the `max-params` rule (Ian Christian Myers)
* Added documentation for `max-len` rule (Ian Christian Myers)
* Created link from rules README.md to no-plusplus.md documentation (Ian Christian Myers)
* Added documentation for `guard-for-in` rule (Ian Christian Myers)
* Added documentation for `dot-notation` rule (Ian Christian Myers)
* Added documentation for `curly` rule (Ian Christian Myers)
* Updated `camelcase` rule documentation (Ian Christian Myers)
* Added documentation for `complexity` rule (Ian Christian Myers)
* Changed `no-dangle` documentation to `no-comma-dangle` (Ian Christian Myers)
* Rule: no-empty-class (Ian Christian Myers)
* Increased test coverage for max-depth (Ian Christian Myers)
* Increased test coverage for no-shadow (Ian Christian Myers)
* Increased test coverage on no-mixed-requires (Ian Christian Myers)
* Added docs for eqeqeq and no-with (fixes #262) (Raphael Pigulla)
* Create camelcase.md (Micah Eschbacher)
* Fix issues with function in no-unused-vars (Ilya Volodin)
* Rule: No shadow (Ilya Volodin)
* fixes #252: semi rule errors on VariableDeclarations in ForInStatements (Michael Ficarra)
* rule: max-len to lint maximum length of a line (Matt DuVall)
* Fixes #249 (Raphael Pigulla)
* Merge branch 'master' of https://github.com/beardtwizzle/eslint (Jonathan Mahoney)
* Re-add lines that were accidentally deleted from config (Jonathan Mahoney)
* Add support for pre-defined environment globals (re: #228) (Jonathan Mahoney)
* Rule: no-else-return (Ian Christian Myers)
* Re-add lines that were accidentally deleted from config (Jonathan Mahoney)
* Add support for pre-defined environment globals (re: #228) (Jonathan Mahoney)
* Fix no-unused-vars to report correct line numbers (Ilya Volodin)
* Rule: no proto (Ilya Volodin)
* Rule: No Script URL (Ilya Volodin)
* Rule: max-depth (Ian Christian Myers)
* Fix: Error severity for rules with options. (Ian Christian Myers)
* Rule: No wrap func (Ilya Volodin)
* bug: Fixes semi rule for VariableDeclaration in ForStatement (Matt DuVall)
* Individual perf tests for rules (Ilya Volodin)
* Fix loading rules from a rules directory (Ian Christian Myers)
* Rule no-mixed-requires (fixes #221) (Raphael Pigulla)
* bug: Add ForStatement for no-cond-assign check (Matthew DuVall)
* JSLint XML formatter now escapes special characters in the evidence and reason attributes. (Ian Christian Myers)
* Formatter: JSLint XML (Ian Christian Myers)
* Refactored `max-statements` rule. (Ian Christian Myers)
* Fix tests broken due to new rule message text (James Allardice)
* Merge branch 'master' into match-jshint-messages (James Allardice)
* Refactored `one-var` rule. (Ian Christian Myers)
* split eslint.define into eslint.defineRule and eslint.defineRules (Michael Ficarra)
* Removed unnecessary rules.js test. (Ian Christian Myers)
* Rule: one-var (Ian Christian Myers)
* Rule: No unused variables (Ilya Volodin)
* expose interface for defining new rules at runtime without fs access (Michael Ficarra)
* disallow 00 in no-octal rule (Michael Ficarra)
* Increased test coverage for `lib/cli.js`. (Ian Christian Myers)
* Increased test coverage for `lib/rules.js` (Ian Christian Myers)
* Increased test coverage for jUnit formatter. (Ian Christian Myers)
* scripts/bundle: output bundle+map to /build directory (Michael Ficarra)
* add test for 0X... hex literals in no-octal tests (Michael Ficarra)
* fixes #200: no-octals should not see leading-0 floats as violations (Michael Ficarra)
* add back tests for loading rules from a directory (Michael Ficarra)
* add back in ability to load rules from a directory (Michael Ficarra)
* Increased test coverage for `complexity` rule. (Ian Christian Myers)
* Increased test coverage for `max-params` rule. (Ian Christian Myers)
* also output source map when generating bundle (Michael Ficarra)
* Rule: unnecessary-strict (Ian Christian Myers)
* Improve performance of getTokens (Ilya Volodin)
* Performance jake task (Ilya Volodin)
* don't force explicit listing of rules; generate listing for bundle (Michael Ficarra)
* Rule: no-dupe-keys (Ian Christian Myers)
* fixes #145: create a browser bundle (Michael Ficarra)
* Fixing no-caller bug (Ilya Volodin)
* Check for use of underscore library as an exception for var declarations (Matthew DuVall)
* Merge branch 'master' of https://github.com/nzakas/eslint into no-underscore-dangle (Matthew DuVall)
* Fixing spelling (Ilya Volodin)
* Rule: no-empty-label (Ilya Volodin)
* Add builtin globals to the global scope (fixes #185) (Mark Macdonald)
* Rule: no-loop-func (Ilya Volodin)
* Merge branch 'master' of https://github.com/nzakas/eslint into no-underscore-dangle (Matt DuVall)
* Use proper node declarations and __proto__ exception (Matt DuVall)
* Updating no-undef patch (see pull request #164) - Simplify parseBoolean() - Make knowledge of```/*jshint*/``` and ```/*global */``` internal to eslint object - Put user-declared globals in Program scope (Mark Macdonald)
* Rule: no-eq-null (Ian Christian Myers)
* fixed broken merge (Raphael Pigulla)
* fixes #143 (Raphael Pigulla)
* added consistent-this rule (Raphael Pigulla)
* Rule: no-sync to encourage async usage (Matt DuVall)
* Update eslint.json with no-underscore-dangle rule (Matt DuVall)
* Rule: no-underscore-dangle for func/var declarations (Matt DuVall)
* Warn on finding the bitwise NOT operator (James Allardice)
* Updating no-undef patch (see pull request #164) 3. Move parsing of ```/*global */``` and ```/*jshint */``` to eslint.js (Mark Macdonald)
* Warn on finding a bitwise shift operator (fixes #170) (James Allardice)
* Fix broken test (James Allardice)
* Add support for the do-while statement to the curly rule (closes #167) (James Allardice)
* Removing nasty leading underscores (Patrick Brosset)
* Added tests and test cases for a few files (Patrick Brosset)
* CLI: -f now accepts a file path (Ian Christian Myers)
* Updating no-undef patch (see pull request #164) 1. Move predefined globals to ```conf/environments.json``` 2. Move mixin() to ```lib/util.js``` (Mark Macdonald)
* Match messages to JS[LH]int where appropriate, and ensure consistent message formatting (closes #163) (James Allardice)
* Add support for the do-while statement to the curly rule (closes #167) (James Allardice)
* Removing nasty leading underscores (Patrick Brosset)
* Added tests and test cases for a few files (Patrick Brosset)
* Merge branch 'master' of github.com:nzakas/jscheck (Nicholas C. Zakas)
* Added acceptance criteria for rules to docs (Nicholas C. Zakas)
* Add no-undef (fixes #6) (Mark Macdonald)
* Fixing no-self-compare (Ilya Volodin)
* Rule: No multiline strings (Ilya Volodin)
* CLI refactor to remove process.exit(), file not found now a regular error message, updated formatters to handle this case (Nicholas C. Zakas)
* Rule: no-self-compare (Ilya Volodin)
* Rule: No unnecessary semicolons (fixes #158) (Nicholas C. Zakas)
* Fixed error in no-ex-assign when return statement as found in catch clause (Nicholas C. Zakas)
* Rename no-exc-assign to no-ex-assign and add to config (Nicholas C. Zakas)
* Renamed count-spaces to regex-spaces (Nicholas C. Zakas)
* Documentation updates (Nicholas C. Zakas)
* Put all rules into strict mode and update docs accordingly (Nicholas C. Zakas)
* Merge branch 'master' of github.com:nzakas/jscheck (Nicholas C. Zakas)
* Ensure getScope() works properly when called from Program node (fixes #148) (Nicholas C. Zakas)
* Rule: wrap-iife (Ilya Volodin)
* add additional test for no-cond-assign rule (Stephen Murray)
* Merge branch 'master' of github.com:nzakas/jscheck (Nicholas C. Zakas)
* Experimental support for Jake as a build system (fixes #151) (Nicholas C. Zakas)
* fixes #152 (Stephen Murray)
* add docs for no-exc-assign (Stephen Murray)
* Merge branch 'master' of https://github.com/nzakas/eslint into no-new-object-array-literals (Matt DuVall)
* Merge branch 'master' of https://github.com/nzakas/eslint into count-spaces (Matt DuVall)
* Added a test for getting global scope from Program node (refs #148) (Nicholas C. Zakas)
* Add positive test case for `object.Array` (Matthew DuVall)
* Only support space characters for repetitions (Matthew DuVall)
* fix line length per code conventions (Stephen Murray)
* fix indentation per code conventions (Stephen Murray)
* fixes #149 (Stephen Murray)
* Rule: no-ternary (Ian Christian Myers)
* Check that the return statement has an argument before checking its type (James Allardice)
* Rule: count-spaces for multiple spaces in regular expressions (Matt DuVall)
* Update eslint.json configuration file for literal rules (Matt DuVall)
* Created no-label-var rule. (Ian Christian Myers)
* Rule: no-new-array and no-new-object (Matt DuVall)
* Added ability to retrieve scope using escope. (Ian Christian Myers)
* Corrected unused arguments (Patrick Brosset)
* Reporting function complexity on function:after and using array push/pop to handle nesting (Patrick Brosset)
* Fixing style issues discovered while npm testing (Patrick Brosset)
* First draft proposal for a cyclomatic complexity ESLint rule (Patrick Brosset)
* Corrected file extension on no-plusplus rule documentation. (Ian Christian Myers)
* Documentation for no-delete-var rule. Closes #129 (Ilya Volodin)
* Rule: max-statements (Ian Christian Myers)
* Better documentation for the `no-plusplus` rule. (Ian Christian Myers)
* Rule: no-plusplus (Ian Christian Myers)
* Rule: no assignment in return statement (Ilya Volodin)
* Updating max-params rule name (Ilya Volodin)
* Rule: Function has too many parameters (Ilya Volodin)
* Removing merge originals (Ilya Volodin)
* Rebasing on master (Ilya Volodin)
* Rule: Variables should not be deleted (Ilya Volodin)
* Fixes incorrect reporting of missing semicolon (Ian Christian Myers)
* Rebase against master branch (Mathias Bynens)
* Rule to warn on use of Math and JSON as functions (James Allardice)
* Formatter: Checkstyle (Ian Christian Myers)
* docs: Clean up structure (Mathias Bynens)
* Merging no-native-reassign and no-redefine (Ilya Volodin)
* Rule: no native reassignment (Ilya Volodin)
* 0.0.8-dev (Nicholas C. Zakas)
* v0.0.7 released (Nicholas C. Zakas)
* Updated Tests, etc. (Jamund Ferguson)
* Added jUnit Support (Fixes #16) (Jamund Ferguson)

v0.0.7 - July 22, 2013

* 0.0.7 (Nicholas C. Zakas)
* Add code coverage checks to npm test and update rule tests to have better coverage (Nicholas C. Zakas)
* Fixed CLI output on serial programatic executions (Ian Christian Myers)
* Removes line length from code style convention docs (Josh Perez)
* Adds escapeRegExp and fixes documentation (Josh Perez)
* Add quotes rule and test coverage for configuration options (Matt DuVall)
* Adds templating for lint messages and refactors rules to use it (Josh Perez)
* Fixes lint rules for unchecked test file (Josh Perez)
* Changes dotnotation rule to match JSHint style (Josh Perez)
* Change configInfo to options and add test coverage (Matt DuVall)
* Merge branch 'master' of https://github.com/nzakas/eslint into optional-args-for-rule (Matt DuVall)
* Adds dot notation lint rule (Josh Perez)
* Strip trailing underscores in camelcase rule - Fixes #94 (Patrick Brosset)
* add mailing list link (Douglas Campos)
* Strip leading underscores in camelcase rule - Fixes #94 (Patrick Brosset)
* Created no-dangle rule. (Ian Christian Myers)
* Fixed rule name (James Allardice)
* Make sure the callee type is Identifier (James Allardice)
* Add rule for implied eval via setTimeout/Interval (James Allardice)
* Fix rule name in config (James Allardice)
* Fixes #90 -- updates docstrings (Stephen Murray)
* Fixes issue with fs.existsSync on NodeJS 0.6 (Ian Christian Myers)
* Fixing -c config option. (Ian Christian Myers)
* Allow arrays to be passed as multiple args to rule (Matt DuVall)
* Test to make sure empty case with one line break is safe (Matt DuVall)
* Rule: The Function constructor is eval (Ilya Volodin)
* Enabled require("eslint") and exposed out CLI. (Ian Christian Myers)
* Adds test and fix for issue #82 (Mark Macdonald)
* Merge branch 'master' of https://github.com/nzakas/eslint into ok (Yusuke Suzuki)
* Created brace-style rule. (Ian Christian Myers)
* Formatters can now process multiple files at once (Jamund Ferguson)
* Rule: Do not use 'new' for side effects (Ilya Volodin)
* Adds smarter-eqeqeq rule (Josh Perez)
* Add EditorConfig file for consistent editor/IDE behavior (Jed Hunsaker)
* Fix the positive case for no-unreachable where there is no return statement at all, or if the return is at the end. Those cases should not return any errors. The error condition was not be checked before throwing the rule error. (Joel Feenstra)
* Adds test and fix for no-octal on 0 literal (Mark Macdonald)
* Don't report no-empty warnings when a parent is FunctionExpression / FunctionDeclaration (Yusuke Suzuki)
* Add api.getAncestors (Yusuke Suzuki)
* Ensure estraverse version 1.2.0 or later (Yusuke Suzuki)
* Fixes no-alert lint rule for non identifier calls (Josh Perez)
* Fixes exception when init is null (Josh Perez)
* Fixes no-octal check to only check for numbers (Josh Perez)
* 0.0.7-dev (Nicholas C. Zakas)
* 0.0.6 (Nicholas C. Zakas)
* Follow the rule naming conventions (James Allardice)
* Add rule for missing radix argument to parseInt (James Allardice)
* Allow return, falls-through comment, and throw for falls-through (Matt DuVall)
* Merge branch 'master' of https://github.com/nzakas/eslint into rule-fall-through (Matt DuVall)
* Globals are not good, declare len (Matt DuVall)
* Rule to add no-fall-through (Matt DuVall)

v0.0.6 - July 16, 2013

* 0.0.6 (Nicholas C. Zakas)
* Changed semi rule to use tokens instead of source (Nicholas C. Zakas)
* Renaming new-parens rule (Ilya Volodin)
* Renaming no-new-wrappers rule and adding tests (Ilya Volodin)
* Add license URL (Nick Schonning)
* Remove unused sinon requires (Nick Schonning)
* Remove redundant JSHint directives (Nick Schonning)
* Rule: Do not use constructor for wrapper objects (Ilya Volodin)
* Test node 0.11 unstable but allow it to fail (Nick Schonning)
* Rule: Constructor should use parentheses (Ilya Volodin)
* Fix reference to "CSS Lint" in Contributing documentation (Brian McKenna)
* Add git attributes file for line endings (Andy Hu)
* Rename to create an 'index' file in GH web view (Evan Goer)
* Avoid accidentally creating a markdown link (Evan Goer)
* Add headings and correct internal links (Evan Goer)
* Add wiki files to docs directory (Evan Goer)
* Add rules for leading/trailing decimal points (James Allardice)
* Add rule to prevent comparisons with value NaN (James Allardice)
* Fixing jshint error (Ilya Volodin)
* Rule: no octal literals (Ilya Volodin)
* Rule: no undefined when initializing variables (Ilya Volodin)
* Updated CONTRIBUTING.md (Nicholas C. Zakas)
* Make sure namespaces are honored in new-cap (Nicholas C. Zakas)
* Make sure no-empty also checks for ';;' (Nicholas C. Zakas)
* Add CLI option to output version (Nicholas C. Zakas)
* Updated contribution guidelines (Nicholas C. Zakas)
* Fixing jshint complaints. (Joel Feenstra)
* Converting to a switch statement and declaring variables. (Joel Feenstra)
* Added .jshintrc file (until ESLint can lint itself) and cleaned up JSHint warnings (Nicholas C. Zakas)
* Merge branch 'master' of github.com:nzakas/jscheck (Nicholas C. Zakas)
* A bit of cleanup (Nicholas C. Zakas)
* Add unreachable code detection for switch cases and after continue/break. (Joel Feenstra)
* Add support for detecting unreachable code after a throw or return statement. (Joel Feenstra)
* Fix curly brace check when an if statement is the alternate. (Joel Feenstra)
* Check for empty switch statements with no cases. (Matt DuVall)
* Added CONTRIBUTING.md (Nicholas C. Zakas)
* Added rule to check for missing semicolons (fixes #9) (Nicholas C. Zakas)
* Verify that file paths exist before reading the file (Matt DuVall)
* Added guard-for-in rule (fixes #1) (Nicholas C. Zakas)
* Run linting with npm test as well (Nicholas C. Zakas)
* Removed foo.txt (Nicholas C. Zakas)
* Updated config file with new no-caller ID (Nicholas C. Zakas)
* Changed name of no-arg to no-caller (Nicholas C. Zakas)
* Increased test coverage (Nicholas C. Zakas)
* Got npm test to work with istanbul, huzzah\! (Nicholas C. Zakas)
* Moved /config to /conf (Nicholas C. Zakas)
* Added script to auto-generate changelog (Nicholas C. Zakas)
* Add `quote-props` rule (Mathias Bynens)
* Cleaned up relationship between bin/eslint, lib/cli.js, and lib/eslint.js (Nicholas C. Zakas)
* Add problem count to compact formatter (Nicholas C. Zakas)
* Fix merge conflict (Nicholas C. Zakas)
* Change reporters to formatters, add format command line option. Also added tests for compact format. (Nicholas C. Zakas)
* Change reporters to formatters, add format command line option (Nicholas C. Zakas)
* Start development of 0.0.6-dev (Nicholas C. Zakas)
