<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>BTC/USDT Price Prediction Dashboard</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
  <link rel="stylesheet" href="/static/css/style.css">
</head>

<body>
  <nav class="navbar navbar-dark bg-dark">
    <div class="container-fluid">
      <span class="navbar-brand mb-0 h1">
        <i class="fab fa-bitcoin text-warning"></i>
        BTC/USDT Price Prediction Dashboard
      </span>
      <div class="d-flex">
        <span class="navbar-text me-3" id="lastUpdate">
          Last Update: <span id="updateTime">Loading...</span>
        </span>
        <button class="btn btn-outline-light btn-sm" onclick="refreshData()">
          <i class="fas fa-sync-alt"></i> Refresh
        </button>
      </div>
    </div>
  </nav>

  <div class="container-fluid mt-4">
    <!-- Current Price Section -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="card bg-primary text-white">
          <div class="card-body">
            <div class="row align-items-center">
              <div class="col-md-4">
                <h2 class="card-title mb-0">
                  <i class="fab fa-bitcoin"></i> BTC/USDT
                </h2>
                <h1 class="display-4 mb-0" id="currentPrice">$0.00</h1>
                <small class="text-light" id="priceTimestamp">Loading...</small>
              </div>
              <div class="col-md-4">
                <div class="row text-center">
                  <div class="col-6">
                    <h6>24h Change</h6>
                    <span class="h5" id="change24h">-</span>
                  </div>
                  <div class="col-6">
                    <h6>Volume</h6>
                    <span class="h5" id="volume24h">-</span>
                  </div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="text-end">
                  <button class="btn btn-light btn-sm me-2" onclick="retrainModels()">
                    <i class="fas fa-brain"></i> Retrain Models
                  </button>
                  <button class="btn btn-outline-light btn-sm" onclick="updateData()">
                    <i class="fas fa-database"></i> Update Data
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Control Panel -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h5 class="mb-0">
              <i class="fas fa-cogs"></i> Control Panel
            </h5>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-3">
                <label for="modelSelect" class="form-label">Select Model:</label>
                <select class="form-select" id="modelSelect" onchange="loadPredictions()">
                  <option value="NeuralNetwork">Neural Network</option>
                  <option value="RandomForest">Random Forest</option>
                  <option value="GradientBoosting">Gradient Boosting</option>
                </select>
              </div>
              <div class="col-md-3">
                <label for="predictionHours" class="form-label">Prediction Hours:</label>
                <select class="form-select" id="predictionHours" onchange="loadHourlyPredictions()">
                  <option value="24">Next 24 Hours</option>
                  <option value="12">Next 12 Hours</option>
                  <option value="6">Next 6 Hours</option>
                </select>
              </div>
              <div class="col-md-3">
                <label for="autoRefresh" class="form-label">Auto Refresh:</label>
                <select class="form-select" id="autoRefresh" onchange="toggleAutoRefresh()">
                  <option value="300">5 Minutes</option>
                  <option value="60">1 Minute</option>
                  <option value="0">Disabled</option>
                </select>
              </div>
              <div class="col-md-3">
                <label class="form-label">Actions:</label>
                <div>
                  <button class="btn btn-success btn-sm me-1" onclick="exportPredictions()">
                    <i class="fas fa-download"></i> Export
                  </button>
                  <button class="btn btn-info btn-sm" onclick="showModelMetrics()">
                    <i class="fas fa-chart-line"></i> Metrics
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Hourly Predictions Chart -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h5 class="mb-0">
              <i class="fas fa-chart-line"></i> Hourly Price Predictions (Next 24 Hours)
            </h5>
          </div>
          <div class="card-body">
            <canvas id="hourlyPredictionChart" height="100"></canvas>
          </div>
        </div>
      </div>
    </div>

    <!-- Predictions Section -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="card">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
              <i class="fas fa-crystal-ball"></i> Price Predictions
            </h5>
            <div>
              <select class="form-select form-select-sm" id="modelSelect" onchange="updatePredictions()">
                <option value="NeuralNetwork">Neural Network</option>
                <option value="RandomForest">Random Forest</option>
                <option value="GradientBoosting">Gradient Boosting</option>
              </select>
            </div>
          </div>
          <div class="card-body">
            <div class="row" id="predictionsContainer">
              <!-- Predictions will be loaded here -->
            </div>
            <div class="mt-3">
              <small class="text-muted">
                <i class="fas fa-info-circle"></i>
                Predictions are based on historical data and technical indicators.
                Cryptocurrency markets are highly volatile and unpredictable.
              </small>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Charts Section -->
    <div class="row mb-4">
      <div class="col-lg-8">
        <div class="card">
          <div class="card-header">
            <h5 class="mb-0">
              <i class="fas fa-chart-line"></i> Price Chart (7 Days)
            </h5>
          </div>
          <div class="card-body">
            <canvas id="priceChart" height="400"></canvas>
          </div>
        </div>
      </div>
      <div class="col-lg-4">
        <div class="card">
          <div class="card-header">
            <h5 class="mb-0">
              <i class="fas fa-chart-bar"></i> Model Performance
            </h5>
          </div>
          <div class="card-body">
            <canvas id="performanceChart" height="400"></canvas>
          </div>
        </div>
      </div>
    </div>

    <!-- Model Information Section -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h5 class="mb-0">
              <i class="fas fa-robot"></i> Model Information
            </h5>
          </div>
          <div class="card-body">
            <div class="row" id="modelInfoContainer">
              <!-- Model info will be loaded here -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Loading Modal -->
  <div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog modal-sm modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-body text-center">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
          <p class="mt-2 mb-0">Loading predictions...</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Retrain Models Modal -->
  <div class="modal fade" id="retrainModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">
            <i class="fas fa-brain"></i> Retrain Models
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-md-6">
              <h6>Select Models to Retrain:</h6>
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="retrainNN" checked>
                <label class="form-check-label" for="retrainNN">Neural Network</label>
              </div>
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="retrainRF" checked>
                <label class="form-check-label" for="retrainRF">Random Forest</label>
              </div>
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="retrainGB" checked>
                <label class="form-check-label" for="retrainGB">Gradient Boosting</label>
              </div>
            </div>
            <div class="col-md-6">
              <h6>Training Options:</h6>
              <div class="mb-3">
                <label for="trainingRecords" class="form-label">Training Records:</label>
                <select class="form-select" id="trainingRecords">
                  <option value="20000">20,000 (Recommended)</option>
                  <option value="15000">15,000</option>
                  <option value="10000">10,000</option>
                  <option value="5000">5,000</option>
                </select>
              </div>
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="updateData" checked>
                <label class="form-check-label" for="updateData">Update data before training</label>
              </div>
            </div>
          </div>
          <div class="mt-3">
            <div class="alert alert-info">
              <i class="fas fa-info-circle"></i>
              <strong>Note:</strong> Retraining will take 5-10 minutes. The dashboard will continue to work with current
              models during training.
            </div>
          </div>
          <div id="retrainProgress" class="mt-3" style="display: none;">
            <div class="progress">
              <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%">
              </div>
            </div>
            <div class="mt-2 text-center">
              <small id="retrainStatus">Preparing...</small>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="button" class="btn btn-primary" onclick="startRetraining()">
            <i class="fas fa-play"></i> Start Retraining
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Model Metrics Modal -->
  <div class="modal fade" id="metricsModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">
            <i class="fas fa-chart-line"></i> Model Performance Metrics
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
          <div id="metricsContent">
            <div class="text-center">
              <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading metrics...</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
  <script src="/static/js/main.js"></script>
</body>

</html>