<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>BTC/USDT Price Prediction Dashboard</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
  <link rel="stylesheet" href="/static/css/style.css">
</head>

<body>
  <nav class="navbar navbar-dark bg-dark">
    <div class="container-fluid">
      <span class="navbar-brand mb-0 h1">
        <i class="fab fa-bitcoin text-warning"></i>
        BTC/USDT Price Prediction Dashboard
      </span>
      <div class="d-flex">
        <span class="navbar-text me-3" id="lastUpdate">
          Last Update: <span id="updateTime">Loading...</span>
        </span>
        <button class="btn btn-outline-light btn-sm" onclick="refreshData()">
          <i class="fas fa-sync-alt"></i> Refresh
        </button>
      </div>
    </div>
  </nav>

  <div class="container-fluid mt-4">
    <!-- Current Price Section -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="card bg-primary text-white">
          <div class="card-body">
            <div class="row align-items-center">
              <div class="col-md-6">
                <h2 class="card-title mb-0">
                  <i class="fab fa-bitcoin"></i> BTC/USDT
                </h2>
                <h1 class="display-4 mb-0" id="currentPrice">$0.00</h1>
                <small class="text-light" id="priceTimestamp">Loading...</small>
              </div>
              <div class="col-md-6">
                <div class="row text-center">
                  <div class="col-4">
                    <h5>24h Change</h5>
                    <span class="h4" id="change24h">-</span>
                  </div>
                  <div class="col-4">
                    <h5>Volume</h5>
                    <span class="h4" id="volume24h">-</span>
                  </div>
                  <div class="col-4">
                    <h5>Market Cap</h5>
                    <span class="h4">$1.9T</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Predictions Section -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="card">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
              <i class="fas fa-crystal-ball"></i> Price Predictions
            </h5>
            <div>
              <select class="form-select form-select-sm" id="modelSelect" onchange="updatePredictions()">
                <option value="NeuralNetwork">Neural Network</option>
                <option value="RandomForest">Random Forest</option>
                <option value="GradientBoosting">Gradient Boosting</option>
              </select>
            </div>
          </div>
          <div class="card-body">
            <div class="row" id="predictionsContainer">
              <!-- Predictions will be loaded here -->
            </div>
            <div class="mt-3">
              <small class="text-muted">
                <i class="fas fa-info-circle"></i>
                Predictions are based on historical data and technical indicators.
                Cryptocurrency markets are highly volatile and unpredictable.
              </small>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Charts Section -->
    <div class="row mb-4">
      <div class="col-lg-8">
        <div class="card">
          <div class="card-header">
            <h5 class="mb-0">
              <i class="fas fa-chart-line"></i> Price Chart (7 Days)
            </h5>
          </div>
          <div class="card-body">
            <canvas id="priceChart" height="400"></canvas>
          </div>
        </div>
      </div>
      <div class="col-lg-4">
        <div class="card">
          <div class="card-header">
            <h5 class="mb-0">
              <i class="fas fa-chart-bar"></i> Model Performance
            </h5>
          </div>
          <div class="card-body">
            <canvas id="performanceChart" height="400"></canvas>
          </div>
        </div>
      </div>
    </div>

    <!-- Model Information Section -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h5 class="mb-0">
              <i class="fas fa-robot"></i> Model Information
            </h5>
          </div>
          <div class="card-body">
            <div class="row" id="modelInfoContainer">
              <!-- Model info will be loaded here -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Loading Modal -->
  <div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog modal-sm modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-body text-center">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
          <p class="mt-2 mb-0">Loading predictions...</p>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
  <script src="/static/js/main.js"></script>
</body>

</html>